// 参考网关实例列表页面的样式结构
.aigw-monitor-dashboard-page {
  height: 100%;
  box-sizing: border-box;
  flex-grow: 1;
  flex-wrap: nowrap;
  position: relative;
  overflow: auto;
  display: flex;
  flex-direction: column;

  .aigw-monitor-dashboard-page-header {
    display: flex;
    flex-direction: column;
    width: inherit;

    .page-header {
      width: inherit;
      background: #FFFFFF;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title { // 页面标题
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #151B26;
        line-height: 24px;
        font-weight: 500;
      }
    }
  }

  .aigw-monitor-dashboard-page-content {
    width: inherit !important;
    padding: 24px !important;
    background-color: #fff !important;
    flex-grow: 1 !important;
    margin: 16px !important;
    border-radius: 6px !important;
  }
}

.controlPanel {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}


.formItem {
  margin-bottom: 16px !important;

  &:last-child {
    margin-bottom: 0 !important;
  }
}

.instanceGroup {
  display: flex;
  align-items: center;
}

.controlRow {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.controlLabel {
  width: 80px;
  font-weight: 500;
  color: #333;
  margin-right: 16px;
  flex-shrink: 0;
}

.controlGroup {
  display: flex;
  align-items: center;
  flex: 1;
}

.timeFilterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.timeRangeButtons {
  :global(.acud-radio-group) {
    display: flex;
    gap: -1px; // 按钮之间无间隙，边框重叠
  }

  :global(.acud-radio-button-wrapper) {
    border-radius: 0;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    &:hover {
      z-index: 1;
    }

    &.acud-radio-button-wrapper-checked {
      background-color: #E6F0FF;
      border-color: #2468F2;
      color: #2468F2;
      z-index: 2;
    }
  }
}

.customTimePicker {
  min-width: 320px;
}

.chartsContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  min-width: 800px; // 确保有足够空间显示2列

  // 固定2列布局，不让任何图表占据整行
}

.chartWrapper {
  background: #fff;
  border-radius: 8px;
  border: #E8E9EB 1px solid;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// 移除响应式设计，固定保持2列布局

// 控制面板响应式
@media (max-width: 768px) {
  .controlRow {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .controlLabel {
    margin-bottom: 8px;
    margin-right: 0;
  }
  
  .controlGroup {
    width: 100%;
    flex-wrap: wrap;
    gap: 8px;
  }
}
