# 根据服务来源查询服务信息接口

## 接口基本信息

- **接口说明**：根据服务来源查询服务信息
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/service`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 | 限制 |
| --- | --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin | 长度为1-64位字符，只允许字母、数字、中划线和下划线 |

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 | 限制 |
| --- | --- | --- | --- | --- | --- |
| serviceSource | String | 否 | 服务来源 | CCE | 当前仅支持"CCE"，不传返回所有来源服务 |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
[
  {
    "serviceName": "mnist-inference",
    "namespace": "default",
    "clusterId": "cce-wqeqccsc"
  },
  {
    "serviceName": "image-recognition",
    "namespace": "default",
    "clusterId": "cce-wqeqccsc"
  }
]
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| serviceName | String | 服务名称 |
| namespace | String | 命名空间 |
| clusterId | String | 集群ID |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查instanceId或serviceSource参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询服务的权限 |
| 404 | 实例不存在 | 确认instanceId是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/gw-ist9vvin/service?serviceSource=CCE
X-Region: gz
```

## 响应示例

```json
[
   "success": true,
    "status": 200,
    "result": [
  {
    "serviceName": "mnist-inference",
    "namespace": "default",
    "clusterId": "cce-wqeqccsc"
  },
  {
    "serviceName": "image-recognition",
    "namespace": "default",
    "clusterId": "cce-wqeqccsc"
  },
  {
    "serviceName": "object-detection",
    "namespace": "ai-services",
    "clusterId": "cce-wqeqccsc"
  }
    ]
]
```

## 注意事项

1. 如果未提供serviceSource参数，将返回所有来源的服务列表
2. 当前服务来源仅支持"CCE"类型
3. 返回的服务列表可能为空数组`[]`，表示该实例下没有来自指定来源的服务
4. 用户需要具有对应网关实例的查询权限才能获取服务列表
5. 接口返回结果不分页，包含所有符合条件的服务 