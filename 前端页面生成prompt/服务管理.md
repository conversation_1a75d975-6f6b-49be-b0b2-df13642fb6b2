1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、命名空间、路由配置、消费者列表），点击每个导航栏右侧会切换成当前导航栏下对应的页面。

2. 需求描述：现在，我期望在实例详情页面左侧导航栏中的实例详情导航栏下方新增一个导航栏【推理服务】，即在实例详情页面左侧会包含 5 个导航栏（实例详情、推理服务、命名空间、路由配置、消费者列表），然后点击推理服务这个导航栏后右侧页面会包含 2 个=标签页=（自有模型服务/第三方模型服务），点击不同的标签页切换至不同的内容，默认选中自有模型服务标签页，其中第三方模型服务这个标签页中以=空状态=展示，提示功能开发中。你可以参考实例详情页中点击实例详情导航后右侧页面的样式。我会在第 3 点中详细描述自有模型服务这个标签页中的交互和样式，你需要在遵循相关规范的前提下生成这个页面。【自有模型服务】这个标签页中涉及的所有接口请求、返回需要和接口文档中涉及的参数完全符合，这个标签页中会涉及到 8 个接口：1. 查询实例中服务列表 2. 查询服务详情 3. 查询实例详情 4. 查询实例关联的集群列表 5. 查询集群中命名空间列表 6. 查询集群中指定命名空间下服务列表 7. 添加服务 8. 移除服务，我会在第 4 点中分点描述这 8 个接口和页面交互的对应关系，你需要在遵循相关规范的前提下修改自有模型服务这个标签页中涉及模块相关的参数和字段。你需要按照接口文档中的对应字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。

3. 页面交互与样式：自有模型服务=标签页=下方首先有一个占满 100%宽度的=高亮提示文案=提示文案为："您可在自有模型服务中添加您自行部署的推理服务，从而通过网关对您的推理服务进行统一代理。若您需要添加来自 CCE 集群中的推理服务，请先前往网关实例详情-关联信息关联容器集群。"；然后在高亮提示文案下方有水平排列的 3 个元素： 1. 添加服务=按钮= 2. =搜索=框 3. 刷新=按钮=，添加服务按钮靠页面左侧，搜索框和刷新按钮靠页面右侧，其中搜索框可根据服务名称进行模糊搜索。在这 3 个元素下方有一个=列表=，用于展示当前实例中已添加的服务信息，列表中的内容在第 4 点中详细描述。点击添加服务按钮在页面右侧会出现一个=抽屉=，抽屉的标题为添加服务，抽屉内容为一个=表单=，我期望分成 2 个模块（基本信息/模型服务配置）以让结构更加清晰，基本信息模块中总共有 2 个字段（所属实例、所属 VPC），所属实例、所属 VPC 为静态回显；模型服务配置模块中总共有 4 个字段（服务来源/CCE 集群/命名空间/推理服务），服务来源右侧为=单选=，只有 1 个选项（CCE 容器引擎）且默认选中，CCE 集群为一个支持单选的=列表=，用于展示当前实例中已关联的集群列表，列表中具体参数参见第 4 点；命名空间是一个仅支持单选的=下拉列表选择=，用于展示选中集群中的所有命名空间；推理服务是一个支持多选的=下拉列表选择=，用于展示指定集群&&指定命名空间中的所有服务。点击抽屉中的确定=按钮=执行添加服务操作。

4. 页面与接口的对应关系：

    1. 用户点击标签页【自有模型服务】后看到的=列表=页面：总共包含 6 列（服务名称、服务状态、服务来源、关联路由数、创建时间、操作），这个列表页面列表支持=分页=，默认 10 条/页。其中操作列中包含 1 个操作：移除。服务名称、服务状态、服务来源、关联路由数、创建时间这 5 个参数来自于【查询实例中服务列表】这个接口中返回的字段，同时操作列中的移除操作需要根据【查询实例中服务列表】这个接口中返回的关联路由数进行判断是否可点击，如果某个服务的关联路由数大于 0，那么操作列中的移除按钮应该置灰，无法点击，同时当用户将鼠标移入“移除“时，需要一个=鼠标 hover 提示文案=，提示："当前服务已和路由关联，无法移除，请先删除相关路由规则后再移除服务。" 若移除按钮可点击，点击移除按钮后会二次提示用户是否需要移除当前服务，用户确认后调用【移除服务】接口根据当前的服务名执行移除服务操作。因此，在刚开始进入自有模型服务标签页的时候就需要调用【查询实例中服务列表】这个接口用于回显字段值，同时，当用户 1. 点击刷新按钮 2. 在搜索框中输入服务名称点击搜索时也需要重新调用查询实例中服务列表接口。另外，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。因为现在只有点击实例名称才会进入实例详情页，才能进入推理服务导航栏中的自有模型服务标签页。另外，当用户将鼠标 hover 在每一行中服务来源这一列的字段值时，需要调用【查询服务详情】这个接口，传当前这一行的服务名称以及当前实例 ID 获取服务详情信息，页面上需要一个=鼠标 hover 提示文案=，需要根据服务来源展示接口返回的不同字段值，如果服务来源为 CCE，在气泡提示文案中则需要回显查询服务详情接口中返回的集群 ID 和命名空间，这 2 个信息需要分行展示。
    2. 点击添加服务按钮会在页面右侧出现=抽屉=，抽屉中是一个=表单=，总共有 2 个模块（基本信息/模型服务配置），基本信息中包含 2 个字段（所属实例、所属 VPC）；模型服务配置中包含 4 个字段（服务来源/CCE 集群/命名空间/推理服务）。点击添加服务按钮后首先会请求【查询实例详情】这个接口（这个接口需要实例 id，可以在路径参数中查询到），用于在抽屉中回显所属实例/所属 VPC 这个字段的值（回显实例的名称和实例的 vpcId）；服务来源默认会选中 CCE 容器引擎，因此下方才会有 CCE 集群这个字段，然后会根据【查询实例详情】返回的当前实例的 id 去请求【查询实例关联的集群列表】这个接口，用于在抽屉中的 CCE 集群这个参数右侧=列表=回显当前实例关联的 CCE 集群列表，CCE 集群列表中包含 4 列（集群名称/ID、运行状态、VPC 网段、地域）。当用户选择 CCE 集群列表中某一行后，再点击命名空间这个下拉列表时，需要根据选中的集群 ID 去请求【查询集群中命名空间列表】这个接口，从而将所有命名空间回显在命名空间下拉列表中（如果用户没有选择 CCE 集群，则无法选择命名空间）。当用户选择了某个命名空间后，才可以点击推理服务这个下拉列表进行多选，否则下拉列表就禁用。当用户点击推理服务这个下拉列表时，需要调用【查询集群中指定命名空间下推理服务】这个接口，你需要根据选中的集群 ID、命名空间去请求接口，从而在推理服务这个下拉列表中返回所有服务，用户可以选择多个服务，注意推理服务如果多次点击下拉列表，已选中的内容不要清空。另外，如果用户在 CCE 集群列表中点击了不同的集群，命名空间和推理服务 2 个下拉列表都需要清空，如果没有选集群则命名空间禁用，如果没有选命名空间则推理服务禁用。
    3. 如果用户点击当前抽屉中的确定按钮就会请求【添加服务】这个接口，如果成功就返回自有模型服务标签页，并重新请求【查询实例中服务列表】这个接口。如果失败就在右上角最顶层 toast 提示具体的失败原因，抽屉不需要关闭。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：1. 查询实例中服务列表. md 2. 查询服务详情. md 3. 查询实例详情. md 4. 查询实例关联的集群列表. md 5. 查询集群中命名空间列表. md 6. 查询集群中指定命名空间下服务列表. md 7. 添加服务. md 8. 移除服务. md（参见附件）

7. 遵守原则：所有字段以接口文档为准，不要自动生成我没有提到的交互逻辑。

