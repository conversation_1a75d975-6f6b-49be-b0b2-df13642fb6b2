import React, { useState, useEffect, useCallback } from 'react';
import { Button, Modal, Select, Form, Radio, toast, Loading, DatePicker, DialogBox } from 'acud';
import { OutlinedRefresh } from 'acud-icon';
import { useRequest } from 'ahooks';
import moment, { Moment } from 'moment';
import locale from 'acud/es/date-picker/locale/zh_CN';

import { checkMonitorStatus, getCPromInstanceList, getCPromInstanceDetail, enableMonitoring, disableMonitoring, getRouteList, getServiceList, CPromInstance } from '@/apis/monitor';
import { createDisabledDateFunction, isTimeRangeExceedsRetention } from '@/utils/dateRangeUtils';
import { getRegistrationInstance } from '@/apis/instance';
import { useRegion } from '@baidu/bce-react-toolkit';
import AlertChart from './components/AlertChart';
import MultiSeriesChart from './components/MultiSeriesChart';
import ServiceStatus from './components/ServiceStatus';
import styles from './index.module.less';

const { Option } = Select;

// 查询配置接口
interface QueryConfig {
  name: string;
  query: string;
  color?: string;
}

// 监控面板配置接口
interface MonitorPanel {
  id: string;
  title: string;
  unit: string;
  description?: string; // 指标说明
  query?: string; // 单查询
  queries?: QueryConfig[]; // 多查询
}

// 时间范围选项
const TIME_RANGE_OPTIONS = [
  { label: '1小时', value: 'hour_1', duration: 1, unit: 'hour' },
  { label: '3小时', value: 'hour_3', duration: 3, unit: 'hour' },
  { label: '6小时', value: 'hour_6', duration: 6, unit: 'hour' },
  { label: '12小时', value: 'hour_12', duration: 12, unit: 'hour' },
  { label: '1天', value: 'day_1', duration: 1, unit: 'day' },
  { label: '3天', value: 'day_3', duration: 3, unit: 'day' },
  { label: '7天', value: 'day_7', duration: 7, unit: 'day' },
  { label: '14天', value: 'day_14', duration: 14, unit: 'day' },
  { label: '自定义', value: 'custom', duration: 0, unit: 'custom' }
];

// 监控维度选项
const MONITOR_DIMENSION_OPTIONS = [
  { label: '网关', value: 'gateway' },
  { label: '路由', value: 'route' },
  { label: '服务', value: 'service' }
];


// 网关监控面板配置
// 详细的指标配置文档请参考：docs/monitoring-metrics.md
const GATEWAY_MONITOR_PANELS: MonitorPanel[] = [
  // === 请求相关指标组 ===
  {
    id: 'success-rate',
    title: '请求成功率',
    unit: '%',
    description: '成功请求数占总请求数的百分比。正常范围：>95%，建议阈值：<90%时告警',
    query: `sum(rate(istio_requests_total{user_namespace="$namespace", response_code=~"2.."}[5m]))
    /
    sum(rate(istio_requests_total{user_namespace="$namespace"}[5m]))`
  },
  {
    id: 'request-success-count',
    title: '请求成功数',
    unit: '次',
    description: '在指定时间范围内成功处理的请求总数。用于监控网关处理能力和业务量趋势',
    query: `increase(istio_requests_total{
      reporter="source",
      response_code=~"2..",
      user_namespace="$namespace"
    }[5m])`
  },
  {
    id: 'qps',
    title: 'QPS',
    unit: 'req/s',
    description: '每秒处理的请求数量。反映网关的实时负载，建议根据业务峰值设置告警阈值',
    query: `sum(
      rate(istio_requests_total{
        user_namespace="$namespace"
      }[5m])
    )`
  },
  {
    id: 'response-time',
    title: '请求响应时间',
    unit: 'ms',
    description: '请求处理时延分布。P50为中位数，P90/P99为高分位数，Avg为平均值。建议P99<1000ms',
    queries: [
      {
        name: 'P50',
        query: `histogram_quantile(
          0.50,
          sum by(le) (
            rate(istio_request_duration_milliseconds_bucket{user_namespace="$namespace"}[5m])
          )
        )`,
        color: '#1890ff'
      },
      {
        name: 'P90',
        query: `histogram_quantile(
          0.90,
          sum by(le) (
            rate(istio_request_duration_milliseconds_bucket{user_namespace="$namespace"}[5m])
          )
        )`,
        color: '#52c41a'
      },
      {
        name: 'P99',
        query: `histogram_quantile(
          0.99,
          sum by(le) (
            rate(istio_request_duration_milliseconds_bucket{user_namespace="$namespace"}[5m])
          )
        )`,
        color: '#faad14'
      },
      {
        name: 'Avg',
        query: `sum (
          increase(istio_request_duration_milliseconds_sum{user_namespace="$namespace"}[5m])
        )
        /
        sum (
          increase(istio_request_duration_milliseconds_count{user_namespace="$namespace"}[5m])
        )`,
        color: '#f5222d'
      }
    ]
  },

  // === 流量相关指标组 ===
  {
    id: 'gateway-inbound-traffic',
    title: '网关入流量',
    unit: 'bytes/s',
    description: '网关接收的入站流量速率。用于监控网关的流量负载和带宽使用情况',
    query: `sum(rate(istio_request_bytes_sum{user_namespace="$namespace"}[5m]))`
  },
  {
    id: 'gateway-outbound-traffic',
    title: '网关出流量',
    unit: 'bytes/s',
    description: '网关发送的出站流量速率。与入流量配合监控网关的整体流量情况',
    query: `sum(rate(istio_response_bytes_sum{user_namespace="$namespace"}[5m]))`
  },
  {
    id: 'tcp-receive-bytes',
    title: 'TCP 接收数',
    unit: 'bytes/s',
    description: 'TCP层面的数据接收速率。用于监控底层网络连接的数据传输情况',
    query: `sum(irate(envoy_http_downstream_cx_rx_bytes_total{user_namespace="$namespace"}[5m]))`
  },
  {
    id: 'tcp-transmit-bytes',
    title: 'TCP 发送数',
    unit: 'bytes/s',
    description: 'TCP层面的数据发送速率。与TCP接收数配合监控网络传输性能',
    query: `sum(irate(envoy_http_downstream_cx_tx_bytes_total{user_namespace="$namespace"}[5m]))`
  },

  // === 连接相关指标组 ===
  {
    id: 'tcp-connections',
    title: 'TCP 连接数',
    unit: '个',
    description: '当前活跃的TCP连接数量。过高可能表示连接泄漏或负载过大，建议设置上限告警',
    query: `sum(envoy_cluster_upstream_cx_active{user_namespace="$namespace"})`
  },

  // === 状态码分布指标组 ===
  {
    id: 'gateway-status-code',
    title: '网关状态码分布',
    unit: 'req/s',
    description: '网关返回的HTTP状态码分布。2xx表示成功，4xx表示客户端错误，5xx表示服务端错误',
    query: `sum(
      rate(istio_requests_total{
        user_namespace="$namespace"
      }[5m])
    ) by (response_code)`
  },
  {
    id: 'backend-status-code',
    title: '后端服务状态码分布',
    unit: 'req/s',
    description: '后端服务返回的状态码分布，按服务名称分组。用于定位具体服务的错误情况',
    query: `sum(
  rate(istio_requests_total{
    user_namespace="$namespace",
    destination_service_name!="unknown"
  }[5m])
) by (response_code,destination_service_name)`
  },

  // === 服务访问统计指标组 ===
  {
    id: 'service-access-ranking',
    title: '服务访问量排行',
    unit: 'req/s',
    description: '各后端服务的访问量排行。用于了解业务热点和负载分布情况',
    query: `sum(rate(istio_requests_total{user_namespace="$namespace", destination_service_name!="unknown"}[5m]))
by (destination_service_name)`
  }
];

// 路由监控面板配置
// 详细的指标配置文档请参考：docs/monitoring-metrics.md
const ROUTE_MONITOR_PANELS: MonitorPanel[] = [
  // === Token基础消耗指标组 ===
  {
    id: 'input-token-count',
    title: '输入Token数',
    unit: 'tokens',
    description: '累计消耗的输入Token总数。用于监控模型输入的数据量和成本控制',
    query: `sum by (ai_route) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'output-token-count',
    title: '输出Token数',
    unit: 'tokens',
    description: '累计生成的输出Token总数。用于监控模型输出的数据量和响应长度',
    query: `sum by (ai_route) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'input-token-consumption-rate',
    title: '输入Token每秒消耗数',
    unit: 'tokens/s',
    description: '输入Token的实时消耗速率。用于监控模型调用频率和输入数据流量',
    query: `sum by (ai_route) (
      rate(route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}[5m])
    )`
  },
  {
    id: 'output-token-consumption-rate',
    title: '输出Token每秒消耗数',
    unit: 'tokens/s',
    description: '输出Token的实时生成速率。用于监控模型响应速度和输出数据流量',
    query: `sum by (ai_route) (
      rate(route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}[5m])
    )`
  },

  // === Token消费者统计指标组 ===
  {
    id: 'consumer-input-token-usage',
    title: '消费者Token使用统计（输入）',
    unit: 'tokens',
    description: '按消费者维度统计的输入Token使用量。用于分析不同消费者的使用模式和成本分摊',
    query: `sum by (ai_route, ai_consumer) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'consumer-output-token-usage',
    title: '消费者Token使用统计（输出）',
    unit: 'tokens',
    description: '按消费者维度统计的输出Token使用量。用于分析不同消费者的响应数据量',
    query: `sum by (ai_route, ai_consumer) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  },

  // === Token服务统计指标组 ===
  {
    id: 'service-input-token-usage',
    title: '服务Token使用统计（输入）',
    unit: 'tokens',
    description: '按服务维度统计的输入Token使用量。用于分析不同后端服务的Token消耗情况',
    query: `sum by (ai_route, ai_cluster) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'service-output-token-usage',
    title: '服务Token使用统计（输出）',
    unit: 'tokens',
    description: '按服务维度统计的输出Token使用量。用于分析不同后端服务的响应数据量',
    query: `sum by (ai_route, ai_cluster) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  },

  // === Token模型统计指标组 ===
  {
    id: 'model-input-token-usage',
    title: '模型Token使用统计（输入）',
    unit: 'tokens',
    description: '按模型维度统计的输入Token使用量。用于分析不同AI模型的使用情况和性能对比',
    query: `sum by (ai_route, ai_model) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'model-output-token-usage',
    title: '模型Token使用统计（输出）',
    unit: 'tokens',
    description: '按模型维度统计的输出Token使用量。用于分析不同AI模型的输出特征和效率',
    query: `sum by (ai_route, ai_model) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  }
];

// 服务监控面板配置
// 详细的指标配置文档请参考：docs/monitoring-metrics.md
const SERVICE_MONITOR_PANELS: MonitorPanel[] = [
  // === 请求相关指标组 ===
  {
    id: 'request-success-count',
    title: '请求成功数',
    unit: '次',
    description: '按服务统计的成功请求数量。用于监控各服务的处理能力和业务量分布',
    query: `sum by (destination_service_name) (
      increase(istio_requests_total{
        reporter="source",
        response_code=~"2..",
        user_namespace="$namespace",
        destination_service_name!="unknown"
      }[5m])
    )`
  },
  {
    id: 'request-success-rate',
    title: '请求成功率',
    unit: '%',
    description: '按服务统计的请求成功率。用于监控各服务的稳定性和错误率，建议>95%',
    query: `sum by (destination_service_name) (
      rate(istio_requests_total{
        reporter="source",
        response_code=~"2..",
        user_namespace="$namespace",
        destination_service_name!="unknown"
      }[5m])
    )
    /
    sum by (destination_service_name) (
      rate(istio_requests_total{
        reporter="source",
        user_namespace="$namespace",
        destination_service_name!="unknown"
      }[5m])
    )`
  },
  {
    id: 'qps-service',
    title: '每秒请求数QPS',
    unit: '次/秒',
    description: '按服务统计的每秒请求数。用于监控各服务的实时负载和流量分布',
    query: `sum by(destination_service_name) (
      rate(istio_requests_total{
        user_namespace="$namespace",
        destination_service_name!="unknown"
      }[5m])
    )`
  },
  {
    id: 'response-time-service',
    title: '请求响应时间',
    unit: 'ms',
    description: '按服务统计的请求处理时延分布。P50为中位数，P90/P99为高分位数，Avg为平均值。建议P99<1000ms',
    queries: [
      {
        name: 'P50',
        query: `histogram_quantile(
          0.50,
          sum by(destination_service_name, le) (
            rate(istio_request_duration_milliseconds_bucket{user_namespace="$namespace", destination_service_name!="unknown"}[5m])
          )
        )`,
        color: '#1890ff'
      },
      {
        name: 'P90',
        query: `histogram_quantile(
          0.90,
          sum by(destination_service_name, le) (
            rate(istio_request_duration_milliseconds_bucket{user_namespace="$namespace", destination_service_name!="unknown"}[5m])
          )
        )`,
        color: '#52c41a'
      },
      {
        name: 'P99',
        query: `histogram_quantile(
          0.99,
          sum by(destination_service_name, le) (
            rate(istio_request_duration_milliseconds_bucket{user_namespace="$namespace", destination_service_name!="unknown"}[5m])
          )
        )`,
        color: '#faad14'
      },
      {
        name: 'Avg',
        query: `sum by(destination_service_name) (
          increase(istio_request_duration_milliseconds_sum{user_namespace="$namespace", destination_service_name!="unknown"}[5m])
        )
        /
        sum by(destination_service_name) (
          increase(istio_request_duration_milliseconds_count{user_namespace="$namespace", destination_service_name!="unknown"}[5m])
        )`,
        color: '#f5222d'
      }
    ]
  },

  // === 流量相关指标组 ===
  {
    id: 'inbound-traffic',
    title: '入站流量',
    unit: 'bytes/s',
    description: '按服务统计的入站流量速率。用于监控各服务的数据接收量和带宽使用',
    query: `sum by (destination_service_name) (
      rate(istio_request_bytes_sum{
        user_namespace="$namespace",
        destination_service_name!="unknown"
      }[5m])
    )`
  },
  {
    id: 'outbound-traffic',
    title: '出站流量',
    unit: 'bytes/s',
    description: '按服务统计的出站流量速率。用于监控各服务的数据发送量和响应大小',
    query: `sum by (destination_service_name) (
      rate(istio_response_bytes_sum{
        user_namespace="$namespace",
        destination_service_name!="unknown"
      }[5m])
    )`
  },

  // === Token基础消耗指标组 ===
  {
    id: 'input-token-consumption-total-service',
    title: '输入Token消耗总数',
    unit: 'tokens',
    description: '按服务统计的输入Token累计消耗量。用于监控各服务的AI模型调用成本',
    query: `sum by (ai_cluster) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'output-token-consumption-total-service',
    title: '输出Token消耗总数',
    unit: 'tokens',
    description: '按服务统计的输出Token累计生成量。用于监控各服务的AI模型响应数据量',
    query: `sum by (ai_cluster) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'input-token-consumption-rate-service',
    title: '输入Token每秒消耗数',
    unit: 'tokens/s',
    description: '按服务统计的输入Token实时消耗速率。用于监控各服务的AI调用频率',
    query: `sum by(ai_cluster) (
      rate(route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}[5m])
    )`
  },
  {
    id: 'output-token-consumption-rate-service',
    title: '输出Token每秒消耗数',
    unit: 'tokens/s',
    description: '按服务统计的输出Token实时生成速率。用于监控各服务的AI响应速度',
    query: `sum by(ai_cluster) (
      rate(route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}[5m])
    )`
  },

  // === 连接相关指标组 ===
  {
    id: 'tcp-connections-service',
    title: 'TCP连接数',
    unit: '个',
    description: '按业务服务统计的TCP连接数。用于监控各业务服务的连接池使用情况和连接泄漏',
    query: `sum by (cluster_name) (
      envoy_cluster_upstream_cx_active{
        user_namespace="$namespace",
        cluster_name=~"^outbound.*",
        cluster_name!~".*redis.*"
      }
    )`
  },

  // === Token消费者统计指标组 ===
  {
    id: 'consumer-input-token-usage-service',
    title: '消费者Token使用统计（输入）',
    unit: 'tokens',
    description: '按服务和消费者维度统计的输入Token使用量。用于分析不同消费者在各服务上的使用情况',
    query: `sum by (ai_cluster, ai_consumer) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'consumer-output-token-usage-service',
    title: '消费者Token使用统计（输出）',
    unit: 'tokens',
    description: '按服务和消费者维度统计的输出Token使用量。用于分析不同消费者的响应数据量分布',
    query: `sum by (ai_cluster, ai_consumer) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  },

  // === Token路由统计指标组 ===
  {
    id: 'service-input-token-usage-service',
    title: '路由Token使用统计（输入）',
    unit: 'tokens',
    description: '按路由和服务维度统计的输入Token使用量。用于分析路由到服务的Token消耗分布',
    query: `sum by (ai_route, ai_cluster) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'service-output-token-usage-service',
    title: '路由Token使用统计（输出）',
    unit: 'tokens',
    description: '按路由和服务维度统计的输出Token使用量。用于分析路由到服务的响应数据量分布',
    query: `sum by (ai_route, ai_cluster) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  },

  // === Token模型统计指标组 ===
  {
    id: 'model-input-token-usage-service',
    title: '模型Token使用统计（输入）',
    unit: 'tokens',
    description: '按服务和模型维度统计的输入Token使用量。用于分析各服务使用不同AI模型的情况',
    query: `sum by (ai_cluster, ai_model) (
      route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
    )`
  },
  {
    id: 'model-output-token-usage-service',
    title: '模型Token使用统计（输出）',
    unit: 'tokens',
    description: '按服务和模型维度统计的输出Token使用量。用于分析不同AI模型在各服务上的响应特征',
    query: `sum by (ai_cluster, ai_model) (
      route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"}
    )`
  }
];

interface MonitorAlertProps {
  instanceId: string;
}

const MonitorAlert: React.FC<MonitorAlertProps> = ({ instanceId }) => {
  const { region: globalRegion } = useRegion();

  // 状态管理
  const [monitorEnabled, setMonitorEnabled] = useState<boolean>(false);
  const [allowQueries, setAllowQueries] = useState<boolean>(false); // 新增：控制是否允许查询监控接口
  const [cpromInstanceId, setCpromInstanceId] = useState<string>(''); // CProm实例ID
  const [retentionPeriod, setRetentionPeriod] = useState<string>(''); // 存储时长
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('hour_1'); // 默认1小时
  const [selectedDimension, setSelectedDimension] = useState<string>('gateway'); // 默认网关
  const [selectedRoute, setSelectedRoute] = useState<string>('全部'); // 默认全部路由
  const [selectedService, setSelectedService] = useState<string>('全部'); // 默认全部服务
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // 全局图表模式状态管理
  const [globalChartMode, setGlobalChartMode] = useState<'line' | 'area'>(() => {
    const saved = localStorage.getItem('global-chart-mode');
    return (saved as 'line' | 'area') || 'area';
  });

  // 全局图表模式切换函数
  const updateGlobalChartMode = useCallback((mode: 'line' | 'area') => {
    setGlobalChartMode(mode);
    localStorage.setItem('global-chart-mode', mode);

    // 同步更新所有面板的图表模式
    const allPanels = [...GATEWAY_MONITOR_PANELS, ...ROUTE_MONITOR_PANELS, ...SERVICE_MONITOR_PANELS];
    allPanels.forEach(panel => {
      localStorage.setItem(`chart-mode-${panel.title}`, mode);
    });

    // 触发自定义事件通知所有组件更新
    window.dispatchEvent(new Event('chartModeChange'));
  }, []);

  // 实例信息状态
  const [instanceRegion, setInstanceRegion] = useState<string>('');

  // 获取实际的region，优先使用实例的region，否则使用全局region
  const region = instanceRegion || globalRegion;

  // 获取实例详情以获取实际的region
  const { run: fetchInstanceDetail } = useRequest(
    () => getRegistrationInstance(instanceId),
    {
      ready: !!instanceId,
      onSuccess: (res) => {
        if (res?.result?.region) {
          setInstanceRegion(res.result.region);
        }
      },
      onError: (error) => {
        console.error('获取实例详情失败:', error);
        // 如果获取失败，使用全局region作为fallback
        setInstanceRegion(globalRegion);
      }
    }
  );

  // 下拉列表数据状态
  const [routeList, setRouteList] = useState<Array<{value: string, label: string}>>([]);
  const [serviceList, setServiceList] = useState<Array<{value: string, label: string}>>([]);
  const [routeLoading, setRouteLoading] = useState<boolean>(false);
  const [serviceLoading, setServiceLoading] = useState<boolean>(false);

  // 关闭服务弹窗状态
  const [closeServiceModalVisible, setCloseServiceModalVisible] = useState<boolean>(false);
  const [closeServiceLoading, setCloseServiceLoading] = useState<boolean>(false);

  // 自定义时间范围状态
  const [customTimeRange, setCustomTimeRange] = useState<[Moment, Moment] | null>(null);
  
  // 弹窗状态
  const [enableModalVisible, setEnableModalVisible] = useState<boolean>(false);
  const [selectedCPromInstance, setSelectedCPromInstance] = useState<string>('');
  const [cpromInstanceList, setCpromInstanceList] = useState<CPromInstance[]>([]);
  
  // 检查CProm实例状态
  const { run: checkCPromInstanceStatus } = useRequest(
    (cpromId: string) => getCPromInstanceDetail(cpromId, region),
    {
      manual: true,
      onSuccess: (res) => {
        console.log('检查CProm实例状态成功:', res);
        if (res?.success && res.result) {
          const instanceStatus = res.result.status?.phase;
          // 只有当实例状态为Running时才允许查询监控数据
          const isRunning = instanceStatus === 'Running';
          setAllowQueries(isRunning);

          if (!isRunning) {
            console.log('CProm实例状态非Running，跳过监控数据查询:', instanceStatus);
          }
        } else {
          // 如果获取实例状态失败，默认不允许查询
          setAllowQueries(false);
        }
      },
      onError: (error) => {
        console.error('检查CProm实例状态失败:', error);
        // 如果检查失败，默认不允许查询
        setAllowQueries(false);
      }
    }
  );

  // 检查监控状态
  const { loading: statusLoading, run: checkStatus } = useRequest(
    () => checkMonitorStatus(instanceId, region),
    {
      manual: true,
      onSuccess: (res) => {
        console.log('检查监控状态成功:', res);
        if (res?.success && res.result) {
          const enabled = res.result.enabled;
          const cpromId = res.result.cpromInstanceId;
          setMonitorEnabled(enabled);
          setCpromInstanceId(cpromId || '');

          // 如果监控已开启且有CProm实例ID，检查实例状态
          if (enabled && cpromId) {
            checkCPromInstanceStatus(cpromId);
          } else {
            setAllowQueries(enabled);
          }
        }
      },
      onError: (error) => {
        console.error('检查监控状态失败:', error);
        toast.error({
          message: '检查监控状态失败',
          duration: 3
        });
      }
    }
  );
  
  // 获取CProm实例列表
  const { loading: cpromListLoading, run: fetchCPromList } = useRequest(
    () => getCPromInstanceList(region),
    {
      manual: true,
      onSuccess: (res) => {
        console.log('获取CProm实例列表成功:', res);
        if (res?.success && Array.isArray(res.result)) {
          // 只显示运行中的实例
          const runningInstances = res.result.filter(
            (instance: any) => instance.status?.phase === 'Running' && instance.status?.ready === true
          );
          setCpromInstanceList(runningInstances);
        }
      },
      onError: (error) => {
        console.error('获取CProm实例列表失败:', error);
        toast.error({
          message: '获取CProm实例列表失败',
          duration: 3
        });
      }
    }
  );
  
  // 开启监控
  const { loading: enableLoading, run: enableMonitor } = useRequest(
    (cpromId: string) => enableMonitoring(instanceId, cpromId, region),
    {
      manual: true,
      onSuccess: (res) => {
        console.log('开启监控成功:', res);
        if (res?.success) {
          toast.success({
            message: '已开启监控服务',
            duration: 5
          });
          setEnableModalVisible(false);
          setSelectedCPromInstance('');
          // 刷新页面状态
          checkStatus();
        }
      },
      onError: (error) => {
        console.error('开启监控失败:', error);
        toast.error({
          message: '开启监控失败',
          duration: 3
        });
      }
    }
  );
  
  // 处理存储时长变化
  const handleRetentionPeriodChange = useCallback((newRetentionPeriod: string) => {
    console.log('存储时长变化:', newRetentionPeriod);
    setRetentionPeriod(newRetentionPeriod);

    // 检查当前选择的时间范围是否超出存储限制
    if (isTimeRangeExceedsRetention(selectedTimeRange, newRetentionPeriod)) {
      console.log('当前时间范围超出存储限制，切换到1小时');
      setSelectedTimeRange('hour_1'); // 切换到最小的时间范围
    }
  }, [selectedTimeRange]);

  // 组件加载时检查监控状态
  useEffect(() => {
    if (instanceId && region) {
      checkStatus();
    }
  }, [instanceId, region]);
  
  // 打开开启监控弹窗
  const handleOpenEnableModal = useCallback(() => {
    setSelectedCPromInstance('');
    fetchCPromList();
    setEnableModalVisible(true);
  }, [fetchCPromList]);
  
  // 确认开启监控
  const handleConfirmEnable = useCallback(() => {
    if (!selectedCPromInstance) {
      toast.error({
        message: '请选择CProm实例',
        duration: 3
      });
      return;
    }
    enableMonitor(selectedCPromInstance);
  }, [selectedCPromInstance, enableMonitor]);
  
  // 刷新CProm实例列表
  const handleRefreshCPromList = useCallback(() => {
    fetchCPromList();
  }, [fetchCPromList]);
  
  // 计算时间范围
  const getTimeRange = useCallback(() => {
    // 如果选择了自定义时间范围且有有效的自定义时间
    if (selectedTimeRange === 'custom' && customTimeRange && customTimeRange.length === 2) {
      return {
        start: customTimeRange[0].valueOf(),
        end: customTimeRange[1].valueOf()
      };
    }

    const now = Date.now();
    const selectedOption = TIME_RANGE_OPTIONS.find(option => option.value === selectedTimeRange);

    let startTime: number;
    if (selectedOption?.unit === 'hour') {
      startTime = now - selectedOption.duration * 60 * 60 * 1000;
    } else if (selectedOption?.unit === 'day') {
      startTime = now - selectedOption.duration * 24 * 60 * 60 * 1000;
    } else {
      // 默认为1小时
      startTime = now - 60 * 60 * 1000;
    }

    return {
      start: startTime,
      end: now
    };
  }, [selectedTimeRange, customTimeRange]);
  
  // 当选择预设时间时，同步更新日期选择器
  useEffect(() => {
    if (selectedTimeRange !== 'custom') {
      const now = moment();
      const selectedOption = TIME_RANGE_OPTIONS.find(option => option.value === selectedTimeRange);

      let startTime: moment.Moment;
      if (selectedOption?.unit === 'hour') {
        startTime = moment().subtract(selectedOption.duration, 'hours');
      } else if (selectedOption?.unit === 'day') {
        startTime = moment().subtract(selectedOption.duration, 'days');
      } else {
        // 默认为1小时
        startTime = moment().subtract(1, 'hour');
      }

      setCustomTimeRange([startTime, now]);
    }
  }, [selectedTimeRange]);

  // 生成namespace
  const namespace = instanceId ? `istio-system-${instanceId}` : '';

  // API调用函数
  const fetchRouteList = useCallback(async () => {
    if (!instanceId || !region) return;

    setRouteLoading(true);
    try {
      const response = await getRouteList(instanceId, region);

      if (response.success && response.page?.result) {
        const routes = response.page.result.map((route: any) => ({
          value: route.routeName,
          label: route.routeName
        }));
        setRouteList([{ value: '全部', label: '全部' }, ...routes]);
      } else {
        console.error('获取路由列表失败:', response.error);
        toast.error({ message: '获取路由列表失败', duration: 3 });
      }
    } catch (error) {
      console.error('获取路由列表异常:', error);
      toast.error({ message: '获取路由列表失败', duration: 3 });
    } finally {
      setRouteLoading(false);
    }
  }, [instanceId, region]);

  const fetchServiceList = useCallback(async () => {
    if (!instanceId || !region) return;

    setServiceLoading(true);
    try {
      const response = await getServiceList(instanceId, region);

      if (response.success && response.page?.result) {
        const services = response.page.result.map((service: any) => ({
          value: service.serviceName,
          label: service.serviceName
        }));
        setServiceList([{ value: '全部', label: '全部' }, ...services]);
      } else {
        console.error('获取服务列表失败:', response.error);
        toast.error({ message: '获取服务列表失败', duration: 3 });
      }
    } catch (error) {
      console.error('获取服务列表异常:', error);
      toast.error({ message: '获取服务列表失败', duration: 3 });
    } finally {
      setServiceLoading(false);
    }
  }, [instanceId, region]);

  // 关闭监控服务
  const handleCloseService = useCallback(async () => {
    if (!instanceId || !region) {
      console.error('缺少必要参数:', { instanceId, region });
      return;
    }

    setCloseServiceLoading(true);
    // 立即禁止查询，防止在关闭过程中触发API调用
    setAllowQueries(false);

    try {
      console.log('开始关闭监控服务:', { instanceId, region });
      const response = await disableMonitoring(instanceId, region);

      if (response.success) {
        console.log('关闭监控服务成功:', response);
        toast.success({
          message: response.result?.message || '监控服务已成功关闭',
          duration: 5
        });

        // 关闭弹窗
        setCloseServiceModalVisible(false);

        // 重新检查监控状态，不调用其他接口
        console.log('重新检查监控状态...');
        checkStatus();

      } else {
        // 根据接口文档，错误信息在 error.message 中
        console.error('关闭监控服务失败:', response.error);
        const errorMessage = response.error?.message || '关闭监控服务失败';
        toast.error({
          message: errorMessage,
          duration: 5
        });
        // 失败时恢复查询状态，弹窗不消失
        setAllowQueries(monitorEnabled);
      }
    } catch (error: any) {
      console.error('关闭监控服务异常:', error);
      // 显示具体的错误信息
      const errorMessage = error?.response?.data?.error?.message ||
                          error?.message ||
                          '关闭监控服务失败';
      toast.error({
        message: errorMessage,
        duration: 5
      });
      // 异常时恢复查询状态，弹窗不消失
      setAllowQueries(monitorEnabled);
    } finally {
      setCloseServiceLoading(false);
    }
  }, [instanceId, region, checkStatus]);

  // 维度切换时重置选择状态
  const handleDimensionChange = useCallback((dimension: string) => {
    setSelectedDimension(dimension);
    setSelectedRoute('全部');
    setSelectedService('全部');
    // 清空列表数据，下次打开时重新获取
    if (dimension !== 'route') {
      setRouteList([]);
    }
    if (dimension !== 'service') {
      setServiceList([]);
    }
  }, []);

  // 处理查询语句过滤
  const processQuery = useCallback((query: string) => {
    let processedQuery = query.replace(/\$namespace/g, namespace);

    // 路由维度过滤
    if (selectedDimension === 'route' && selectedRoute !== '全部') {
      // 对于新的Token数指标查询语句，添加ai_route过滤
      if (query.includes('sum by (ai_route)') &&
          (query.includes('metric_input_token') || query.includes('metric_output_token'))) {
        // 将 sum by (ai_route) 改为 sum() 并添加路由过滤
        processedQuery = processedQuery.replace('sum by (ai_route)', 'sum');
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",ai_route="${selectedRoute}"`
        );
      }
      // 对于包含多个标签的查询（消费者、服务、模型统计）
      else if (query.includes('sum by (ai_route, ai_consumer)') ||
               query.includes('sum by (ai_route, ai_cluster)') ||
               query.includes('sum by (ai_route, ai_model)')) {
        // 添加ai_route过滤
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",ai_route="${selectedRoute}"`
        );
      }
      // 对于Token消耗数/s查询
      else if (query.includes('rate(route_upstream_model_consumer_metric_')) {
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",ai_route="${selectedRoute}"`
        );
      }
    }

    // 服务维度过滤
    if (selectedDimension === 'service' && selectedService !== '全部') {
      // 对于请求成功数指标，需要特殊处理：切换到按destination_address分组并添加destination_service_name过滤
      if (query.includes('sum by (destination_service_name)') && query.includes('increase(istio_requests_total')) {
        // 将查询从按destination_service_name分组切换到按destination_address分组
        processedQuery = processedQuery.replace('sum by (destination_service_name)', 'sum by (destination_address)');
        // 添加destination_service_name模糊匹配过滤条件（因为实际服务名包含完整域名）
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",destination_service_name=~".*${selectedService}.*"`
        );
      }
      // 对于请求成功率指标，需要特殊处理：切换到按destination_address分组并添加destination_service_name过滤
      else if (query.includes('sum by (destination_service_name)') && query.includes('rate(istio_requests_total')) {
        // 将查询从按destination_service_name分组切换到按destination_address分组
        processedQuery = processedQuery.replace(/sum by \(destination_service_name\)/g, 'sum by (destination_address)');
        // 添加destination_service_name模糊匹配过滤条件（因为实际服务名包含完整域名）
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",destination_service_name=~".*${selectedService}.*"`
        );
      }
      // 对于入站流量指标，需要特殊处理：切换到按destination_address分组并添加destination_service_name过滤
      else if (query.includes('sum by (destination_service_name)') && query.includes('rate(istio_request_bytes_sum')) {
        // 将查询从按destination_service_name分组切换到按destination_address分组
        processedQuery = processedQuery.replace('sum by (destination_service_name)', 'sum by (destination_address)');
        // 添加destination_service_name模糊匹配过滤条件（因为实际服务名包含完整域名）
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",destination_service_name=~".*${selectedService}.*"`
        );
      }
      // 对于出站流量指标，需要特殊处理：切换到按destination_address分组并添加destination_service_name过滤
      else if (query.includes('sum by (destination_service_name)') && query.includes('rate(istio_response_bytes_sum')) {
        // 将查询从按destination_service_name分组切换到按destination_address分组
        processedQuery = processedQuery.replace('sum by (destination_service_name)', 'sum by (destination_address)');
        // 添加destination_service_name模糊匹配过滤条件（因为实际服务名包含完整域名）
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",destination_service_name=~".*${selectedService}.*"`
        );
      }
      // 对于请求平均时延指标，需要特殊处理：切换到按destination_address分组并添加destination_service_name过滤
      else if (query.includes('sum by(destination_service_name)') && query.includes('increase(istio_request_duration_milliseconds')) {
        // 将查询从按destination_service_name分组切换到按destination_address分组
        processedQuery = processedQuery.replace(/sum by\(destination_service_name\)/g, 'sum by(destination_address)');
        // 添加destination_service_name模糊匹配过滤条件（因为实际服务名包含完整域名）
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",destination_service_name=~".*${selectedService}.*"`
        );
      }
      // 对于每秒请求数QPS指标，需要特殊处理：切换到按destination_address分组并添加destination_service_name过滤
      else if (query.includes('sum by(destination_service_name)') && query.includes('rate(istio_requests_total')) {
        // 将查询从按destination_service_name分组切换到按destination_address分组
        processedQuery = processedQuery.replace('sum by(destination_service_name)', 'sum by(destination_address)');
        // 添加destination_service_name模糊匹配过滤条件（因为实际服务名包含完整域名）
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",destination_service_name=~".*${selectedService}.*"`
        );
      }
      // 对于其他包含destination_service_name或destination_address的查询，添加服务过滤
      else if (query.includes('destination_service_name') || query.includes('destination_address')) {
        // 在现有过滤条件基础上添加服务过滤
        if (query.includes('destination_service_name')) {
          processedQuery = processedQuery.replace(
            /user_namespace="[^"]*"/g,
            `user_namespace="${namespace}",destination_service_name=~".*${selectedService}.*"`
          );
        }
        if (query.includes('destination_address')) {
          processedQuery = processedQuery.replace(
            /user_namespace="[^"]*"/g,
            `user_namespace="${namespace}",destination_address=~".*${selectedService}.*"`
          );
        }
      }

      // 对于包含多个标签的查询（服务、消费者、模型统计）
      if (query.includes('sum by (ai_route, ai_cluster)') ||
          query.includes('sum by (ai_cluster, ai_consumer)') ||
          query.includes('sum by (ai_cluster, ai_model)')) {
        // 添加ai_cluster过滤（在服务维度中，ai_cluster对应服务名）
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",ai_cluster=~".*${selectedService}.*"`
        );
      }
      // 对于Token相关查询，添加cluster过滤
      else if (query.includes('sum by (cluster)') || query.includes('sum by (consumer,cluster)') ||
          query.includes('sum by (model,cluster)')) {
        const serviceFilter = `{cluster="${selectedService}"}`;
        processedQuery = processedQuery.replace(/\)[3m:]\)/g, `)[3m:])${serviceFilter}`);
      }
      // 对于新的Token每秒消耗数查询，添加ai_cluster过滤
      else if (query.includes('sum by(ai_cluster)') && query.includes('rate(route_upstream_model_consumer_metric_')) {
        processedQuery = processedQuery.replace(
          /user_namespace="[^"]*"/g,
          `user_namespace="${namespace}",ai_cluster=~".*${selectedService}.*"`
        );
      }
    }

    // 添加调试信息，特别关注TCP连接数查询
    if (query.includes('envoy_cluster_upstream_cx_active')) {
      console.log('🔍 TCP连接数查询处理:', {
        原始查询: query,
        处理后查询: processedQuery,
        selectedDimension,
        selectedService,
        namespace
      });
    }

    return processedQuery;
  }, [namespace, selectedDimension, selectedRoute, selectedService]);
  
  // 如果正在加载状态
  if (statusLoading) {
    return (
      <div className="aigw-detail-content-wrap">
        <div className="aigw-detail-content-title">监控与告警</div>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <Loading />
        </div>
      </div>
    );
  }
  
  // 如果未开启监控，显示空态页面
  if (!monitorEnabled) {
    return (
      <div className="aigw-detail-content-wrap">
        <div className="aigw-detail-content-title">监控与告警</div>
        <div className={styles.emptyContainer}>
          <div className={styles.emptyContent}>
            <div className={styles.emptyContentInner}>
              <div className={styles.emptyTextSection}>
                <div className={styles.emptyTitle}>开启 Prometheus 监控服务</div>
                <div className={styles.emptyDescription}>
                  Prometheus 监控服务，是针对云原生场景提供的全托管、高可用、可扩展的 Prometheus 监控服务。完全对接开源 Prometheus 生态，支持多维度指标数据采集，支持灵活查询语句 PromQL，基于 Grafana 提供开箱即用的监控 Dashboard。
                </div>
                <div className={styles.emptySubDescription}>
                  网关需要使用 Prometheus 监控服务进行指标的收集、分析与展示。
                  <span style={{ color: '#ff9325' }}><span style={{fontWeight:'bold'}}>注意：</span>开启后会产生费用，网关产生指标均为收费指标，具体参考</span> <a href='https://cloud.baidu.com/doc/CProm/s/Gl0gcsh46' target="_blank">Prometheus 监控计费详情</a>
                </div>
              </div>
              <Button
                type="primary"
                onClick={handleOpenEnableModal}
                className={styles.emptyButton}
              >
                立即开启
              </Button>
            </div>
          </div>
        </div>
        
        {/* 开启监控弹窗 */}
        <Modal
          title="开启监控服务"
          visible={enableModalVisible}
          onCancel={() => setEnableModalVisible(false)}
          onOk={handleConfirmEnable}
          confirmLoading={enableLoading}
          width={520}
        >
          <Form layout="horizontal" labelWidth={80} >
            <Form.Item label="CProm 实例：" required extra={
            <span>建议使用存储时长大于 15 天的 CProm。<a target="_blank" href="https://console.bce.baidu.com/cprom/#/create">去创建 CProm</a></span>
            }>
              <div style={{ display: 'flex', gap: '4px' }}>
                <Select
                  value={selectedCPromInstance}
                  onChange={setSelectedCPromInstance}
                  placeholder="请选择 Prometheus 监控实例"
                  style={{ width: '320px' }}
                  loading={cpromListLoading}
                >
                  {cpromInstanceList.map((instance) => (
                    <Option key={instance.instanceId} value={instance.instanceId}>
                      {instance.instanceName} ({instance.instanceId})
                    </Option>
                  ))}
                </Select>
                <Button 
                  icon={<OutlinedRefresh />} 
                  onClick={handleRefreshCPromList}
                  loading={cpromListLoading}
                />
              </div>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  }
  
  // 已开启监控，显示监控页面
  return (
    <div className="aigw-detail-content-wrap">
      <div className="aigw-detail-content-title" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <span>监控与告警</span>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ServiceStatus
            cpromInstanceId={cpromInstanceId}
            region={region}
            onRetentionPeriodChange={handleRetentionPeriodChange}
          />
          <Button
            onClick={() => setCloseServiceModalVisible(true)}
          >
            关闭服务
          </Button>
        </div>
      </div>
      
      {/* 控制面板 */}
      <div className={styles.controlPanel}>
        {/* 时间筛选 */}
        <div className={styles.controlRow}>
          <span className={styles.controlLabel}>时间筛选：</span>
          <div className={styles.timeRangeGroup}>
            <div className={styles.timeControlsLeft}>
              <Radio.Group
                value={selectedTimeRange}
                onChange={(e) => {
                  if (e.target && 'value' in e.target) {
                    setSelectedTimeRange(e.target.value as string);
                  }
                }}
                className={styles.timeRangeButtons}
              >
                {TIME_RANGE_OPTIONS.map(option => {
                  // 检查该时间范围是否超出存储限制
                  const isDisabled = retentionPeriod ? isTimeRangeExceedsRetention(option.value, retentionPeriod) : false;
                  return (
                    <Radio.Button
                      key={option.value}
                      value={option.value}
                      disabled={isDisabled}
                    >
                      {option.label}
                    </Radio.Button>
                  );
                })}
              </Radio.Group>
              <DatePicker.RangePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder={['开始时间', '结束时间']}
                value={customTimeRange}
                onChange={(dates) => {
                  setCustomTimeRange(dates as [Moment, Moment] | null);
                  // 当直接修改日期选择器时，自动选中"自定义"选项
                  if (dates && dates.length === 2) {
                    setSelectedTimeRange('custom');
                  }
                }}
                disabledDate={createDisabledDateFunction(retentionPeriod)}
                className={styles.customDatePicker}
                locale={locale}
              />
            </div>
            <Button
              icon={<OutlinedRefresh />}
              onClick={() => {
                if (allowQueries) {
                  setRefreshTrigger(prev => prev + 1);
                }
              }}
              disabled={!allowQueries}
              className={styles.refreshButton}
            />
          </div>
        </div>
        
        {/* 监控维度 */}
        <div className={styles.controlRow}>
          <span className={styles.controlLabel}>监控维度：</span>
          <Radio.Group
            value={selectedDimension}
            onChange={(e) => {
              if (e.target && 'value' in e.target) {
                handleDimensionChange(e.target.value as string);
              }
            }}
            className={styles.dimensionButtons}
          >
            {MONITOR_DIMENSION_OPTIONS.map(option => (
              <Radio.Button key={option.value} value={option.value}>
                {option.label}
              </Radio.Button>
            ))}
          </Radio.Group>

          {/* 全局图表模式切换 */}
          <div className={styles.globalChartModeControl}>
            <div className={styles.chartModeToggle}>
              <Button
                type={globalChartMode === 'line' ? 'primary' : 'default'}
                size="small"
                onClick={() => updateGlobalChartMode('line')}
                className={`${styles.chartModeButton} ${styles.leftButton}`}
              >
                折线图
              </Button>
              <Button
                type={globalChartMode === 'area' ? 'primary' : 'default'}
                size="small"
                onClick={() => updateGlobalChartMode('area')}
                className={`${styles.chartModeButton} ${styles.rightButton}`}
              >
                面积图
              </Button>
            </div>
          </div>
        </div>
        
        {/* 路由维度筛选条件 */}
        {selectedDimension === 'route' && (
          <div className={styles.filterRow}>
            <div className={styles.filterGroup}>
              <span className={styles.filterLabel}>路由：</span>
              <Select
                value={selectedRoute}
                onChange={setSelectedRoute}
                style={{ width: 200 }}
                loading={routeLoading}
                onDropdownVisibleChange={(open) => {
                  if (open && routeList.length <= 1) {
                    fetchRouteList();
                  }
                }}
              >
                {routeList.map(route => (
                  <Option key={route.value} value={route.value}>
                    {route.label}
                  </Option>
                ))}
              </Select>
            </div>
          </div>
        )}

        {/* 服务维度筛选条件 */}
        {selectedDimension === 'service' && (
          <div className={styles.filterRow}>
            <div className={styles.filterGroup}>
              <span className={styles.filterLabel}>服务：</span>
              <Select
                value={selectedService}
                onChange={setSelectedService}
                style={{ width: 200 }}
                loading={serviceLoading}
                onDropdownVisibleChange={(open) => {
                  if (open && serviceList.length <= 1) {
                    fetchServiceList();
                  }
                }}
              >
                {serviceList.map(service => (
                  <Option key={service.value} value={service.value}>
                    {service.label}
                  </Option>
                ))}
              </Select>
            </div>
          </div>
        )}
      </div>
      
      {/* 监控图表 */}
      {selectedDimension === 'gateway' && (
        <div className={styles.chartsContainer}>
          {GATEWAY_MONITOR_PANELS.map((panel) => (
            <div key={panel.id} className={styles.chartWrapper}>
              {/* 对于多系列数据图表，使用专门的多系列图表组件 */}
              {(panel.title.includes('状态码') || panel.title.includes('排行')) ? (
                <MultiSeriesChart
                  title={panel.title}
                  unit={panel.unit}
                  description={panel.description}
                  query={panel.query ? panel.query.replace(/\$namespace/g, namespace) : ''}
                  instanceId={instanceId}
                  timeRange={getTimeRange()}
                  refreshTrigger={`${selectedTimeRange}-${refreshTrigger}`}
                  region={region}
                  metricType="business"
                  allowQueries={allowQueries}
                />
              ) : (
                <AlertChart
                  title={panel.title}
                  unit={panel.unit}
                  description={panel.description}
                  query={panel.query ? panel.query.replace(/\$namespace/g, namespace) : undefined}
                  queries={panel.queries ? panel.queries.map((q: QueryConfig) => ({
                    ...q,
                    query: q.query.replace(/\$namespace/g, namespace)
                  })) : undefined}
                  instanceId={instanceId}
                  timeRange={getTimeRange()}
                  refreshTrigger={`${selectedTimeRange}-${refreshTrigger}`}
                  region={region}
                  metricType="business" // 明确指定为业务指标
                  allowQueries={allowQueries}
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* 路由维度监控图表 */}
      {selectedDimension === 'route' && (
        <div className={styles.chartsContainer}>
          {ROUTE_MONITOR_PANELS.map((panel) => (
            <div key={panel.id} className={styles.chartWrapper}>
              {/* 对于多系列数据图表，使用专门的多系列图表组件 */}
              {(panel.title.includes('统计') ||
                (panel.title.includes('Token') && selectedRoute === '全部')) ? (
                <MultiSeriesChart
                  title={panel.title}
                  unit={panel.unit}
                  description={panel.description}
                  query={panel.query ? processQuery(panel.query) : ''}
                  instanceId={instanceId}
                  timeRange={getTimeRange()}
                  refreshTrigger={`${selectedTimeRange}-${selectedRoute}-${refreshTrigger}`}
                  region={region}
                  metricType="business"
                  allowQueries={allowQueries}
                />
              ) : (
                <AlertChart
                  title={panel.title}
                  unit={panel.unit}
                  description={panel.description}
                  query={panel.query ? processQuery(panel.query) : undefined}
                  queries={panel.queries ? panel.queries.map((q: QueryConfig) => ({
                    ...q,
                    query: processQuery(q.query)
                  })) : undefined}
                  instanceId={instanceId}
                  timeRange={getTimeRange()}
                  refreshTrigger={`${selectedTimeRange}-${selectedRoute}-${refreshTrigger}`}
                  region={region}
                  metricType="business" // 明确指定为业务指标
                  allowQueries={allowQueries}
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* 服务维度监控图表 */}
      {selectedDimension === 'service' && (
        <div className={styles.chartsContainer}>
          {SERVICE_MONITOR_PANELS.map((panel) => (
            <div key={panel.id} className={styles.chartWrapper}>
              {/* 对于多系列数据图表，使用专门的多系列图表组件 */}
              {(panel.title.includes('统计') || panel.title.includes('流量') ||
                panel.title.includes('请求成功数') || panel.title.includes('请求成功率') ||
                panel.title.includes('入站流量') || panel.title.includes('出站流量') ||
                panel.title.includes('请求平均时延') || panel.title.includes('每秒请求数') ||
                panel.title.includes('TCP连接数') || panel.title.includes('Token每秒消耗数') ||
                panel.title.includes('Token消耗总数')) ? (
                <MultiSeriesChart
                  title={panel.title}
                  unit={panel.unit}
                  description={panel.description}
                  query={panel.query ? processQuery(panel.query) : ''}
                  instanceId={instanceId}
                  timeRange={getTimeRange()}
                  refreshTrigger={`${selectedTimeRange}-${selectedService}-${refreshTrigger}`}
                  region={region}
                  metricType="business"
                  allowQueries={allowQueries}
                />
              ) : (
                <AlertChart
                  title={panel.title}
                  unit={panel.unit}
                  description={panel.description}
                  query={panel.query ? processQuery(panel.query) : undefined}
                  queries={panel.queries ? panel.queries.map((q: QueryConfig) => ({
                    ...q,
                    query: processQuery(q.query)
                  })) : undefined}
                  instanceId={instanceId}
                  timeRange={getTimeRange()}
                  refreshTrigger={`${selectedTimeRange}-${selectedService}-${refreshTrigger}`}
                  region={region}
                  metricType="business" // 明确指定为业务指标
                  allowQueries={allowQueries}
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* 关闭监控服务确认弹窗 */}
      <DialogBox
        visible={closeServiceModalVisible}
        title="关闭监控服务"
        content={
          <>
            确定要关闭监控服务吗？关闭后网关将不再为您采集相关指标，并停止向 CProm 实例推送指标数据。
          </>
        }
        onCancel={() => setCloseServiceModalVisible(false)}
        onOk={() => {
          handleCloseService();
        }}
        confirmLoading={closeServiceLoading}
      />
    </div>
  );
};

export default MonitorAlert;
