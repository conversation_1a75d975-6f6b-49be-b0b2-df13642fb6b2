# 查询消费者列表接口

## 接口基本信息

- **接口说明**：查询指定实例下的消费者列表
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/consumers`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| instanceId | String | 是 | 实例ID |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 | 1 |
| pageSize | Integer | 否 | 每页条数，默认为10 | 10 |
| orderBy | String | 否 | 排序字段，默认为createTime | createTime |
| order | String | 否 | 排序方式，可选值：asc（升序）、desc（降序），默认为desc | desc |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 2,
        "result": [
            {
                "consumerId": "consumer-001",
                "consumerName": "test-consumer",
                "description": "测试用消费者",
                "createTime": "2023-06-10 09:15:30",
                "unlimitedQuota": true,
                "totalQuota": -1,
                "quotaValue": -1
            },
            {
                "consumerId": "consumer-002",
                "consumerName": "limited-consumer",
                "description": "-",
                "createTime": "2023-06-12 14:30:25",
                "unlimitedQuota": false,
                "totalQuota": 5000,
                "quotaValue": 2500
            }
        ]
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| page | Object | 分页信息和结果 |
| page.orderBy | String | 排序字段，基于createTime排序 |
| page.order | String | 排序方式，desc表示倒序，asc表示顺序 |
| page.pageNo | Integer | 当前页码 |
| page.pageSize | Integer | 每页条数 |
| page.totalCount | Integer | 总数据条数 |
| page.result | Array | 消费者列表数据 |
| page.result[].consumerId | String | 消费者ID |
| page.result[].consumerName | String | 消费者名称 |
| page.result[].description | String | 消费者描述，若无则显示"-" |
| page.result[].createTime | String | 消费者创建时间，格式：YYYY-MM-DD HH:mm:ss |
| page.result[].unlimitedQuota | Boolean | 是否开启不限额，true表示不限额，false表示限额 |
| page.result[].totalQuota | Integer | 总配额值，若开启不限额则返回-1，否则返回用户设置的总配额数值 |
| page.result[].quotaValue | Integer | 剩余配额值，若开启不限额则返回-1，否则返回当前剩余的配额数值 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/i-a1b2c3d4/consumers?pageNo=1&pageSize=10&orderBy=createTime&order=desc
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 2,
        "result": [
            {
                "consumerId": "consumer-001",
                "consumerName": "test-consumer",
                "description": "测试用消费者",
                "createTime": "2023-06-10 09:15:30",
                "unlimitedQuota": true,
                "totalQuota": -1,
                "quotaValue": -1
            },
            {
                "consumerId": "consumer-002",
                "consumerName": "limited-consumer",
                "description": "-",
                "createTime": "2023-06-12 14:30:25",
                "unlimitedQuota": false,
                "totalQuota": 5000,
                "quotaValue": 2500
            }
        ]
    }
}
``` 