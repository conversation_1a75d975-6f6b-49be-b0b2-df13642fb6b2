import React, {FC, useCallback, useEffect, useMemo, useState} from 'react';
import {
  Button,
  Form,
  Input,
  Loading,
  Modal,
  Radio,
  Select,
  Table,
  toast,
  Upload
} from 'acud';
import {OutlinedButtonUpload, OutlinedPlusNew} from 'acud-icon';
import cx from 'classnames';

import {
  createConfigGroup,
  editConfigGroup,
  getConfigGroupList,
  importConfigGroup
} from '@/apis/configManage';
import {getRegistrationNamespace} from '@/apis/instance';
import EllipsisWrap from '@/components/Ellipsis';

import styles from './index.module.less';

interface CreateModalProps {
  instanceId: string;
  refresh(): void;
  isEdit?: boolean;
  editData?: any;
}

export const CreateModal: FC<CreateModalProps> = ({
  refresh,
  instanceId,
  isEdit,
  editData
}) => {
  const [namespaceList, setNamespaceList] = useState<any>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [formDataInstante] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initLoading, setInitLoading] = useState(false);
  const [selectNamespace, setNamespace] = useState('');
  const [nameSpaceGroup, setGroup] = useState<any>([]);

  const _getConfigGroupList = useCallback(
    (namespace) => {
      return getConfigGroupList({
        instanceId: instanceId,
        pageNo: 1,
        pageSize: 1000,
        namespace: namespace || selectNamespace
      }).then((res: any) => {
        setGroup(res?.result?.configGroups || []);
      });
    },
    [selectNamespace, instanceId]
  );

  const getList = useCallback(() => {
    setInitLoading(true);
    return getRegistrationNamespace({
      instanceId: instanceId,
      pageNo: 1,
      pageSize: 10000
    })
      .then((res: any) => {
        setNamespaceList(
          res?.result?.result?.map((d) => {
            return {
              label: d.name,
              value: d.name
            };
          }) || []
        );
      })
      .finally(() => {
        setInitLoading(false);
      });
  }, [instanceId]);

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    if (!isEdit) {
      getList();
    }
  }, [getList]);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
    formDataInstante.setFieldsValue({
      name: '',
      comment: '',
      namespace: ''
    });
  }, [formDataInstante]);

  const onClickOK = useCallback(
    async (e: React.MouseEvent<HTMLElement>) => {
      e.preventDefault();
      setLoading(true);
      try {
        await formDataInstante.validateFields();
        const formData = formDataInstante.getFieldsValue();
        const res = isEdit
          ? await editConfigGroup({
              instanceId: instanceId,
              ...formData
            })
          : await createConfigGroup({
              instanceId: instanceId,
              ...formData
            });
        if (res?.success) {
          toast.success({
            message: isEdit ? '配置分组编辑成功' : '配置分组已创建',
            duration: 5
          });
          refresh();
          handleCancel();
        }
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    },
    [formDataInstante, refresh, instanceId, handleCancel]
  );

  const creatRules = useMemo(() => {
    return {
      namespace: [
        {
          required: true,
          message: '命名空间不能为空'
        }
      ],
      name: [
        {
          required: true,
          message: '请输入配置分组名称'
        },
        {
          max: 128,
          message: '不可超过128个字符'
        },
        {
          validator: (rule: any, value: string) => {
            if (value && !/^[a-zA-Z0-9._-]+$/.test(value)) {
              return Promise.reject(new Error('配置分组名称不符合规则'));
            }
            if (nameSpaceGroup.find((d) => d.name === value)) {
              return Promise.reject(new Error('配置分组名称已存在'));
            }
            return Promise.resolve();
          }
        }
      ],
      comment: [
        {
          max: 200,
          message: '不可超过200个字符'
        }
      ]
    };
  }, [nameSpaceGroup]);

  useEffect(() => {
    const formData = formDataInstante.getFieldsValue();
    if (formData.name) {
      formDataInstante.validateFields(['name']);
    }
  }, [nameSpaceGroup]);

  useEffect(() => {
    if (isEdit && editData && isModalVisible) {
      formDataInstante.setFieldsValue({
        namespace: editData.namespace,
        name: editData.name,
        comment: editData.comment
      });
    }

    if (namespaceList.length) {
      formDataInstante.setFieldsValue({
        namespace: namespaceList[0].value
      });
      setNamespace(namespaceList[0].value);
      _getConfigGroupList(namespaceList[0].value);
    }
  }, [namespaceList, isEdit, editData, isModalVisible]);
  return (
    <>
      {isEdit ? (
        <Button type="actiontext" onClick={showModal}>
          编辑
        </Button>
      ) : (
        <Button type="primary" icon={<OutlinedPlusNew />} onClick={showModal}>
          新建
        </Button>
      )}
      <Modal
        title={`${isEdit ? '编辑' : '创建'}配置分组`}
        maskClosable={false}
        onOk={onClickOK}
        onCancel={handleCancel}
        visible={isModalVisible}
        confirmLoading={loading}
        className="mse-create-modal"
      >
        <Form
          name="formData"
          labelAlign="left"
          form={formDataInstante}
          labelWidth="100px"
        >
          {isEdit ? (
            <Form.Item label="命名空间" name="namespace">
              <EllipsisWrap data={editData.namespace} width={340} />
            </Form.Item>
          ) : (
            <Form.Item
              rules={creatRules.namespace}
              label="命名空间"
              name="namespace"
            >
              <Select
                options={namespaceList}
                placeholder="请选择命名空间"
                loading={initLoading}
                disabled={isEdit}
                onChange={(val: string) => {
                  setNamespace(val);
                  _getConfigGroupList(val);
                }}
                style={{width: 344}}
              />
            </Form.Item>
          )}

          {isEdit ? (
            <Form.Item label="配置分组名称" name="name">
              <EllipsisWrap data={editData.name} width={340} />
            </Form.Item>
          ) : (
            <Form.Item
              rules={creatRules.name}
              label="配置分组名称"
              name="name"
              extra="仅支持英文字母、数字、'.'、'-'、'_',不超过128个字符"
              tooltip="配置分组是一组配置文件的集合，您可将一个应用或微服务对应一个配置分组。创建后配置分组的名称不可更改，请谨慎输入。"
            >
              <Input disabled={isEdit} />
            </Form.Item>
          )}

          <Form.Item
            rules={creatRules.comment}
            name="comment"
            label="描述"
            extra="配置分组备注，不超过200个字符"
          >
            <Input.TextArea limitLength={200} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

interface ImportModalProps {
  instanceId: string;
  refresh(): void;
}

export const ImportModal: FC<ImportModalProps> = ({instanceId, refresh}) => {
  const [formDataInstante] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [namespaceList, setNamespaceList] = useState<any>([]);
  const [initLoading, setInitLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [result, setResult] = useState<any>({});

  const getList = useCallback(() => {
    setInitLoading(true);
    return getRegistrationNamespace({
      instanceId: instanceId,
      pageNo: 1,
      pageSize: 10000
    })
      .then((res: any) => {
        setNamespaceList(
          res?.result?.result?.map((d) => {
            return {
              label: d.name,
              value: d.name
            };
          }) || []
        );
      })
      .finally(() => {
        setInitLoading(false);
      });
  }, [instanceId]);

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    getList();
  }, [instanceId, getList]);

  const normFile = (e) => {
    return e?.file?.originFileObj;
  };

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
    formDataInstante.resetFields();
    if (showResult) {
      refresh();
    }
    setShowResult(false);
  }, [formDataInstante, refresh, showResult]);

  const onClickOK = useCallback(
    async (e: React.MouseEvent<HTMLElement>) => {
      e.preventDefault();
      if (showResult) {
        handleCancel();
        return;
      }
      try {
        await formDataInstante.validateFields();
        const formData = formDataInstante.getFieldsValue();
        setLoading(true);
        const res = await importConfigGroup({
          instanceId: instanceId,
          ...formData
        });
        if (res?.success) {
          setShowResult(true);
          setResult(res?.result || {});
        } else {
          Modal.confirm({
            title: '导入失败',
            content: '未读取到合法数据，请检查导入的数据文件。'
          });
        }
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    },
    [formDataInstante, refresh, instanceId, handleCancel, showResult]
  );

  const resultColumns = [
    {
      key: 'group',
      title: '配置分组',
      dataIndex: 'group',
      width: '50%',
      render: (val) => {
        return <EllipsisWrap data={val} width={210} />;
      }
    },
    {
      key: 'name',
      title: '配置文件名',
      dataIndex: 'name',
      width: '50%',
      render: (val) => {
        return <EllipsisWrap data={val} width={210} />;
      }
    }
  ];

  const renderResult = useMemo(() => {
    return (
      <div className={styles['import-result-wrap']}>
        {result?.createConfigFiles?.length ? (
          <div className={styles['import-result-item']}>
            <div className={styles['import-result-title']}>
              新增配置文件数：{result?.createConfigFiles?.length}
            </div>
            <Table
              columns={resultColumns}
              dataSource={result?.createConfigFiles}
              pagination={false}
            />
          </div>
        ) : (
          <></>
        )}
        {result?.overwriteConfigFiles?.length ? (
          <div className={styles['import-result-item']}>
            <div className={styles['import-result-title']}>
              覆盖配置文件数：{result?.overwriteConfigFiles?.length}
            </div>
            <Table
              columns={resultColumns}
              dataSource={result?.overwriteConfigFiles}
              pagination={false}
            />
          </div>
        ) : (
          <></>
        )}
        {result?.skipConfigFiles?.length ? (
          <div className={styles['import-result-item']}>
            <div className={styles['import-result-title']}>
              跳过配置文件数：{result?.skipConfigFiles?.length}
            </div>
            <Table
              columns={resultColumns}
              dataSource={result?.skipConfigFiles}
              pagination={false}
            />
          </div>
        ) : (
          <></>
        )}
      </div>
    );
  }, [result]);

  useEffect(() => {
    if (namespaceList.length) {
      formDataInstante.setFieldsValue({
        namespace: namespaceList[0].value
      });
    }
  }, [namespaceList]);
  return (
    <>
      <Button onClick={showModal}>导入</Button>
      {isModalVisible && (
        <Modal
          title={showResult ? '导入完成' : '导入配置'}
          maskClosable={false}
          onOk={onClickOK}
          onCancel={handleCancel}
          visible={isModalVisible}
          destroyOnClose={true}
          className={cx('mse-create-modal', styles['mse-import-modal'])}
          footer={null}
        >
          {loading ? (
            <Loading size="small" loading={true} tip="正在导入..." />
          ) : (
            <>
              {showResult ? (
                renderResult
              ) : (
                <>
                  <Form
                    name="formData"
                    labelAlign="left"
                    form={formDataInstante}
                    initialValues={{
                      conflictHandling: 'skip'
                    }}
                  >
                    <>
                      <Form.Item
                        rules={[
                          {
                            required: true,
                            message: '命名空间不能为空'
                          }
                        ]}
                        label="命名空间"
                        name="namespace"
                      >
                        <Select
                          options={namespaceList}
                          placeholder="请选择命名空间"
                          loading={initLoading}
                          style={{width: 385}}
                        />
                      </Form.Item>
                      <Form.Item
                        rules={[
                          {
                            required: true,
                            message: '请添加上传文件'
                          }
                        ]}
                        label="上传文件"
                        name="zip"
                        getValueFromEvent={normFile}
                        extra={
                          <div>
                            请上传zip格式文件，大小10M以内。
                            <div>
                              文件格式需符合
                              <a
                                href="https://cloud.baidu.com/doc/MSE/s/Em4m2r9gm"
                                target="_blank"
                                rel="noreferrer"
                              >
                                导入配置
                              </a>
                              要求。
                            </div>
                          </div>
                        }
                      >
                        <Upload
                          name="zip"
                          accept=".zip"
                          maxCount={1}
                          action="https://yapi.baidu-int.com/mock/23714/upload"
                        >
                          <Button icon={<OutlinedButtonUpload />}>
                            上传文件
                          </Button>
                        </Upload>
                      </Form.Item>
                      <Form.Item
                        name="conflictHandling"
                        label="冲突处理"
                        required={true}
                      >
                        <Radio.Group>
                          <Radio value="skip">跳过</Radio>
                          <Radio value="overwrite">覆盖</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </>
                  </Form>
                </>
              )}
              <div className={styles['import-dialog-footer']}>
                <Button onClick={handleCancel}>取消</Button>
                <Button type="primary" onClick={onClickOK}>
                  确定
                </Button>
              </div>
            </>
          )}
        </Modal>
      )}
    </>
  );
};
