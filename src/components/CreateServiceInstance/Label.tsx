import React, {
  forwardRef,
  Ref,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState
} from 'react';
import {Button, Input, Tag} from 'acud';
import {OutlinedPlusNew} from 'acud-icon';
import _ from 'lodash';

import c from './index.module.less';

export interface LabelItemType {
  label: string;
  value: string;
  errorMsg?: string | null;
}

function Lebel(
  props: {
    value?: LabelItemType[];
    onChange?: (list: Array<Omit<LabelItemType, 'errorMsg'>>) => void;
    isView?: boolean;
    width?: number;
  },
  ref:
    | Ref<{
        validate: () => Promise<any>;
      }>
    | undefined
) {
  const {value, onChange, isView, width = 500} = props;
  const [list, setList] = useState<LabelItemType[]>(
    (value?.length && value) || [{label: '', value: '', errorMsg: null}]
  );
  useEffect(() => {
    if (isView && value && value?.length) {
      setList(value);
    }
  }, [value, isView]);
  // 校验
  const validate = useCallback(
    async (canEmpty: boolean = true) => {
      const newlist = _.cloneDeep(list);
      newlist.forEach((item, index) => {
        item.errorMsg = '';
        const repeatTargetIndex = newlist.findIndex(
          (e, i) => e.label === item.label && index !== i
        );
        if (repeatTargetIndex > -1) {
          item.errorMsg = item.label ? '重复标签名称' : '请输入标签名称';
          newlist[repeatTargetIndex].errorMsg = item.label
            ? '重复标签名称'
            : '请输入标签名称';
        }
        if (canEmpty) {
          if (newlist.length > 1 && !item.label) {
            item.errorMsg = '请输入标签名称';
          }
        } else if (!item.label) {
          item.errorMsg = '请输入标签名称';
        }
      });
      setList(newlist);
      changeList(newlist);
      const errorItem = newlist.find((e) => e.errorMsg);
      return errorItem?.errorMsg
        ? Promise.reject(errorItem.errorMsg)
        : Promise.resolve();
    },
    [list]
  );
  const addItem = async () => {
    await validate(false);
    if (!list.find((e) => !e.label)) {
      setList([...list, {label: '', value: ''}]);
      changeList([...list, {label: '', value: ''}]);
    }
  };
  const deleteItem = (index: number) => {
    const newList = list.filter((e, i) => i !== index);
    setList(newList);
    changeList(newList);
  };
  const setKV = (type: 'label' | 'value', index: number, value: string) => {
    const newList = _.cloneDeep(list);
    newList[index][type] = value?.trim();
    setList(newList);
    changeList(newList);
  };

  // 传给父组件
  // useEffect(() => {
  //   onChange &&
  //     onChange(
  //       list.map((e) => ({
  //         label: e.label,
  //         value: e.value
  //       }))
  //     );
  // }, [onChange, list]);

  const changeList = useCallback(
    (list) => {
      onChange &&
        onChange(
          list.map((e) => ({
            label: e.label,
            value: e.value
          }))
        );
    },
    [onChange, list]
  );

  useImperativeHandle(
    ref,
    () => ({
      validate: () => {
        return validate();
      }
    }),
    [validate]
  );
  if (isView) {
    if (!value || !value.length) {
      return '-';
    }
    return (
      <div className={c['label-view-wrap']} style={{width: width}}>
        {value.map((d) => {
          return <Tag>{`${d.label}：${d.value}`}</Tag>;
        })}
      </div>
    );
  }
  return (
    <div className={c['label-wrap']} style={{width: width}}>
      <div className={c['label']}>
        {list.map((item, index) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <div key={index} className={c['label-item']}>
              <div className={c['label-item-content']}>
                <div>
                  <span>标签键：</span>
                  <div>
                    <Input
                      placeholder="请输入标签名称"
                      value={item.label}
                      onChange={(e: any) =>
                        setKV('label', index, e.target?.value)
                      }
                    />
                  </div>
                </div>
                <div>
                  <span>值：</span>
                  <div>
                    <Input
                      placeholder="请输入标签值"
                      value={item.value}
                      onChange={(e: any) =>
                        setKV('value', index, e.target?.value)
                      }
                    />
                  </div>
                </div>
                <Button
                  type="actiontext"
                  style={{
                    marginLeft: 10,
                    position: 'absolute',
                    right: -55
                  }}
                  onClick={() => deleteItem(index)}
                >
                  删除
                </Button>
              </div>
              {item.errorMsg && (
                <div className={c['error-msg']}>{item.errorMsg}</div>
              )}
            </div>
          );
        })}

        <Button type="actiontext" icon={<OutlinedPlusNew />} onClick={addItem}>
          添加
        </Button>
      </div>
    </div>
  );
}

export default forwardRef(Lebel);
