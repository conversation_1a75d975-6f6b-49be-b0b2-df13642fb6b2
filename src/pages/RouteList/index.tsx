import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Table, Space, Input, Popconfirm, toast, Empty, Tag, Modal, Alert, Tooltip } from 'acud';
import { OutlinedRefresh, OutlinedSearch, OutlinedCopy, OutlinedPlusNew } from 'acud-icon';
import { useRequest } from 'ahooks';
import { getQueryParams, useRegion } from '@baidu/bce-react-toolkit';

import { getRouteList, RouteType, deleteRoute } from '@/apis/route';
import urls from '@/utils/urls';
import styles from './index.module.less';

// 自定义组件：只在文本溢出时显示Tooltip
const EllipsisTooltip: React.FC<{ 
  title: string; 
  children: React.ReactNode; 
  className?: string;
  style?: React.CSSProperties;
}> = ({ title, children, className, style }) => {
  const [isOverflowing, setIsOverflowing] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        const isOverflow = textRef.current.scrollWidth > textRef.current.clientWidth;
        setIsOverflowing(isOverflow);
      }
    };

    checkOverflow();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [title]);

  const content = (
    <div 
      ref={textRef}
      className={className}
      style={{
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        ...style
      }}
    >
      {children}
    </div>
  );

  return isOverflowing ? (
    <Tooltip 
      title={title}
      mouseEnterDelay={0.5}
      getPopupContainer={() => document.body}
    >
      {content}
    </Tooltip>
  ) : content;
};

interface RouteListProps {
  detail: any;
  instanceId: string;
  requestDetail?: () => void;
}

const RouteList: React.FC<RouteListProps> = (props) => {
  const { instanceId } = props;
  const { region } = useRegion();
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState<string>('');
  const [pageParams, setPageParams] = useState({
    pageNo: 1,
    pageSize: 10,
    orderBy: 'createTime',
    order: 'desc'
  });
  const [totalCount, setTotalCount] = useState<number>(0);
  const [routeList, setRouteList] = useState<RouteType[]>([]);
  
  // 示例弹窗相关状态
  const [exampleVisible, setExampleVisible] = useState<boolean>(false);
  const [currentRoute, setCurrentRoute] = useState<RouteType | null>(null);

  // 检查instanceId
  useEffect(() => {
    console.log('RouteList组件接收的instanceId:', instanceId);
  }, [instanceId]);

  // 获取路由列表
  const { loading, run: fetchRouteList } = useRequest(
    () => {
      console.log('获取路由列表，实例ID:', instanceId);
      const params = {
        ...pageParams,
        routeName: searchValue || undefined
      };
      return getRouteList(instanceId, params, region);
    },
    {
      manual: true,
      onSuccess: (res) => {
        console.log('获取路由列表成功:', res);
        if (res?.page) {
          setTotalCount(res.page.totalCount);
          setRouteList(res.page.result || []);
        }
      },
      onError: (error) => {
        console.error('获取路由列表失败:', error);
        toast.error({
          message: '获取路由列表失败',
          duration: 3
        });
      }
    }
  );

  // 当instanceId变化时，获取列表
  useEffect(() => {
    if (instanceId) {
      fetchRouteList();
    }
  }, [instanceId]);

  // 当分页参数变化时，重新获取列表
  useEffect(() => {
    if (instanceId) {
      fetchRouteList();
    }
  }, [pageParams]);

  // 处理搜索
  const handleSearch = () => {
    console.log('搜索路由:', searchValue);
    setPageParams({
      ...pageParams,
      pageNo: 1 // 重置页码
    });
    // 由于设置pageParams是异步的，不会立即触发useEffect，应该手动调用fetchRouteList
    setTimeout(() => {
      fetchRouteList();
    }, 0);
  };

  // 处理搜索框变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  // 处理搜索框回车
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '路由名称',
      dataIndex: 'routeName',
      key: 'routeName',
      width: 180,
      fixed: 'left' as const,
      ellipsis: true,
      render: (routeName: string, record: RouteType) => (
        <Tooltip title={routeName}>
          <a onClick={() => handleRouteDetail(record)}>{routeName}</a>
        </Tooltip>
      )
    },
    {
      title: '路由状态',
      dataIndex: 'routeStatus',
      key: 'routeStatus',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          'PUBLISHED': { text: '已发布', className: 'status-success' },
          'UNPUBLISHED': { text: '未发布', className: 'status-inactive' },
          'PENDING': { text: '处理中', className: 'status-active' },
          'ERROR': { text: '错误', className: 'status-error' }
        };
        const currentStatus = statusMap[status] || { text: status || '未知', className: 'status-inactive' };
        return (
          <Tag 
            color="transparent" 
            icon={<span className={`circle ${currentStatus.className}`}></span>}
            style={{ paddingLeft: 0 }}
          >
            {currentStatus.text}
          </Tag>
        );
      }
    },
    {
      title: '匹配路径',
      key: 'matchPath',
      width: 200,
      ellipsis: true,
      render: (_, record: RouteType) => {
        const pathValue = record.matchPath.value;
        const matchTypeText = record.matchPath.matchType === 'prefix' ? '前缀匹配' : record.matchPath.matchType === 'exact' ? '精确匹配' : record.matchPath.matchType;
        
        return (
          <div className={styles.pathInfo}>
            <EllipsisTooltip title={pathValue}>
              {pathValue}
            </EllipsisTooltip>
            <div className={styles.matchType}>
              {matchTypeText}
            </div>
          </div>
        );
      }
    },
    {
      title: '目标服务',
      key: 'targetService',
      width: 220,
      ellipsis: true,
      render: (_, record: RouteType) => {
        // targetService 现在是数组格式
        const services = Array.isArray(record.targetService) ? record.targetService : [record.targetService];
        
        // 判断是单服务还是多服务
        if (services.length === 1) {
          // 单服务，保持原有展示格式
          const service = services[0];
          return (
            <Tooltip 
              title={
                <div>
                  <div>{service.serviceName}</div>
                  <div>命名空间: {service.namespace}</div>
                  <div>端口: {service.servicePort}</div>
                </div>
              }
            >
              <div className={styles.targetServiceInfo}>
                <div style={{ 
                  overflow: 'hidden', 
                  textOverflow: 'ellipsis', 
                  whiteSpace: 'nowrap' 
                }}>
                  {service.serviceName}
                </div>
                <div className={styles.namespace} style={{ 
                  overflow: 'hidden', 
                  textOverflow: 'ellipsis', 
                  whiteSpace: 'nowrap' 
                }}>
                  命名空间: {service.namespace}, 端口: {service.servicePort}
                </div>
              </div>
            </Tooltip>
          );
        } else {
          // 多服务，根据是否有 modelName 或 requestRatio 判断分发策略
          const hasModelName = services.some(s => s.modelName);
          const hasRequestRatio = services.some(s => s.requestRatio);
          
          const tooltipContent = (
            <div>
              {services.map((service, index) => (
                <div key={index} style={{ marginBottom: index < services.length - 1 ? '8px' : 0 }}>
                  <div>服务: {service.serviceName}</div>
                  <div>命名空间: {service.namespace}</div>
                  <div>端口: {service.servicePort}</div>
                  {hasRequestRatio && <div>请求比例: {service.requestRatio}%</div>}
                  {hasModelName && <div>模型名称: {service.modelName}</div>}
                </div>
              ))}
            </div>
          );
          
          return (
            <Tooltip title={tooltipContent}>
              <div className={styles.targetServiceInfo}>
                {services.map((service, index) => (
                  <div key={index} style={{ 
                    overflow: 'hidden', 
                    textOverflow: 'ellipsis', 
                    whiteSpace: 'nowrap',
                    marginBottom: index < services.length - 1 ? '4px' : 0
                  }}>
                    {hasModelName ? (
                      // 按模型名称分发：模型名称 -> 服务名称.命名空间
                      `${service.modelName} -> ${service.serviceName}.${service.namespace}`
                    ) : (
                      // 按请求比例分发：服务名称.命名空间（请求比例）
                      `${service.serviceName}.${service.namespace}(${service.requestRatio}%)`
                    )}
                  </div>
                ))}
                {services.length > 1 && (
                  <div className={styles.namespace} style={{ 
                    overflow: 'hidden', 
                    textOverflow: 'ellipsis', 
                    whiteSpace: 'nowrap',
                    color: '#999',
                    fontSize: '12px'
                  }}>
                    {hasModelName ? '按模型名称分发' : '按请求比例分发'}
                  </div>
                )}
              </div>
            </Tooltip>
          );
        }
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right' as const,
      render: (_, record: RouteType) => (
        <Space size="middle">
          <a onClick={() => handleShowExample(record)}>调用示例</a>
          <a onClick={() => handleEdit(record)}>编辑</a>
          <a onClick={() => {
            Modal.confirm({
              title: '确定要删除当前路由吗？',
              content: (
                <div>
                  路由删除后无法恢复，请谨慎操作！请确定是否要删除路由"{record.routeName}"？
                </div>
              ),
              okText: '确定',
              cancelText: '取消',
              width: 400,
              onOk: () => handleDelete(record)
            });
          }}>删除</a>
        </Space>
      )
    }
  ];

  // 显示调用示例
  const handleShowExample = (record: RouteType) => {
    console.log('显示调用示例:', record);
    setCurrentRoute(record);
    setExampleVisible(true);
  };
  
  // 复制调用示例代码
  const handleCopyExample = () => {
    const code = `curl http://${props.detail?.data?.externalIP || '{网关入口地址}'}/v1/chat/completions \\
-H "Content-Type: application/json" \\
-H "Authorization: Bearer $AUTH_INFO" \\
-d '{
  "model": "deepseek-r1-distill-qwen-32b",
  "messages": [{"role": "user", "content": "Say this is a test!"}],
  "temperature": 0.7,
  "stream": true
}'`;

    navigator.clipboard.writeText(code)
      .then(() => {
        toast.success({
          message: '代码已复制到剪贴板',
          duration: 3
        });
      })
      .catch(err => {
        console.error('复制失败:', err);
        toast.error({
          message: '复制失败',
          duration: 3
        });
      });
  };

  // 编辑路由
  const handleEdit = (record: RouteType) => {
    console.log('编辑路由:', record);
    // 此处需要添加编辑路由的逻辑，根据需求描述，不需要在本次实现
    navigate(`${urls.routeEdit}?instanceId=${instanceId}&routeName=${record.routeName}`);
  };

  // 删除路由
  // region参数由useRegion钩子提供，用于设置接口请求中的X-Region请求头
  const handleDelete = async (record: RouteType) => {
    try {
      console.log('删除路由:', record);
      const res = await deleteRoute(instanceId, record.routeName, region);
      
      console.log('删除路由响应:', res);
      if (res.success) {
        // 显示成功提示
        toast.success({
            message:`路由 ${record.routeName} 删除成功`,
            duration: 3
        });
        // 刷新列表
        fetchRouteList();
      } else {
        // 显示错误提示
        toast.error({
          message: res.message || '删除路由失败',
          duration: 3
        });
      }
    } catch (error) {
      console.error('删除路由失败:', error);
      toast.error({
        message: '删除路由失败，请稍后重试',
        duration: 3
      });
    }
  };

  // 刷新列表
  const handleRefresh = () => {
    console.log('刷新路由列表');
    // 不改变任何参数，直接重新获取数据
    fetchRouteList();
  };

  // 分页变化
  const handlePageChange = (pageNo: number, pageSize?: number) => {
    console.log('分页变化:', { pageNo, pageSize });
    setPageParams({
      ...pageParams,
      pageNo,
      pageSize: pageSize || pageParams.pageSize
    });
  };

  // 创建路由
  const handleCreateRoute = () => {
    console.log('创建路由');
    navigate(`${urls.routeCreate}?instanceId=${instanceId}`);
  };

  // 进入路由详情页
  const handleRouteDetail = (record: RouteType) => {
    console.log('进入路由详情页:', record);
    navigate(`${urls.routeDetail}?instanceId=${instanceId}&routeName=${record.routeName}`);
  };

  return (
    <div className="aigw-detail-content-wrap">
    <div className="aigw-detail-content-title">路由配置</div>
    <div className="aigw-detail-operation-container">
      <div className="aigw-detail-operation-left">
        <Button type="primary"  icon={<OutlinedPlusNew />} onClick={() => handleCreateRoute()}>
          创建路由
        </Button>
      </div>
      <div className="aigw-detail-operation-right">
        <Input
          placeholder="请输入路由名称"
          value={searchValue}
          onChange={handleSearchChange}
          onKeyPress={handleSearchKeyPress}
          suffix={<OutlinedSearch onClick={handleSearch} style={{ cursor: 'pointer' }} />}
          style={{ width: '240px' }}
        />
        <Button
          icon={<OutlinedRefresh />}
          onClick={handleRefresh}
        />
      </div>
    </div>
      

      <Table
        className={styles.routeTable}
        columns={columns}
        dataSource={routeList}
        loading={loading}
        rowKey="routeName"
        tableLayout="fixed"
        locale={{ 
          emptyText: (
            <Empty 
              description={
                <span>
                  暂未创建路由。
                  <a onClick={handleCreateRoute} style={{ marginLeft: '4px', cursor: 'pointer' }}>
                    <OutlinedPlusNew style={{ marginRight: '4px' }} />
                    创建路由
                  </a>
                </span>
              } 
            />
          ) 
        }}
        pagination={{
          current: pageParams.pageNo,
          pageSize: pageParams.pageSize,
          total: totalCount,
          onChange: handlePageChange,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`
        }}
      />

      {/* 调用示例弹窗 */}
      <Modal
        title="调用示例"
        visible={exampleVisible}
        onCancel={() => setExampleVisible(false)}
        footer={null}
        width={1000}
      >
        <Alert
          message="若当前路由开启了消费者认证，请使用环境变量或其他方法来隐藏您的消费者认证信息。"
          type="warning"
          showIcon
          style={{ marginBottom: '16px', width: '100%' }}
        />
        
        <div style={{ marginBottom: '8px',color:'#84868C' }}>
          本调用示例仅供参考，若您的推理服务不符合 OpenAI 协议，需自行替换请求参数。复制后，并将其中的 $AUTH_INFO 替换成您的消费者认证信息

        </div>
        
        <div style={{ 
          position: 'relative', 
          marginBottom: '16px' 
        }}>
          <div style={{ 
            padding: '8px 16px', 
            backgroundColor: '#f5f5f5', 
            borderTopLeftRadius: '4px',
            borderTopRightRadius: '4px',
            borderBottom: '1px solid #e8e8e8',
            fontSize: '14px',
           
          }}>
            Curl
          </div>
          <div style={{ 
            position: 'absolute', 
            right: '8px', 
            top: '8px', 
            cursor: 'pointer' 
          }} onClick={handleCopyExample}>
            <OutlinedCopy />
          </div>
          <div style={{
            display: 'flex',
            backgroundColor: '#f5f5f5',
            borderBottomLeftRadius: '4px',
            borderBottomRightRadius: '4px',
            overflow: 'auto',
            maxHeight: '300px',
            fontFamily: 'monospace',
            fontSize: '13px',
            lineHeight: '1.5'
          }}>
            {/* 行号区域 */}
            <div style={{
              padding: '16px 8px 16px 16px',
              textAlign: 'right',
              color: '#999',
              userSelect: 'none',
              borderRight: '1px solid #e8e8e8',
              backgroundColor: '#f0f0f0',
              whiteSpace: 'pre',
              lineHeight: '1.5'
            }}>
              {`1\n2\n3\n4\n5\n6\n7\n8\n9`}
            </div>
            {/* 代码区域 */}
            <pre style={{ 
              padding: '16px',
              margin: 0,
              flexGrow: 1,
              fontFamily: 'monospace',
              whiteSpace: 'pre',
              lineHeight: '1.5'
            }}>
{`curl http://${props.detail?.data?.externalIP || '{网关入口地址}'}/v1/chat/completions \\
-H "Content-Type: application/json" \\
-H "Authorization: Bearer $AUTH_INFO" \\
-d '{
  "model": "deepseek-r1-distill-qwen-32b",
  "messages": [{"role": "user", "content": "Say this is a test!"}],
  "temperature": 0.7,
  "stream": true
}'`}
            </pre>
          </div>
        </div>
        
       
      </Modal>
    </div>
  );
};

export default RouteList;
