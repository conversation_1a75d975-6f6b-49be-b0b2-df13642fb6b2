import React, {useCallback, useState} from 'react';
import {
  AppContextActionType,
  env,
  useAppContext,
  useFrameworkContext
} from '@baidu/bce-react-toolkit';
import {Button, Checkbox, Tooltip} from 'acud';
import {useBoolean, useRequest} from 'ahooks';

import {activateIamStsRole, activeMse, queryIamStsRole} from '@/apis/auth';
import InfoIcon from '@/assets/svg/info.svg';
import {isSubUser} from '@/utils/utils';

import styles from './banner.module.less';

const Banner: React.FC = () => {
  const [agreeTipVisible, setAgreeTipVisible] = useState(false);
  const [protocolChecked, setProtocolChecked] = useState(false);
  const [cpromHasActive, setCprom] = useState(false);

  const subUser = isSubUser();

  const {appState, appDispatch} = useAppContext();
  const {userId} = useFrameworkContext();

  const [
    isActivateBtnLocked,
    {setTrue: lockActivateBtn, setFalse: unlockActivateBtn}
  ] = useBoolean(false);

  useRequest(() => queryIamStsRole({roleName: 'BceServiceRole_cprom'}), {
    onSuccess: (res) => {
      if (res?.result?.name) {
        setCprom(true);
      }
    }
  });

  const onProtocolChange = useCallback(
    (e: any) => {
      setProtocolChecked(e.target.checked);

      if (e.target.checked && agreeTipVisible) {
        setAgreeTipVisible(false);
      }
    },
    [agreeTipVisible]
  );

  const onClickActivateBtn = useCallback(async () => {
    if (!protocolChecked) {
      setAgreeTipVisible(true);
      return;
    }

    if (subUser) {
      setAgreeTipVisible(true);
      return;
    }

    if (isActivateBtnLocked) {
      return;
    }

    lockActivateBtn();

    const promises = [
      activateIamStsRole({
        roleName: 'BceServiceRole_csm',
        accountId: userId!,
        policyId: env.isSandbox //服务角色
          ? ''
          : '********************************',
        serviceId: env.isSandbox //服务号 ID，用于身份标识，这里需要替换成CSM的,CSM没有沙盒的
          ? ''
          : '2bf77fcc60c0446b8d45abc3bd789ce0'
      }),
      // activeMse()
    ];
    if (!cpromHasActive) {
      promises.push(
        activateIamStsRole({
          roleName: 'BceServiceRole_cprom',
          accountId: userId!,
          policyId: env.isSandbox
            ? '********************************'
            : '144183130855432e968f4632aebda251',
          serviceId: env.isSandbox
            ? '********************************'
            : '137e3e4dc46e44ea805c173835ad6347'
        })
      );
    }

    const result = await Promise.all(promises);

    if (result.every((item) => item.success)) {
      appDispatch({
        type: AppContextActionType.ACTIVATE_PRODUCT
      });
    }

    unlockActivateBtn();
  }, [
    appDispatch,
    appState.serviceParams,
    isActivateBtnLocked,
    lockActivateBtn,
    protocolChecked,
    unlockActivateBtn,
    userId,
    cpromHasActive
  ]);
//开通页面最顶部的 banner 模块
  return (
    <div className={styles.container}>
      <div className={styles.contentContainer}>
        <div className={styles.title}>
          云原生网关 —— 高扩展、高可用的云上托管网关服务
        </div>
        <div className={styles.desc}>
          百度智能云云原生网关（AI Gateway）集流量网关、微服务网关、安全网关和 AI 网关四合一，实现碎片化网关的架构统一，是面向 Kubernetes AI 推理场景/传统容器服务/微服务设计的云原生网关，通过零侵入式服务代理与多策略流量调度，为企业提供生产级高效接入与调度能力。
        </div>

        <div className={styles.operateContainer}>
          <div className={styles.desc}>
            <div className={styles.svgWrap}>
              {' '}
              <InfoIcon />
            </div>
            <div>
              开通云原生网关产品，将自动在 IAM
              中创建服务角色并授权相关数据调用权限。同时为您默认开通{' '}
              <a
                href="https://cloud.baidu.com/doc/CCE/index.html"
                target="blank"
              >
                容器引擎 CCE
              </a>
              、
              <a
                href="https://cloud.baidu.com/doc/VPC/index.html"
                target="blank"
              >
                私有网络 VPC
              </a>
              {' '}2 个云产品。开通后，云原生网关将获取您在 CCE 和 VPC 中的集群以及实例列表。
            </div>
          </div>
          <div className={styles.protocolContainer}>
            <Tooltip
              title={
                <span className={styles.agreeTip}>
                  {protocolChecked && subUser
                    ? '当前登录的子用户没有开通产品服务的权限，请联系主账户开通服务权限，或联系主账户开通服务后授权使用'
                    : '请先阅读并同意服务协议信息'}
                </span>
              }
              visible={agreeTipVisible}
            >
              <Checkbox
                onChange={onProtocolChange}
                value={protocolChecked}
              ></Checkbox>
            </Tooltip>
            <span className={styles.agreeText}>我已阅读并同意</span>
            <a
              href="https://cloud.baidu.com/doc/Agreements/s/yjwvy1x03"
              target="_blank"
              rel="noreferrer"
            >
              《百度智能云用户服务协议》
            </a>
          </div>
          <div className={styles.buttonWrapper}>
          <Button
            type="primary"
            className={styles.activateBtn}
            onClick={onClickActivateBtn}
          >
            立即开通
          </Button>
            <div className={styles.betaTag}>产品目前公测中，公测期间免费为用户提供网关服务</div>
          </div>
        </div>
      </div>
      <div className={styles.bannerWrap}>
        <div className={styles.banner}></div>
      </div>
    </div>
  );
};

export default Banner;
