/**
 * 调试版本的图例工具提示
 * 用于排查问题和验证功能
 */

/**
 * 调试图例工具提示设置
 * @param chartInstance ECharts实例
 * @param originalDataMap 原始数据映射
 * @param maxTextLength 最大文本长度
 */
export function debugLegendTooltip(
  chartInstance: any,
  originalDataMap: Record<string, string>,
  maxTextLength: number = 25
) {
  console.log('=== 开始调试图例工具提示 ===');
  console.log('chartInstance:', !!chartInstance);
  console.log('originalDataMap:', originalDataMap);
  console.log('maxTextLength:', maxTextLength);

  if (!chartInstance) {
    console.error('❌ chartInstance 为空');
    return;
  }

  if (!originalDataMap || Object.keys(originalDataMap).length === 0) {
    console.error('❌ originalDataMap 为空或无数据');
    return;
  }

  // 延迟执行，确保图表渲染完成
  const setupDebugTooltips = () => {
    console.log('🔍 开始查找图例元素...');
    
    const chartDom = chartInstance.getDom();
    if (!chartDom) {
      console.error('❌ 无法获取图表DOM元素');
      return;
    }

    console.log('✅ 图表DOM元素找到:', chartDom);

    // 查找所有文本元素
    const allTexts = chartDom.querySelectorAll('text');
    console.log(`📝 找到 ${allTexts.length} 个文本元素`);

    // 查找所有可能的图例元素
    const possibleLegends = [];
    
    allTexts.forEach((textEl: any, index: number) => {
      const textContent = textEl.textContent || textEl.innerText || '';
      const rect = textEl.getBoundingClientRect();
      
      console.log(`文本 ${index}: "${textContent}" 位置:`, {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height
      });

      // 检查是否可能是图例文本
      Object.keys(originalDataMap).forEach(key => {
        const originalText = originalDataMap[key];
        
        // 检查是否匹配（完整匹配或截断匹配）
        if (textContent === originalText || 
            (originalText.length > maxTextLength && textContent.includes('...'))) {
          
          possibleLegends.push({
            element: textEl,
            textContent,
            originalText,
            key,
            isTruncated: originalText.length > maxTextLength
          });
          
          console.log(`🎯 找到可能的图例: "${textContent}" -> "${originalText}"`);
        }
      });
    });

    console.log(`📊 找到 ${possibleLegends.length} 个可能的图例元素`);

    // 为每个图例元素添加工具提示
    possibleLegends.forEach((legend, index) => {
      console.log(`🔧 为图例 ${index} 设置工具提示: "${legend.textContent}"`);
      
      // 方法1: 添加title属性
      legend.element.setAttribute('title', legend.originalText);
      legend.element.style.cursor = 'help';
      
      // 方法2: 添加自定义事件监听
      const showCustomTooltip = (e: MouseEvent) => {
        console.log('🖱️ 鼠标进入图例:', legend.textContent);
        
        // 移除已存在的工具提示
        const existingTooltip = document.querySelector('.debug-legend-tooltip');
        if (existingTooltip) {
          existingTooltip.remove();
        }

        // 创建新的工具提示
        const tooltip = document.createElement('div');
        tooltip.className = 'debug-legend-tooltip';
        tooltip.style.cssText = `
          position: fixed;
          background: rgba(255, 0, 0, 0.9);
          color: white;
          padding: 10px 15px;
          border-radius: 4px;
          font-size: 12px;
          max-width: 300px;
          word-wrap: break-word;
          z-index: 99999;
          pointer-events: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          border: 2px solid yellow;
        `;
        tooltip.textContent = `完整文本: ${legend.originalText}`;
        tooltip.style.left = e.clientX + 10 + 'px';
        tooltip.style.top = e.clientY - 10 + 'px';
        
        document.body.appendChild(tooltip);
        console.log('✅ 自定义工具提示已显示');
      };

      const hideCustomTooltip = () => {
        console.log('🖱️ 鼠标离开图例:', legend.textContent);
        const tooltip = document.querySelector('.debug-legend-tooltip');
        if (tooltip) {
          tooltip.remove();
          console.log('✅ 自定义工具提示已隐藏');
        }
      };

      legend.element.addEventListener('mouseenter', showCustomTooltip);
      legend.element.addEventListener('mouseleave', hideCustomTooltip);
      
      console.log(`✅ 图例 ${index} 工具提示设置完成`);
    });

    console.log('🎉 所有图例工具提示设置完成');
  };

  // 多次尝试设置，确保成功
  const delays = [500, 1000, 2000, 3000];
  delays.forEach((delay, index) => {
    setTimeout(() => {
      console.log(`⏰ 第 ${index + 1} 次尝试设置工具提示 (延迟 ${delay}ms)`);
      setupDebugTooltips();
    }, delay);
  });

  // 监听图表事件
  chartInstance.on('finished', () => {
    console.log('📈 图表渲染完成事件触发');
    setTimeout(setupDebugTooltips, 500);
  });

  chartInstance.on('rendered', () => {
    console.log('🎨 图表重绘事件触发');
    setTimeout(setupDebugTooltips, 500);
  });

  console.log('=== 图例工具提示调试设置完成 ===');

  return () => {
    console.log('🧹 清理调试工具提示');
    const tooltips = document.querySelectorAll('.debug-legend-tooltip');
    tooltips.forEach(tooltip => tooltip.remove());
  };
}
