.detail-widget {
  min-width: 1280px;
  height: 100%;
  background-color: #f7f7f7;
  overflow: auto;
  &-header {
    display: flex;
    align-items: center;
    flex-shrink: none;
    background-color: #ffffff;
    height: 50px;
    margin: 0;
    padding: 0 16px;
    .acud-link {
      color: #83868c;
      font-size: 14px;
      display: flex;
      align-items: center;
    }
    .name {
      max-width: 256px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-left: 16px;
      font-size: 16px;
      font-weight: 500;
      color: #151b26;
    }
    .status-badge-common {
      margin-left: 12px;
      margin-top: 2px;
      &:before {
        margin-right: 5px;
      }

      &.error {
        &:before {
          color: #f33e3e;
        }
      }

      &.warning {
        &:before {
          color: #ff9326;
        }
      }

      &.normal {
        &:before {
          color: #30bf13;
        }
      }

      &.unavailable {
        &:before {
          color: #b5b7ba;
        }
      }

      &.blue {
        &:before {
          color: #528eff;
        }
      }

      &.scaling {
        &:before {
          color: #2468f2;
        }

        &:after {
          color: #528eff;
          font-family: iconfont;
          content: '\e632';
          font-size: inherit;
          position: absolute;
          left: 0;
          top: 0;
          animation-delay: 1s;
        }

        &:after {
          animation-name: StatusScaling;
          animation-duration: 3s;
          animation-iteration-count: infinite;
          animation-timing-function: linear;
        }
      }
      &.weak-warning {
        &:before {
          color: #ffce00;
        }
      }
    }
  }
  &-content {
    margin: 16px;
    min-height: calc(~'100% - 82px');
    background-color: #ffffff;
    border-radius: 6px;
    & > .tab-widget {
      .tab-vertical-widget {
        min-height: calc(~'100vh - 132px');
        .acud-tabs-nav {
          border-radius: 6px;
          &::before {
            border-bottom: none;
          }
          .acud-tabs-nav-wrap {
            .acud-tabs-nav-list {
              padding-top: 8px;
              width: 160px;
            }
          }
        }
      }
    }
  }
}

.detail-widget-header-wrap {
  display: flex;
  background-color: #fff;
  align-items: center;
  justify-content: space-between;
  padding-right: 16px;
  .mse-custom-header-link {
    color: #151b26;
    display: none; // 隐藏帮助文档按钮
    align-items: center;
    svg {
      font-size: 14px;
    }
  }
}

.no-instance-wrap {
  height: calc(~'100% - 50px');
  .dcgw-tab-class {
    margin: 0;
  }
}

.page-empty {
  position: relative;
  top: 25%;
  .page-empty-action {
    .page-empty-action-button:nth-child(2n + 2) {
      display: none;
    }
  }
}

.acud-loading-loading-wrapper {
  height: 100%;
  & > * {
    height: 100%;
  }
}
