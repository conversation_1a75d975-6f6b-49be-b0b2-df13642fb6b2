import React, {FC, useEffect, useState} from 'react';
import {
  GlobalNotifyDescription,
  GlobalNotifyMessage,
  PageEmpty
} from '@baidu/bce-react-toolkit';
import {Link, Loading, Tag, toast, Tooltip} from 'acud';
import {OutlinedInfoCircle, OutlinedLeft} from 'acud-icon';
import cn from 'classnames';

import SecurityTabs, {TabProps} from '@/components/TabPage';

import './index.less';

interface DetailPageProps {
  backUrl?: string;
  headerName?: string;
  target?: '_blank' | '_self';
  statusClassName?: string;
  statusText?: string;
  isShowHeader?: boolean;
  tabClassName?: string;
  dataResponse?: any; // 详情接口的返回值
  onBackList?: any; // 返回列表的回调函数
  matchCode?: string; // 匹配对应matchCode的屏蔽字段
  module?: string; // 报错时候的模块名称，一般为serviceType
  isCustomContent?: boolean; // 是否使用自定义内容而不是标准的Tabs
  children?: React.ReactNode; // 自定义内容的子元素
  headerOperations?: React.ReactNode; // 头部右侧自定义操作按钮
}

const DetailPage: FC<DetailPageProps & TabProps> = (props) => {
  const {
    backUrl = '',
    mode = 'horizontal',
    target = '_self',
    headerName,
    panesData,
    statusText = '',
    statusClassName = '',
    isShowHeader = true,
    tabClassName = '',
    dataResponse,
    onBackList,
    matchCode,
    module = '',
    isCustomContent = false,
    children,
    headerOperations
  } = props;
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // 保证在请求成功或者报错的情况下，将loading变为false，用vpcId的意义是因为都具有vpcId属性，这个值后续酌情变动
    if (!dataResponse || (dataResponse && Object.keys(dataResponse).length)) {
      setLoading(false);
    }
    if (dataResponse?.code && dataResponse?.code !== matchCode) {
      const requestId = dataResponse.requestId;
      const ticket = 'https://console.bce.baidu.com/ticket/#/ticket/create';
      toast.error({
        message: (
          <GlobalNotifyMessage
            requestId={requestId!}
            ticket={ticket}
            module={module}
            message={typeof dataResponse?.message?.global === 'object' 
              ? JSON.stringify(dataResponse?.message?.global) 
              : dataResponse?.message?.global}
          ></GlobalNotifyMessage>
        ),
        description: (
          <GlobalNotifyDescription
            requestId={requestId}
          ></GlobalNotifyDescription>
        ),
        key: requestId,
        className: 'global-toast-error-container'
      });
    }
  }, [dataResponse]);

  const noInstanceClass = dataResponse?.message?.global
    ? 'no-instance-wrap'
    : '';
  return (
    <Loading loading={loading}>
      <div className={cn('detail-widget', {[noInstanceClass]: true})}>
        {isShowHeader && (
          <div className="detail-widget-header-wrap">
            <div className="detail-widget-header">
              <Link
                href={backUrl}
                target={target}
                type="primary"
                icon={<OutlinedLeft width={16} height={16} fill={'#84868c'} />}
              >
                返回
              </Link>
              <Tooltip title={headerName}>
                <span className="name">{headerName}</span>
              </Tooltip>
              <Tag
                color="transparent"
                className="table-status"
                style={{paddingLeft: '14px'}}
                icon={<span className={cn('circle', statusClassName)} />}
              >
                {statusText}
              </Tag>
            </div>
            {headerOperations ? (
              headerOperations
            ) : (
              <Link
                className="mse-custom-header-link"
                href="https://cloud.baidu.com/doc/MSE/index.html"
                target="_blank"
                icon={<OutlinedInfoCircle />}
              >
                产品文档
              </Link>
            )}
          </div>
        )}
        <div className={cn('detail-widget-content', {[tabClassName]: true})}>
          {dataResponse?.code && dataResponse?.code === matchCode && (
            <PageEmpty
              title="您访问的资源不存在或者已被删除"
              desc="请稍候刷新页面重试，或返回资源列表页刷新确认信息"
              goBack={() => {
                onBackList('Hello from Child');
              }}
              refresh={() => {}}
            />
          )}
          {(!dataResponse?.code ||
            (dataResponse?.code && dataResponse?.code !== matchCode)) && (
            <>
              {isCustomContent ? (
                children
              ) : (
                <SecurityTabs mode={mode} panesData={panesData} />
              )}
            </>
          )}
        </div>
      </div>
    </Loading>
  );
};
export default DetailPage;
