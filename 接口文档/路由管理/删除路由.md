# 删除路由接口

## 接口基本信息

- **接口说明**：根据实例ID和路由名称删除路由
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/{routeName}/route/detail`
- **请求方式**：DELETE
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| routeName | String | 是 | 路由名称 | inference-api |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeId": "rt-f3b9e2d1",
        "routeName": "inference-api",
        "deletedTime": "2025-04-19 10:30:25"
    }
}
```

### 错误响应体结构

```json
{
    "success": false,
    "status": 404,
    "message": "路由不存在"
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 删除的路由信息 |
| result.routeId | String | 路由ID |
| result.routeName | String | 路由名称 |
| result.deletedTime | String | 删除时间，格式：YYYY-MM-DD HH:mm:ss |
| message | String | 错误信息，仅在失败时返回 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有删除路由的权限 |
| 404 | 路由不存在 | 确认路由名称是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
DELETE /api/aigw/v1/aigateway/gw-ist9vvin/inference-api/route/detail
X-Region: gz
```

## 响应示例

### 成功响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "routeId": "rt-f3b9e2d1",
        "routeName": "inference-api",
        "deletedTime": "2025-04-19 10:30:25"
    }
}
```

### 错误响应示例

```json
{
    "success": false,
    "status": 404,
    "message": "路由不存在"
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 路由名称必须是实例中已存在的路由
3. 删除操作不可逆，请谨慎操作
4. 删除路由后，相关的API请求将不再被路由
5. 如果路由正在被使用，可能会影响现有服务 