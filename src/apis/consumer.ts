import {BaseResponseType, request} from '@baidu/bce-react-toolkit';


/**
 * 消费者信息接口
 */
export interface ConsumerType {
  consumerId?: string;
  consumerName: string;
  description?: string;
  authType: string;
  routeNames?: string[];
  createdAt?: string;
  createTime?: string; // 添加createTime字段以匹配接口返回
  unlimitedQuota?: boolean; // 是否开启不限制配额，true表示不限额
  totalQuota?: number; // 总配额值，若未开启不限额则该值为必填，默认为3000
}

/**
 * 分页返回结构
 */
export interface PageResponseType<T> {
  success: boolean;
  status: number;
  page: {
    orderBy: string;
    order: string;
    pageNo: number;
    pageSize: number;
    totalCount: number;
    result: T[];
  };
}

/**
 * 创建消费者
 * @param param 请求路径参数
 * @param data 请求体数据
 * @returns 接口响应数据
 */
export function createConsumer(
  param: {instanceId: string},
  data: ConsumerType
): Promise<BaseResponseType<ConsumerType>> {
  return request({
    url: `/api/aigw/v1/aigateway/${param.instanceId}/consumer`,
    data,
    method: 'POST'
  });
}

/**
 * 获取消费者列表
 * @param instanceId 实例ID
 * @param params 请求参数
 * @param region 地域
 * @returns 消费者列表响应数据
 */
export function getConsumerList(
  instanceId: string,
  params?: {
    pageNo: number;
    pageSize: number;
    orderBy: string;
    order: string;
    consumerName?: string;
  },
  region?: string
): Promise<{
  success: boolean;
  status: number;
  page: {
    pageNo: number;
    pageSize: number;
    orderBy: string;
    order: string;
    totalCount: number;
    result: ConsumerType[];
  };
}> {
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/consumers`,
    params,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 获取消费者详情
 * @param param 请求路径参数
 * @returns 接口响应数据
 */
export function getConsumerDetail(
  param: {instanceId: string, consumerId: string}
): Promise<BaseResponseType<ConsumerType>> {
  return request({
    url: `/api/aigw/v1/aigateway/${param.instanceId}/consumer/${param.consumerId}`,
    method: 'GET'
  });
}

/**
 * 更新消费者
 * @param param 请求路径参数
 * @param data 请求体数据
 * @returns 接口响应数据
 */
export function updateConsumer(
  param: {instanceId: string, consumerId: string},
  data: ConsumerType
): Promise<BaseResponseType<ConsumerType>> {
  return request({
    url: `/api/aigw/v1/aigateway/${param.instanceId}/consumer/${param.consumerId}`,
    data,
    method: 'PUT'
  });
}

/**
 * 删除消费者
 * @param param 请求路径参数
 * @returns 接口响应数据
 */
export function deleteConsumer(
  param: {instanceId: string, consumerId: string}
): Promise<BaseResponseType<any>> {
  return request({
    url: `/api/aigw/v1/aigateway/${param.instanceId}/consumer/${param.consumerId}`,
    method: 'DELETE'
  });
} 