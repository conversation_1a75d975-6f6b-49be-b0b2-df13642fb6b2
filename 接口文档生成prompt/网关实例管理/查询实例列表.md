现在，我需要开发 1 个接口（查询实例列表），你需要给每个接口中的参数起字段名，并规定这个字段的相关格式和限制。写 1 份接口文档，以 md 格式存储，存储在接口文档文件夹中，如果没有这个文件夹你需要新建一个。

1. 这个接口路径是：/api/aigw/v1/aigateway/list
2. 需要返回是否开启删除保护，从而帮助我之后在操作列控制是否可以删除；另外，这个接口不需要根据实例状态过滤。region不需要放在查询参数中，在请求头中带上即可
3. 响应体需要包含以下字段："namespace": "istio-system-gw-vnrdr4y4",

"instanceId": "i-abcdefgh",

"name": "ai-gateway-0413",

"ingressStatus": "running",

"replicas": 0,

"vpcCidr": "***********/16", "vpcId":""

"subnetId": "sbn-suwftkzv543e",

"gatewayType": "",

"internalIP": "",

"publicAccessible": false,

"externalIP": "",

"description": "生产环境订单系统网关",

"createTime": "2023-04-13 10:30:45",

"deleteProtection": true

"region": "gz",

|   |   |   |
|---|---|---|
|**参数**|**说明**|**相关限制**|
|实例名称/ID|网关实例名称/ID，ID 由后端随机生成唯一标识||
|状态|网关实例的运行状态，由后端返回 |1. 非运行中状态的实例无法点击实例名称进入实例详情页面2. 失败/异常原因由后端返回|
|接入地址|网关实例的接入地址，包括 公网、私网 2 种||
|描述|网关实例的描述信息||
|创建时间|网关实例的创建时间，格式 YYYY-MM-DD HH:mm:ss||
