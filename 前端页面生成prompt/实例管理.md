# 编辑实例 ✅

1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、监控信息、消费者列表），点击每个导航栏右侧会切换成当前导航栏下对应的页面，现在在实例详情页面右侧有 2 个标签页（基本信息/关联容器集群），在基本信息中可以查看实例的基本信息。

2. 需求描述：现在，我期望在基本信息标签页中能对 4 个字段（实例名称、描述、删除保护、公网访问）的值进行编辑，每个可编辑的字段右侧都新增一个编辑图标，可以使用 acud-icon 中的<OutlinedEditingSquare />图标，接下来在第 3 点页面交互中我会描述点击不同字段对应的编辑图标后的交式和样式，你需要在遵循相关规范的前提下生成相关模块。

3. 页面交互：点击实例名称右侧的编辑图标后出现一个Popover，气泡卡片中从上往下依次为输入框、提示文案、确定按钮+取消按钮，输入框中 Placeholder 提示请输入实例名称，提示文案根据更新实例这个接口中的限制回显，输入框中的内容不符合接口要求时确定按钮置灰，无法点击，并且在输入框下方以红字提示不符合要求的具体原因，点击确定后将输入框中的实例名称传给更新实例这个接口，更新成功后刷新实例详情页面。点击描述右侧的编辑图标后样式和点击实例名称右侧的编辑图标后出现的 Popover 样式一致，输入框中 Placeholder 为请输入描述，提示文案根据更新实例这个接口中的限制回显，输入框中的内容不符合接口要求时确定按钮置灰，无法点击，并且在输入框下方以红字提示不符合要求的具体原因，点击确定后将输入框中的描述传给更新实例这个接口，更新成功后刷新实例详情页面。点击公网访问右侧编辑图标后，需要在当前页面正中间打开一个浮层，出现一个Modal 弹框，弹窗标题为“修改公网访问”，弹窗中包含 2 个字段：1. 网关名称/ID  2. 公网访问，网关名称/ID 右侧内容为静态回显，公网访问右侧为一个开关，默认为当前网关实例是否开启公网访问的状态，在按钮下方有一行提示文案：“开启后将为网关绑定公网 IP，若关闭则仅可通过私有网络访问”，同时弹窗右下角为确认和取消两个按钮，点击确认后将当前这个实例的开启公网状态传给更新实例这个接口，等待接口响应，此期间确认按钮处于加载状态，若接口响应成功，则关闭浮层回到实例详情页面并重新调用查询实例详情这个接口。另外，这 4 个字段编辑成功返回实例详情页面时，需要在页面右上角出现一个成功提示无简介的 toast，提示：更新成功，3 s 后自动关闭 toast。

4. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

5. 接口文档：参见附件

6. 遵守原则：尽量不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改基本信息这个标签页，所以你不应该在为我自动完善其他页面。

# 实例列表 & 删除实例 ✅

1. 现状描述：目前在实例列表页面中展示了当前地域中的所有实例，在列表操作列中有一个删除操作。

2. 需求描述：现在，我期望实例列表中涉及到的所有接口请求、返回需要和接口文档中涉及的参数完全符合，这个页面中会涉及到 2 个接口：1. 查询实例列表 2. 删除实例，你需要在遵循相关规范的前提下修改实例列表这个页面中涉及模块相关的参数和字段。你需要按照接口文档中的对应字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。

3. 页面与接口的对应关系：看到的实例=列表=页面：总共包含 6 列（实例名称/ID、实例状态、接入地址、描述、创建时间、操作），这个列表页面列表支持=分页=，默认 10 条/页，根据创建时间进行倒序排列。其中操作列中包含 1 个操作：删除。实例名称/ID、实例状态、接入地址、描述、创建时间这 5 个参数来自于【查询实例列表】这个接口中返回的字段，其中实例名称和 ID 需要分两行展示。同时操作列中的删除操作需要根据【查询实例列表】这个接口中返回的当前实例是否开启删除保护进行判断是否可点击，如果某个实例已经开启了删除保护，那么操作列中的删除置灰，无法点击，同时当用户将鼠标移入“删除“时，需要一个=鼠标 hover 提示文案=，提示："当前实例已开启删除保护功能，请在实例详情页面中关闭删除保护后再删除。" 若删除操作可点击，点击删除后会出现一个对话框二次提示用户是否需要移除当前实例，用户确认后调用【删除实例】接口根据当前的实例 ID 执行移除服务操作。因此，在刚开始进入实例列表的时候就需要调用【查询实例列表】这个接口用于回显字段值，同时，当用户 1. 点击刷新按钮 2. 在搜索框中输入实例名称/实例 ID点击搜索时也需要重新调用查询实例列表接口。另外，若实例的状态非运行中，则无法点击实例名称进入实例详情页面，实例名称置灰，同时当鼠标移入实例名称时需要=鼠标 hover 提示文案=，提示：“当前状态下不允许查看网关详情”。

4. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

5. 接口文档：参见附件

6. 遵守原则：尽量不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改实例列表这个页面中的相关参数和接口，所以你不应该再自动修改点击实例名称之后的交互页面。



# 移除集群 ✅

1. 现状描述：现在，点击实例名称进入实例详情后，左侧有4个导航栏：实例详情、推理服务、监控信息、消费者列表，现在点击实例详情导航栏后右侧包含 2 个标签页（基本信息/实例关联信息），点击实例关联信息后又包含 2 个子标签页（关联容器集群/关联注册中心），在关联容器集群这个子标签页中包含一个=列表=，用于展示当前实例已关联的集群信息。

2. 需求描述：在关联容器集群这个列表中共包含 6 列（集群名称/ID、集群状态、备注、关联时间、最近更新时间、操作），现在我期望备注这一列中如果接口没有返回数据，就回显“-”。另外，在操作列中可执行解除关联操作，当用户点击解除关联后，就调用移除集群接口，你需要按照接口文档中的对应字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。我会在第 3 点中详细描述页面交互以及页面和接口的对应关系。

3. 页面交互与样式&页面和接口的对应关系：点击解除关联后出现一个Popconfirm，提示用户：是否解除关联？用户点击确定后根据当前集群 ID 和实例 ID调用移除集群操作，实例 ID 在当前路由路径中可以查到。点击确定后根据接口返回的信息在当前页面右上角提示，1. 若返回成功则出现一个成功提示无简介的 toast，提示：已移除集群，3 s 后自动关闭 toast。2. 若返回失败则出现一个错误提示的 toast，message 为解除关联失败，description 为后端返回的具体信息。并且同时刷新当前这个列表页。

4. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

5. 接口文档：参见附件

6. 遵守原则：尽量不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改关联容器集群这个子标签页中的相关参数和接口，所以你不应该再自动修改其他页面。

在实例列表中，操作列中包含删除，现在需要补充一些逻辑，若删除操作可点击，用户点击删除后会出现一个弹窗，用户在弹窗中点击右下角确定按钮确认删除后，需要根据当前实例 ID 调用查询服务列表这个接口，根据接口返回的 totalCount 数量（实例中服务总数）判断当前实例中是否存在服务，若果存在服务则无法被删除，即如果 totalCount 数量>0，则在页面右上角 toast 错误提示：当前实例中仍存在服务，暂无法删除，需要先移除所有服务后才能执行删除操作。


# 实例列表支持编辑

1. 现状描述：目前在实例列表页面中展示了当前地域中的所有实例，在列表操作列中有一个删除操作。当鼠标移入实例列表中某一行时，实例 ID 和接入地址中内网、公网后面会出现一个复制的图标，点击之后支持复制对应内容。
2. 需求描述：现在，我期望保留原有复制功能和逻辑不变，当鼠标移入实例列表中某一行时，实例名称和描述对应的内容右侧会出现一个编辑的图标，点击编辑图标之后可以对对应的内容进行编辑。当用户编辑完成后，就调用更新实例接口，接口字段和格式和接口文档保持一致，我会在第 3 点中描述页面交互以及和页面接口对应的关系。
3. 页面交互与样式&页面和接口的对应关系：当用户点击实例名称右侧的编辑图标后，出现一个 Popover 气泡卡片，卡片包含 1 个输入框，左下角为确定和取消按钮，首先需要在输入框内回显当前网关实例的名称，用户支持编辑，在输入框下方有一行提示文案：“必须以字母或者中文开头，支持大小写字母、中文、数字以及-/.特殊字符“，并且输入框长度限制 64，在输入的过程中需要判断实例名称是否符合规则，如果不符合需要输入框置红，并且在输入框下方提示不合法的原因；仅合法实例名称可点击确定按钮。点击确定按钮后调用更新实例接口，将输入框内的实例名称传给接口，若更新成功则右上角 toast 提示：实例名称编辑成功。当用户点击描述内容右侧的编辑图标后，出现一个Popover 气泡卡片，卡片包含 1 个输入框，左下角为确定和取消按钮，同样首先在输入框回显原有的描述内容，支持编辑，编辑完成后点击确定调用更新实例接口，若更新成功则右上角 toast 提示：实例描述编辑成功。
4. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。
5.  接口文档：参见附件
6. 遵守原则：尽量不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改实例列表中编辑实例名称和描述的逻辑，所以你不应该再自动修改其他页面。



![[Pasted image 20250513165629.png]]

