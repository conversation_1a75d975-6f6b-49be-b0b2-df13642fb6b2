import React, {memo, useMemo, useRef} from 'react';
import {Form, Input, InputNumber, Modal, Popover, Switch} from 'acud';
import {OutlinedQuestionCircle} from 'acud-icon';

import {createServiceInstance, editServiceInstance} from '@/apis/serviceAdmin';

import c from './index.module.less';
import Label, {LabelItemType} from './Label';

const TTL = (props: {value?: number; onChange?: () => void}) => {
  const {value, onChange} = props;
  return (
    <div className={c['ttl-wrap']}>
      <span>检查方式：心跳上报</span>
      <span
        style={{marginLeft: 20, marginTop: -8}}
        className={c['ttl-wrap-input']}
      >
        TTL：
        <InputNumber
          min={1}
          max={60}
          value={value}
          step={1}
          precision={0}
          onChange={onChange}
        />{' '}
        秒
      </span>
    </div>
  );
};

function CreateServiceInstance(props: {
  instanceId: string;
  serviceId: string;
  serviceName: string;
  instance?: Record<string, any>;
  refresh: any;
  isEdit?: boolean;
  namespace: string;
  isModalOpen: boolean;
  onClose: any;
}) {
  const [formInstance] = Form.useForm();

  const {
    instanceId,
    serviceId,
    serviceName,
    instance,
    refresh,
    namespace,
    isEdit = false,
    isModalOpen,
    onClose
  } = props;

  const {
    host,
    port,
    healthCheckEnable,
    ttl,
    isolateEnable,
    metadata,
    serviceInstanceId
  } = instance || {};

  const metadataArr: LabelItemType[] = [];

  if (metadata) {
    Object.keys(metadata).forEach((key) => {
      metadataArr.push({
        label: key,
        value: metadata[key] as string
      });
    });
  }

  const defaultValue = useMemo(() => {
    return isEdit && instance
      ? {
          serviceName: serviceName,
          namespace: namespace,
          host,
          port,
          healthCheckEnable,
          ttl: ttl || 5,
          isolateEnable,
          metadata: metadataArr
        }
      : {
          serviceName: '',
          namespace: namespace,
          host: '',
          port: '',
          healthCheckEnable: false,
          ttl: 5,
          isolateEnable: false,
          metadata: []
        };
  }, [isEdit, instance, isModalOpen]);

  const handleCanale = () => {
    formInstance.resetFields();
    onClose();
  };

  const labelRef = useRef<{validate: () => Promise<any>}>(null);

  const labelValidate = async () => {
    return labelRef.current?.validate();
  };

  const onSubmit = async (data: Record<string, any>) => {
    let res: any = null;
    if (isEdit) {
      res = await editServiceInstance(
        {
          instanceId,
          serviceId,
          serviceInstanceId
        },
        data
      );
    } else {
      res = await createServiceInstance({instanceId, serviceId}, data);
    }
    if (res?.success) {
      handleCanale();
      refresh();
    }
  };

  const onConfirm = async () => {
    await Promise.all([formInstance?.validateFields(), labelValidate?.()]);
    const data = formInstance.getFieldsValue();
    data.host = data.host?.trim();
    const metadata = data.metadata || [];
    const metadataObj: Record<string, any> = {};
    metadata.forEach((item: LabelItemType) => {
      item.label && (metadataObj[item.label] = item.value);
    });
    data.metadata = metadataObj;
    data.namespace = namespace;
    data.serviceName = serviceName;
    onSubmit(data);
  };

  return (
    <Modal
      className={c['create-instance-modal']}
      title={isEdit ? '编辑服务实例' : '创建服务实例'}
      visible={isModalOpen}
      width={700}
      onCancel={() => handleCanale()}
      onOk={onConfirm}
    >
      <Form
        initialValues={defaultValue}
        labelAlign="left"
        form={formInstance}
        labelWidth={119}
      >
        {isEdit ? (
          <Form.Item label="实例IP" name="host">
            {host}
          </Form.Item>
        ) : (
          <Form.Item
            label="实例IP"
            name="host"
            required
            rules={[
              {
                pattern:
                  /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/,
                message: 'IP格式不符合规则'
              }
            ]}
          >
            <Input
              placeholder="请输入实例IP"
              style={{width: 400}}
              disabled={isEdit}
            />
          </Form.Item>
        )}

        {isEdit ? (
          <Form.Item label="端口" name="port">
            {port}
          </Form.Item>
        ) : (
          <Form.Item label="端口" name="port" required>
            <InputNumber
              style={{width: 400}}
              min={0}
              step={1}
              max={65535}
              precision={0}
              disabled={isEdit}
              placeholder="请输入端口"
            />
          </Form.Item>
        )}

        <Form.Item
          label="实例标签"
          name="metadata"
          extra="温馨提示：实例标签用于标识实例的功能和特征，每个标签包含键和值两部分"
        >
          <Label ref={labelRef} />
        </Form.Item>
        <Form.Item
          label={
            <>
              开启健康检查：
              <Popover content="开启后，服务实例的健康状态检查将由Server端负责">
                <OutlinedQuestionCircle />
              </Popover>
              <span style={{marginRight: 3}}></span>
            </>
          }
          name="healthCheckEnable"
        >
          <Switch defaultChecked={defaultValue.healthCheckEnable} />
        </Form.Item>
        <Form.Item shouldUpdate noStyle>
          {({getFieldValue}) => {
            const healthCheckEnable = getFieldValue('healthCheckEnable');
            if (healthCheckEnable) {
              return (
                <Form.Item noStyle name="ttl">
                  <TTL />
                </Form.Item>
              );
            }
          }}
        </Form.Item>
        <Form.Item
          label={
            <>
              是否隔离:
              <Popover content="在隔离状态下，主调方无法发现识别被隔离的服务实例，不论其健康状态如何">
                <OutlinedQuestionCircle />
              </Popover>
              <span style={{marginRight: 3}}></span>
            </>
          }
          name="isolateEnable"
        >
          <Switch defaultChecked={defaultValue.isolateEnable} />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default memo(CreateServiceInstance);
