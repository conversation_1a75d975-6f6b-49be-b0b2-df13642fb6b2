.serviceStatus {
  position: relative;
  color: #151B26;
  cursor: pointer;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.67;
  margin-right: 16px;

  // 静态虚线效果 - 根据Figma设计稿调整
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: repeating-linear-gradient(90deg, #5C5F66 0, #5C5F66 3px, transparent 3px, transparent 6px);
  }
}



// 工具提示样式
:global(.serviceStatusTooltip) {
  .acud-tooltip-inner {
    background: #fff;
    color: #333;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 12px;
    min-width: 200px;
  }

  .acud-tooltip-arrow {
    &::before {
      background: #fff;
      border: 1px solid #d9d9d9;
    }
  }
}

.tooltipContent {
  .tooltipItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .tooltipLabel {
    color: #666;
    font-size: 12px;
    margin-right: 12px;
    white-space: nowrap;
  }
  
  .tooltipValue {
    color: #333;
    font-size: 12px;
    font-weight: 500;
    text-align: right;
    word-break: break-all;
  }
}
