现在，我需要开发 1 个接口（查询实例关联的集群列表），你需要给每个接口中的参数起字段名，并规定这个字段的相关格式和限制。写 1 份接口文档，以 md 格式存储，存储在接口文档文件夹中，如果没有这个文件夹你需要新建一个。（包含 5 个参数），注意这个接口是列表相关，需要遵循接口文档中的规范。

1. 查询实例关联的集群列表的接口路径为：/api/aigw/v1/aigateway/instance/{instanceId}/clusterList

|   |   |   |
|---|---|---|
|**参数**|**说明**|**相关限制**|
|集群名称/ID|回显当前网关实例已经关联的 CCE 集群名称/ID||
|集群状态|当前关联的 CCE 集群的状态，包含运行中、访问异常||
|备注|选填，可输入本次关联集群的备注信息，若选择了多个集群，则这一批次的集群备注信息一样。若无则回显：-||
|关联时间|关联当前集群的时间||
|最近更新时间|最近一次更新 CCE 集群中服务的时间|