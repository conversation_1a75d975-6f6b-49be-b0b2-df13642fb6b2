import React, {useContext, useEffect, useRef, useState} from 'react';
import {getQueryParams, useRegion} from '@baidu/bce-react-toolkit';
import {Select} from 'acud';
import {useRequest} from 'ahooks';

import {getInstanceList} from '@/apis/instance';
import {SmartContext} from '@/contexts/SmartContext';
import {compareVersion} from '@/utils/utils';

import styles from './index.module.less';

const filterOption = (input: any, option: any) => {
  return option?.label?.toLowerCase()?.includes(input.toLowerCase());
};

const ConfigManageHeader = () => {
  const {instanceId} = getQueryParams();
  const {region} = useRegion();
  const isFirstRun = useRef(true);
  const [instanceList, setInstanceList] = useState<
    Array<{label: string; value: string}>
  >([]);
  const [currentRegistrationInstance, setRegistrationInstance] =
    useState<string>('');
  const {configInstance, setConfigInstance} = useContext(SmartContext);

  const {run: _getInstanceList} = useRequest(
    () => getInstanceList({pageNo: 1, pageSize: 1000}),
    {
      onSuccess: (res) => {
        const list = (res?.result?.result || [])
          .filter((e) => e.status === 2)
          .filter(
            (e) => e.release === '1.2.0' || compareVersion(e.release, '1.2.0')
          )
          ?.map((item) => ({
            label: item.name,
            value: item.id
          }));
        setInstanceList(list || []);
        if (!instanceId && !configInstance && list.length) {
          setRegistrationInstance(list[0].value);
          setConfigInstance(list[0].value);
        }
      }
    }
  );

  const onRegistrationInstanceChange = (value: string) => {
    setRegistrationInstance(value);
    setConfigInstance(value);
  };

  useEffect(() => {
    if (instanceList?.length) {
      if (instanceList.find((e) => e.value === instanceId)) {
        setRegistrationInstance(instanceId);
        setConfigInstance(instanceId);
      } else if (
        configInstance &&
        instanceList.find((e) => e.value === configInstance)
      ) {
        setRegistrationInstance(configInstance);
        setConfigInstance(configInstance);
      } else {
        setRegistrationInstance(instanceList[0].value);
        setConfigInstance(instanceList[0].value);
      }
    }
  }, [instanceList]);

  useEffect(() => {}, [currentRegistrationInstance]);

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }
    _getInstanceList();
  }, [region]);

  return (
    <div className={styles['mse-config-header']}>
      <div className={styles['mse-config-header-select']}>
        所属实例：
        <Select
          options={instanceList}
          value={currentRegistrationInstance || undefined}
          showSearch
          placeholder="请选择实例"
          filterOption={filterOption}
          style={{minWidth: 100}}
          onChange={onRegistrationInstanceChange}
        ></Select>
      </div>
    </div>
  );
};

export default ConfigManageHeader;
