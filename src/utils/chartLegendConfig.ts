/**
 * 图表图例配置工具函数
 * 用于统一管理ECharts图例的显示配置，解决图例过多时遮挡图表内容的问题
 * 集成标签清理功能，优化图例显示
 */

import { cleanSeriesLabels, getLabelDisplayInfo, createLabelMapping } from './labelCleaner';

export interface LegendConfigOptions {
  /** 系列数据数量 */
  seriesCount: number;
  /** 是否为弹窗模式 */
  isModal?: boolean;
  /** 自定义格式化函数 */
  formatter?: (name: string) => string;
  /** 最大文本长度 */
  maxTextLength?: number;
  /** 是否显示选择器 */
  showSelector?: boolean;
  /** 是否启用工具提示 */
  enableTooltip?: boolean;
  /** 原始数据映射，用于工具提示显示完整文本 */
  originalDataMap?: Record<string, string>;
}

/**
 * 获取优化的图例配置
 * @param options 配置选项
 * @returns ECharts图例配置对象
 */
export function getOptimizedLegendConfig(options: LegendConfigOptions) {
  const {
    seriesCount,
    isModal = false,
    formatter,
    maxTextLength,
    showSelector = true,
    enableTooltip = true,
    originalDataMap = {}
  } = options;

  // 如果没有系列数据，不显示图例
  if (seriesCount === 0) {
    return { show: false };
  }

  // 根据系列数量确定显示模式
  const useScrollMode = seriesCount > 6;
  const showSelectorButtons = showSelector && seriesCount > 6;

  // 响应式字体大小
  const getFontSize = () => {
    if (seriesCount > 12) {
      return isModal ? 10 : 9;
    } else if (seriesCount > 8) {
      return isModal ? 11 : 10;
    } else {
      return isModal ? 12 : 11;
    }
  };

  // 响应式间距
  const getItemGap = () => {
    if (seriesCount > 12) {
      return isModal ? 8 : 6;
    } else if (seriesCount > 8) {
      return isModal ? 12 : 8;
    } else {
      return isModal ? 20 : 15;
    }
  };

  // 响应式顶部位置
  const getTopPosition = () => {
    if (isModal) {
      return seriesCount > 6 ? '15px' : '20px';
    } else {
      return seriesCount > 6 ? '8px' : '12px';
    }
  };

  // 存储截断信息的映射
  const truncatedMap: Record<string, { original: string; truncated: string; isTruncated: boolean }> = {};

  // 默认文本格式化函数
  const defaultFormatter = (name: string) => {
    const defaultMaxLength = maxTextLength || (isModal ? 35 : 25);
    const originalText = originalDataMap[name] || name;
    const isTruncated = originalText.length > defaultMaxLength;
    const displayText = isTruncated ? originalText.substring(0, defaultMaxLength) + '...' : originalText;

    // 存储截断信息
    truncatedMap[name] = {
      original: originalText,
      truncated: displayText,
      isTruncated
    };

    return displayText;
  };

  const config = {
    show: true,
    top: getTopPosition(),
    left: 'center',
    right: '10px', // 确保不超出右边界
    itemGap: getItemGap(),
    textStyle: {
      fontSize: getFontSize(),
      color: '#666'
    },
    type: useScrollMode ? 'scroll' : 'plain',
    formatter: formatter || defaultFormatter,
    // 启用图例工具提示
    tooltip: enableTooltip ? {
      show: true,
      formatter: function(params: any) {
        const name = params.name;
        const originalText = originalDataMap[name];

        // 只有被截断的图例才显示工具提示
        if (originalText && originalText.length > (maxTextLength || (isModal ? 35 : 25))) {
          return originalText;
        }
        return null; // 不显示工具提示
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      borderWidth: 0,
      padding: [8, 12],
      extraCssText: 'border-radius: 4px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); max-width: 300px; word-wrap: break-word;'
    } : undefined,

    // 滚动模式配置
    ...(useScrollMode && {
      scrollDataIndex: 0,
      pageButtonItemGap: 3,
      pageButtonGap: isModal ? 20 : 15,
      pageIconColor: '#2f4554',
      pageIconInactiveColor: '#aaa',
      pageIconSize: isModal ? 15 : 12,
      pageTextStyle: {
        color: '#333',
        fontSize: isModal ? 11 : 10
      },
      // 设置图例区域的最大高度
      height: (() => {
        if (seriesCount > 15) {
          return isModal ? '25%' : '20%';
        } else if (seriesCount > 10) {
          return isModal ? '20%' : '15%';
        } else {
          return 'auto';
        }
      })()
    }),
    // 图例选择器配置
    ...(showSelectorButtons && {
      selector: [
        {
          type: 'all',
          title: '全选'
        },
        {
          type: 'inverse',
          title: '反选'
        }
      ],
      selectorPosition: 'end'
    })
  };

  // 将截断映射附加到配置对象上，供外部使用
  (config as any)._truncatedMap = truncatedMap;
  (config as any)._originalDataMap = originalDataMap;

  return config;
}

/**
 * 获取优化的网格配置
 * @param seriesCount 系列数据数量
 * @param isModal 是否为弹窗模式
 * @returns ECharts网格配置对象
 */
export function getOptimizedGridConfig(seriesCount: number, isModal: boolean = false) {
  const getTopMargin = () => {
    if (seriesCount === 0) return '15%';
    
    if (isModal) {
      // 弹窗模式下的间距配置 - 增加图例与图表之间的距离
      if (seriesCount > 15) {
        return '40%';
      } else if (seriesCount > 10) {
        return '35%';
      } else if (seriesCount > 6) {
        return '30%';
      } else if (seriesCount > 3) {
        return '27%';
      } else {
        return '25%';
      }
    } else {
      // 普通模式下的间距配置 - 增加图例与图表之间的距离
      if (seriesCount > 15) {
        return '35%';
      } else if (seriesCount > 10) {
        return '30%';
      } else if (seriesCount > 6) {
        return '25%';
      } else if (seriesCount > 3) {
        return '22%';
      } else {
        return '20%';
      }
    }
  };

  return {
    left: isModal ? '5%' : '3%',
    right: isModal ? '5%' : '4%',
    bottom: '15%',
    top: getTopMargin(),
    containLabel: true
  };
}

/**
 * 预定义的图例描述映射
 */
export const LEGEND_DESCRIPTIONS = {
  // 'P50': 'P50: 50% 的请求在此时间内完成',
  // 'P90': 'P90: 90% 的请求在此时间内完成',
  // 'P99': 'P99: 99% 的请求在此时间内完成',
  // 'Avg': 'Avg: 平均响应时间',
  // 'Max': 'Max: 最大响应时间',
  // 'Min': 'Min: 最小响应时间'
};

/**
 * 创建带描述的格式化函数
 * @param descriptions 描述映射对象
 * @param maxLength 最大文本长度
 * @returns 格式化函数
 */
export function createDescriptiveFormatter(
  descriptions: Record<string, string> = LEGEND_DESCRIPTIONS,
  maxLength: number = 30
) {
  return function(name: string) {
    const displayName = descriptions[name] || name;
    if (displayName.length > maxLength) {
      return displayName.substring(0, maxLength) + '...';
    }
    return displayName;
  };
}

/**
 * 创建增强的格式化函数，支持工具提示
 * @param descriptions 描述映射对象
 * @param maxLength 最大文本长度
 * @param truncatedMapRef 截断信息映射的引用
 * @returns 格式化函数
 */
export function createEnhancedFormatter(
  descriptions: Record<string, string> = LEGEND_DESCRIPTIONS,
  maxLength: number = 30,
  truncatedMapRef?: { current: Record<string, { original: string; truncated: string; isTruncated: boolean }> }
) {
  return function(name: string) {
    const originalText = descriptions[name] || name;
    const isTruncated = originalText.length > maxLength;
    const displayText = isTruncated ? originalText.substring(0, maxLength) + '...' : originalText;

    // 如果提供了截断映射引用，存储截断信息
    if (truncatedMapRef && truncatedMapRef.current) {
      truncatedMapRef.current[name] = {
        original: originalText,
        truncated: displayText,
        isTruncated
      };
    }

    return displayText;
  };
}

/**
 * 获取系列数据的原始文本映射
 * @param seriesData 系列数据数组
 * @param descriptions 描述映射对象
 * @param enableLabelCleaning 是否启用标签清理
 * @returns 原始文本映射
 */
export function getOriginalDataMap(
  seriesData: Array<{ name: string; originalName?: string }>,
  descriptions: Record<string, string> = LEGEND_DESCRIPTIONS,
  enableLabelCleaning: boolean = true
): Record<string, string> {
  const map: Record<string, string> = {};

  seriesData.forEach(series => {
    // 获取显示名称和原始名称
    const displayName = series.name;
    const originalName = series.originalName || series.name;

    // 如果启用标签清理，使用清理后的名称作为key，原始名称作为value
    if (enableLabelCleaning) {
      const labelInfo = getLabelDisplayInfo(originalName);
      const cleanDisplayName = labelInfo.displayName;
      const fullName = labelInfo.fullName;

      // 使用描述映射或完整名称
      const finalText = descriptions[displayName] || descriptions[originalName] || fullName;
      map[cleanDisplayName] = finalText;

      // 同时保留原始映射，以防需要
      if (cleanDisplayName !== displayName) {
        map[displayName] = finalText;
      }
    } else {
      // 不启用标签清理时的原始逻辑
      const originalText = descriptions[displayName] || originalName;
      map[displayName] = originalText;
    }
  });

  return map;
}

/**
 * 处理系列数据的标签清理
 * @param seriesData 原始系列数据
 * @param enableLabelCleaning 是否启用标签清理
 * @returns 处理后的系列数据
 */
export function processSeriesDataLabels(
  seriesData: Array<{ name: string; [key: string]: any }>,
  enableLabelCleaning: boolean = true
): Array<{ name: string; originalName: string; [key: string]: any }> {
  console.log('🚀 processSeriesDataLabels 开始处理', {
    enableLabelCleaning,
    seriesCount: seriesData?.length || 0,
    isArray: Array.isArray(seriesData)
  });

  if (!enableLabelCleaning || !Array.isArray(seriesData)) {
    console.log('⚪ processSeriesDataLabels: 跳过标签清理');
    return seriesData.map(series => ({
      ...series,
      originalName: series.originalName || series.name
    }));
  }

  console.log('📝 processSeriesDataLabels: 原始标签列表:', seriesData.map(s => s.name));

  const processedData = seriesData.map((series, index) => {
    console.log(`🔄 处理第 ${index + 1} 个标签: "${series.name}"`);

    const labelInfo = getLabelDisplayInfo(series.name);
    console.log(`📊 标签信息:`, labelInfo);

    const processed = {
      ...series,
      name: labelInfo.displayName,
      originalName: series.originalName || labelInfo.fullName
    };

    if (labelInfo.displayName !== series.name) {
      console.log(`✅ 标签已清理: "${series.name}" -> "${labelInfo.displayName}"`);
    } else {
      console.log(`⚪ 标签无需清理: "${series.name}"`);
    }

    return processed;
  });

  console.log('🎉 processSeriesDataLabels 处理完成:', {
    原始数量: seriesData.length,
    处理后数量: processedData.length,
    清理示例: processedData.slice(0, 3).map(s => ({
      显示: s.name,
      原始: s.originalName
    }))
  });

  return processedData;
}

/**
 * 设置图例工具提示事件监听
 * @param chartInstance ECharts实例
 * @param originalDataMap 原始数据映射
 * @param maxTextLength 最大文本长度
 */
export function setupLegendTooltip(
  chartInstance: any,
  originalDataMap: Record<string, string>,
  maxTextLength: number = 25
) {
  if (!chartInstance || !originalDataMap) {
    console.warn('setupLegendTooltip: chartInstance or originalDataMap is missing');
    return;
  }

  console.log('Setting up legend tooltip with data:', originalDataMap);

  // 创建工具提示DOM元素
  let tooltipEl: HTMLElement | null = null;

  const createTooltip = () => {
    if (tooltipEl) return tooltipEl;

    tooltipEl = document.createElement('div');
    tooltipEl.className = 'legend-tooltip';
    tooltipEl.style.cssText = `
      position: fixed;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      max-width: 300px;
      word-wrap: break-word;
      z-index: 9999;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    `;
    document.body.appendChild(tooltipEl);
    return tooltipEl;
  };

  const showTooltip = (text: string, x: number, y: number) => {
    const tooltip = createTooltip();
    tooltip.textContent = text;
    tooltip.style.left = x + 10 + 'px';
    tooltip.style.top = y - 10 + 'px';
    tooltip.style.opacity = '1';
    console.log('Tooltip shown:', text, 'at', x, y);
  };

  const hideTooltip = () => {
    if (tooltipEl) {
      tooltipEl.style.opacity = '0';
      console.log('Tooltip hidden');
    }
  };

  // 使用DOM事件监听，更可靠
  const setupDOMListeners = () => {
    // 延迟执行，确保图表已经渲染完成
    setTimeout(() => {
      const chartContainer = chartInstance.getDom();
      if (!chartContainer) {
        console.warn('Chart container not found');
        return;
      }

      console.log('Setting up DOM listeners on chart container');

      // 查找图例元素
      const findLegendItems = () => {
        return chartContainer.querySelectorAll('[data-tooltip-name]') ||
               chartContainer.querySelectorAll('.echarts-legend-item') ||
               chartContainer.querySelectorAll('text[data-name]');
      };

      // 设置图例项的事件监听
      const setupLegendItemListeners = () => {
        const legendItems = findLegendItems();
        console.log('Found legend items:', legendItems.length);

        legendItems.forEach((item: any, index: number) => {
          // 尝试获取图例名称
          const name = item.getAttribute('data-name') ||
                      item.getAttribute('data-tooltip-name') ||
                      item.textContent ||
                      Object.keys(originalDataMap)[index];

          if (!name) return;

          const originalText = originalDataMap[name];
          if (!originalText || originalText.length <= maxTextLength) return;

          console.log('Setting up listener for legend item:', name);

          item.addEventListener('mouseenter', (e: MouseEvent) => {
            console.log('Mouse enter legend item:', name);
            showTooltip(originalText, e.clientX, e.clientY);
          });

          item.addEventListener('mouseleave', () => {
            console.log('Mouse leave legend item:', name);
            hideTooltip();
          });

          // 添加样式标识
          item.style.cursor = 'help';
        });
      };

      // 初始设置
      setupLegendItemListeners();

      // 监听图表更新，重新设置监听器
      const observer = new MutationObserver(() => {
        setupLegendItemListeners();
      });

      observer.observe(chartContainer, {
        childList: true,
        subtree: true
      });

      // 返回清理函数
      return () => {
        observer.disconnect();
      };
    }, 500); // 延迟500ms确保图表渲染完成
  };

  // 同时使用ECharts事件和DOM事件
  const setupEChartsListeners = () => {
    // ECharts事件监听（作为备用方案）
    chartInstance.on('mouseover', (params: any) => {
      console.log('ECharts mouseover event:', params);
      if (params.componentType === 'legend') {
        const name = params.name;
        const originalText = originalDataMap[name];

        if (originalText && originalText.length > maxTextLength) {
          console.log('ECharts: Showing tooltip for:', name);
          const event = params.event?.event || params.event;
          if (event) {
            showTooltip(originalText, event.clientX || event.offsetX, event.clientY || event.offsetY);
          }
        }
      }
    });

    chartInstance.on('mouseout', (params: any) => {
      if (params.componentType === 'legend') {
        console.log('ECharts: Hiding tooltip');
        hideTooltip();
      }
    });
  };

  // 设置两种监听方式
  setupEChartsListeners();
  const domCleanup = setupDOMListeners();

  // 清理函数
  return () => {
    if (tooltipEl && tooltipEl.parentNode) {
      tooltipEl.parentNode.removeChild(tooltipEl);
      tooltipEl = null;
    }
    if (domCleanup) {
      domCleanup();
    }
    console.log('Legend tooltip cleanup completed');
  };
}
