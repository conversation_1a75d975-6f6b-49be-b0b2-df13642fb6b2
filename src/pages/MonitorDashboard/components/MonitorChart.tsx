import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button, Modal } from 'acud';
import { OutlinedArrowsAlt } from 'acud-icon';
import { useRequest } from 'ahooks';
import * as echarts from 'echarts/core';

import { queryMetrics } from '@/apis/monitor';
import { formatValueWithUnit, formatYAxisLabel } from '@/utils/numberFormatter';
import '@/utils/echartsInit'; // 确保ECharts组件已注册
import styles from './MonitorChart.module.less';

interface MonitorChartProps {
  title: string;
  unit: string;
  query: string;
  instanceId: string;
  timeRange: {
    start: number;
    end: number;
  };
  refreshTrigger?: any; // 用于触发刷新的依赖
  region: string; // 添加region参数
  metricType?: 'basic' | 'business'; // 添加metricType参数，默认为business
}

const MonitorChart: React.FC<MonitorChartProps> = ({
  title,
  unit,
  query,
  instanceId,
  timeRange,
  refreshTrigger,
  region,
  metricType = 'business' // 默认为business类型
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const modalChartRef = useRef<HTMLDivElement>(null);
  const modalChartInstance = useRef<echarts.ECharts | null>(null);
  const [chartData, setChartData] = useState<any[]>([]);

  console.log(`MonitorChart ${title} 渲染`, {
    instanceId,
    query,
    timeRange,
    refreshTrigger
  });

  // 查询监控数据
  const { run: fetchData, loading } = useRequest(
    () => {
      if (!instanceId || !query) {
        console.log(`${title} 跳过数据查询: instanceId=${instanceId}, query=${query}`);
        return Promise.resolve(null);
      }

      const step = Math.max(60, Math.floor((timeRange.end - timeRange.start) / 1000 / 100)); // 最少60秒步长
      
      console.log(`${title} 开始查询数据:`, {
        instanceId,
        query,
        start: Math.floor(timeRange.start / 1000),
        end: Math.floor(timeRange.end / 1000),
        step
      });

      return queryMetrics({
        instanceId,
        metricType, // 使用传入的metricType参数
        query,
        start: Math.floor(timeRange.start / 1000).toString(),
        end: Math.floor(timeRange.end / 1000).toString(),
        step: step.toString()
      }, region); // 使用传入的region参数
    },
    {
      manual: true,
      onSuccess: (res) => {
        console.log(`${title} 查询数据成功:`, res);
        // 根据实际响应结构，数据在 res.result.data.result 中
        if (res?.result?.status === 'success' && res?.result?.data?.result?.[0]?.values) {
          const processedData = res.result.data.result[0].values.map(([timestamp, value]: [number, string]) => [
            timestamp * 1000, // 转换为毫秒
            parseFloat(value) || 0
          ]);
          setChartData(processedData);
          console.log(`${title} 处理后的数据:`, processedData.slice(0, 3), '...');
        } else if (res?.result?.status === 'error') {
          console.error(`${title} 查询失败:`, res.result.error);
          setChartData([]);
        } else {
          console.log(`${title} 无数据，响应结构:`, res);
          setChartData([]);
        }
      },
      onError: (error) => {
        console.error(`${title} 查询数据失败:`, error);
        setChartData([]);
      }
    }
  );

  // 格式化数值 - 使用智能精度格式化
  const formatValue = useCallback((value: number | string | any) => {
    return formatValueWithUnit(value, unit);
  }, [unit]);

  // 初始化图表
  const initChart = useCallback(() => {
    if (!chartRef.current) return;

    // 销毁已存在的图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    chartInstance.current = echarts.init(chartRef.current);

    const option = {
      title: {
        // text: title, // 暂时不显示标题，因为图表已经在顶部显示了
        left: 'left',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          if (params && params.length > 0) {
            const point = params[0];
            const time = new Date(point.axisValue).toLocaleString();
            const value = formatValue(point.value[1]);
            return `${time}<br/>${point.seriesName}: ${value}`;
          }
          return '';
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLabel: {
          formatter: function(value: number) {
            const date = new Date(value);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');

            // 计算时间范围（小时）
            const timeRangeDuration = timeRange.end - timeRange.start;
            const hours = timeRangeDuration / (1000 * 60 * 60);

            // 如果是近1小时，且不是第一个或最后一个标签，只显示时:分
            if (hours <= 1) {
              // 获取所有刻度值来判断是否为首尾标签
              const isFirstOrLast = value === timeRange.start || value === timeRange.end;
              console.log('X轴标签格式化:', {
                value: new Date(value).toLocaleString(),
                isFirstOrLast,
                timeRangeStart: new Date(timeRange.start).toLocaleString(),
                timeRangeEnd: new Date(timeRange.end).toLocaleString(),
                hours
              });

              if (isFirstOrLast) {
                return `${month}-${day} ${hour}:${minute}`;
              } else {
                return `${hour}:${minute}`;
              }
            }

            // 其他时间范围保持原有格式
            return `${month}-${day} ${hour}:${minute}`;
          },
          color: '#666',
          fontSize: 12,
          // 控制标签间隔，避免重叠
          interval: 'auto',
          rotate: 0,
          margin: 8,
          // 根据时间范围动态调整显示间隔
          // showMaxLabel: true, // 控制是否显示最后一个标签
          // showMinLabel: true // 控制是否显示第一个标签,如果设置成true则不会X轴的标签不会均分
        },
        axisLine: {
          lineStyle: { color: '#e0e0e0' }
        },
        splitLine: {
          show: false, // 控制网格线是否显示
          lineStyle: { color: '#f0f0f0', type: 'dashed' }
        },
        // 动态控制刻度线的数量和间隔
        splitNumber: (() => {
          const timeRangeDuration = timeRange.end - timeRange.start;
          const hours = timeRangeDuration / (1000 * 60 * 60);

          if (hours <= 1) return 6; // 1小时内：6个刻度
          if (hours <= 6) return 6; // 6小时内：6个刻度
          if (hours <= 24) return 6; // 24小时内：6个刻度
          return Math.min(Math.ceil(hours / 24), 8); // 超过24小时：按天计算，最多8个
        })(),
        // 设置最小和最大刻度间隔
        minInterval: (() => {
          const timeRangeDuration = timeRange.end - timeRange.start;
          const hours = timeRangeDuration / (1000 * 60 * 60);

          if (hours <= 1) return 10 * 60 * 1000; // 1小时内：每10分钟
          if (hours <= 6) return 60 * 60 * 1000; // 6小时内：每小时
          if (hours <= 24) return 4 * 60 * 60 * 1000; // 24小时内：每4小时
          return 24 * 60 * 60 * 1000; // 超过24小时：每天
        })(),
        maxInterval: (() => {
          const timeRangeDuration = timeRange.end - timeRange.start;
          const hours = timeRangeDuration / (1000 * 60 * 60);

          if (hours <= 1) return 10 * 60 * 1000; // 1小时内：每10分钟
          if (hours <= 6) return 60 * 60 * 1000; // 6小时内：每小时
          if (hours <= 24) return 4 * 60 * 60 * 1000; // 24小时内：每4小时
          return 24 * 60 * 60 * 1000; // 超过24小时：每天
        })()
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            return formatYAxisLabel(value, unit);
          }
        }
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
          height: 30,
          bottom: 10,
          handleStyle: {
            color: '#1890ff'
          },
          textStyle: {
            color: '#1890ff'
          }
        }
      ],
      series: [
        {
          name: title,
          type: 'line',
          data: chartData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          }
        }
      ]
    };

    chartInstance.current.setOption(option);
    console.log(`${title} 图表初始化完成`);
  }, [title, chartData, formatValue, timeRange]);

  // 更新图表数据
  const updateChart = useCallback(() => {
    if (!chartInstance.current) return;

    chartInstance.current.setOption({
      series: [
        {
          data: chartData
        }
      ]
    });
    console.log(`${title} 图表数据更新完成`);
  }, [title, chartData]);

  // 打开弹窗
  const openModal = useCallback(() => {
    console.log(`${title} 打开弹窗`);
    setIsModalVisible(true);
  }, [title]);

  // 关闭弹窗
  const closeModal = useCallback(() => {
    console.log(`${title} 关闭弹窗`);
    // 清理弹窗中的图表实例
    if (modalChartInstance.current) {
      modalChartInstance.current.dispose();
      modalChartInstance.current = null;
    }
    setIsModalVisible(false);
  }, [title]);

  // 监听数据变化，触发查询
  useEffect(() => {
    if (instanceId && query && timeRange.start && timeRange.end) {
      console.log(`${title} 触发数据查询`);
      fetchData();
    } else {
      console.log(`${title} 跳过数据查询，缺少必要参数:`, {
        instanceId: !!instanceId,
        query: !!query,
        timeRange: !!(timeRange.start && timeRange.end)
      });
      // 清空数据
      setChartData([]);
    }
  }, [instanceId, query, timeRange.start, timeRange.end, refreshTrigger, fetchData, title]);

  // 初始化图表
  useEffect(() => {
    initChart();
    
    // 窗口大小变化时重新调整图表
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [initChart]);

  // 数据变化时更新图表
  useEffect(() => {
    if (chartInstance.current && chartData.length > 0) {
      updateChart();
    }
  }, [chartData, updateChart]);

  // 初始化弹窗中的图表
  const initModalChart = useCallback(() => {
    if (!modalChartRef.current || !chartData.length) return;

    if (modalChartInstance.current) {
      modalChartInstance.current.dispose();
    }

    modalChartInstance.current = echarts.init(modalChartRef.current);

    // 使用与主图表相同的配置，但调整尺寸
    const option = {
      title: {
        // text: title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          if (params && params.length > 0) {
            const dataPoint = params[0];
            const date = new Date(dataPoint.axisValue);
            const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
            // 修复：使用 dataPoint.value[1] 获取数值，与主图表保持一致
            const value = Array.isArray(dataPoint.value) ? dataPoint.value[1] : dataPoint.value;
            return `${title}<br/>${formattedDate}<br/>${formatValue(value)}`;
          }
          return '';
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%', // 为底部滑动条留出空间
        top: '5%',    // 为标题留出空间
        containLabel: true
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLabel: {
          formatter: function(value: number) {
            const date = new Date(value);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');

            const timeRangeDuration = timeRange.end - timeRange.start;
            const hours = timeRangeDuration / (1000 * 60 * 60);

            if (hours <= 1) {
              const isFirstOrLast = value === timeRange.start || value === timeRange.end;
              if (isFirstOrLast) {
                return `${month}-${day} ${hour}:${minute}`;
              } else {
                return `${hour}:${minute}`;
              }
            }

            return `${month}-${day} ${hour}:${minute}`;
          },
          color: '#666',
          fontSize: 12,
          interval: 'auto',
          rotate: 0,
          margin: 8
        },
        axisLine: {
          lineStyle: { color: '#e0e0e0' }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            return formatYAxisLabel(value, unit);
          }
        }
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
          bottom: 10,
          height: 20,
          handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#2468F2',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          textStyle: {
            color: '#666'
          },
          borderColor: '#ddd',
          fillerColor: 'rgba(36, 104, 242, 0.2)',
          backgroundColor: '#f8f8f8'
        },
        {
          type: 'inside',
          xAxisIndex: [0],
          start: 0,
          end: 100
        }
      ],
      series: [{
        name: title,
        type: 'line',
        data: chartData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: '#2468F2'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(36, 104, 242, 0.3)'
            }, {
              offset: 1, color: 'rgba(36, 104, 242, 0.1)'
            }]
          }
        }
      }]
    };

    modalChartInstance.current.setOption(option);
  }, [title, chartData, formatValue, timeRange]);

  // 当弹窗打开时初始化图表
  useEffect(() => {
    if (isModalVisible) {
      setTimeout(() => {
        initModalChart();
      }, 100);
    }
  }, [isModalVisible, initModalChart]);

  // 监听窗口大小变化，调整弹窗中的图表大小
  useEffect(() => {
    const handleResize = () => {
      if (isModalVisible && modalChartInstance.current) {
        setTimeout(() => {
          modalChartInstance.current?.resize();
        }, 100);
      }
    };

    if (isModalVisible) {
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [isModalVisible]);

  return (
    <>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.title}>{title}</div>
          <div className={styles.actions}>
            <Button
              type="text"
              icon={<OutlinedArrowsAlt />}
              onClick={openModal}
            />
          </div>
        </div>
        <div
          ref={chartRef}
          className={styles.chart}
          style={{
            height: '300px',
            opacity: loading ? 0.6 : 1
          }}
        />
        {loading && (
          <div className={styles.loading}>
            数据加载中...
          </div>
        )}
        {!loading && chartData.length === 0 && instanceId && (
          <div className={styles.noData}>
            暂无数据
          </div>
        )}
        {!instanceId && (
          <div className={styles.noData}>
            请选择网关实例
          </div>
        )}
      </div>

      {/* 弹窗显示放大的图表 */}
      <Modal
        title={title}
        visible={isModalVisible}
        onCancel={closeModal}
        footer={null}
        destroyOnClose={true}
        width='1000px'
        style={{ height: '70vh', overflow: 'hidden' }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%'
          }}
        >
          <div
            ref={modalChartRef}
            style={{
              height: '80%', // 图表高度缩小到80%
              width: '100%'   // 图表宽度缩小到80%
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default MonitorChart;
