import React, {FC, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {Tabs} from 'acud';
import cn from 'classnames';

import './index.less';

const {TabPane} = Tabs;
interface PaneProps {
  key: string;
  tab: string;
  url?: string;
  content: React.ReactNode;
}
export interface TabProps {
  mode?: 'horizontal' | 'vertical';
  isSwitchReRender?: boolean;
  panesData: Array<PaneProps>;
}
const TabPage: FC<TabProps> = (props) => {
  const navigate = useNavigate();
  const {mode = 'horizontal', panesData, isSwitchReRender = true} = props;
  const hash = location.hash;
  const pageUrl = hash.split('#')?.[1]?.split('?')?.[0];
  const [activeKey, setActiveKey] = useState(panesData[0].key);

  const initUrlActiveKey = (url: string, ak: string) => {
    const query = location?.hash?.split('?')?.[1] || '';
    navigate(`${url + (query ? `?${query}` : '')}`);
    setActiveKey(ak);
  };

  useEffect(() => {
    if (pageUrl) {
      const targetTabPane = panesData.find(
        (pane: any) => pane.key.split(',').indexOf(pageUrl) > -1
      );
      if (targetTabPane) {
        initUrlActiveKey(pageUrl, targetTabPane.key);
      }
    }
  }, [panesData, hash]);

  const handleTabsChange = (key: string) => {
    const urlKeyArr = key.split(',');
    const urlKey = urlKeyArr[0];
    initUrlActiveKey(urlKey, key);
  };
  return (
    <div className="tab-widget">
      <Tabs
        className={cn({'tab-vertical-widget': mode === 'vertical'})}
        activeKey={activeKey}
        onChange={handleTabsChange}
      >
        {panesData.map((pane: any) => (
          <TabPane tab={pane.tab} key={pane.key}>
            {isSwitchReRender
              ? pane.key === activeKey
                ? pane.content
                : null
              : pane.content}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};
export default TabPage;
