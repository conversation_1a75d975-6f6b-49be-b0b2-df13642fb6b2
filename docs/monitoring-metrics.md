# 监控指标配置文档

## 概述

本文档详细说明了AI网关监控系统中各个指标的含义、计算方法、正常范围和告警建议。

## 动态Step配置

系统根据查询时间范围自动调整数据采样间隔：

- **1小时内**: step = 1分钟 (60秒)
- **1-6小时**: step = 5分钟 (300秒)  
- **6-24小时**: step = 15分钟 (900秒)
- **1-7天**: step = 1小时 (3600秒)
- **7天以上**: step = 6小时 (21600秒)

## 网关维度监控指标

### 请求相关指标组

#### 1. 请求成功率 (success-rate)
- **含义**: 成功请求数占总请求数的百分比
- **计算方法**: `sum(2xx响应码请求) / sum(所有请求) * 100%`
- **正常范围**: > 95%
- **告警阈值**: < 90%时告警
- **业务意义**: 反映网关整体服务质量和稳定性

#### 2. 请求成功数 (request-success-count)
- **含义**: 在指定时间范围内成功处理的请求总数
- **计算方法**: `increase(istio_requests_total{response_code=~"2.."}[5m])`
- **正常范围**: 根据业务量确定
- **告警阈值**: 根据历史数据设置下限
- **业务意义**: 监控网关处理能力和业务量趋势

#### 3. QPS (qps)
- **含义**: 每秒处理的请求数量
- **计算方法**: `rate(istio_requests_total[5m])`
- **正常范围**: 根据业务峰值确定
- **告警阈值**: 根据容量规划设置上限
- **业务意义**: 反映网关的实时负载

#### 4. 请求响应时间 (response-time)
- **含义**: 请求处理时延分布
- **计算方法**: 基于histogram_quantile计算P50/P90/P99/Avg
- **正常范围**: P99 < 1000ms, P90 < 500ms
- **告警阈值**: P99 > 2000ms时告警
- **业务意义**: 反映网关性能和用户体验

### 流量相关指标组

#### 5. 网关入流量 (gateway-inbound-traffic)
- **含义**: 网关接收的入站流量速率
- **计算方法**: `rate(istio_request_bytes_sum[5m])`
- **单位**: bytes/s
- **业务意义**: 监控网关的流量负载和带宽使用情况

#### 6. 网关出流量 (gateway-outbound-traffic)
- **含义**: 网关发送的出站流量速率
- **计算方法**: `rate(istio_response_bytes_sum[5m])`
- **单位**: bytes/s
- **业务意义**: 与入流量配合监控网关的整体流量情况

#### 7. TCP接收数 (tcp-receive-bytes)
- **含义**: TCP层面的数据接收速率
- **计算方法**: `irate(envoy_http_downstream_cx_rx_bytes_total[5m])`
- **单位**: bytes/s
- **业务意义**: 监控底层网络连接的数据传输情况

#### 8. TCP发送数 (tcp-transmit-bytes)
- **含义**: TCP层面的数据发送速率
- **计算方法**: `irate(envoy_http_downstream_cx_tx_bytes_total[5m])`
- **单位**: bytes/s
- **业务意义**: 与TCP接收数配合监控网络传输性能

### 连接相关指标组

#### 9. TCP连接数 (tcp-connections)
- **含义**: 当前活跃的TCP连接数量
- **计算方法**: `envoy_cluster_upstream_cx_active`
- **正常范围**: 根据并发用户数确定
- **告警阈值**: 过高可能表示连接泄漏或负载过大
- **业务意义**: 监控连接池使用情况

### 状态码分布指标组

#### 10. 网关状态码分布 (gateway-status-code)
- **含义**: 网关返回的HTTP状态码分布
- **计算方法**: `rate(istio_requests_total[5m]) by (response_code)`
- **业务意义**: 2xx表示成功，4xx表示客户端错误，5xx表示服务端错误

#### 11. 后端服务状态码分布 (backend-status-code)
- **含义**: 后端服务返回的状态码分布，按服务名称分组
- **计算方法**: `rate(istio_requests_total[5m]) by (response_code,destination_service_name)`
- **业务意义**: 用于定位具体服务的错误情况

### 服务访问统计指标组

#### 12. 服务访问量排行 (service-access-ranking)
- **含义**: 各后端服务的访问量排行
- **计算方法**: `rate(istio_requests_total[5m]) by (destination_service_name)`
- **业务意义**: 了解业务热点和负载分布情况

## 路由维度监控指标

### Token基础消耗指标组

#### 1. 输入Token数 (input-token-count)
- **含义**: 累计消耗的输入Token总数
- **计算方法**: `sum by (ai_route) (route_upstream_model_consumer_metric_input_token)`
- **业务意义**: 监控模型输入的数据量和成本控制

#### 2. 输出Token数 (output-token-count)
- **含义**: 累计生成的输出Token总数
- **计算方法**: `sum by (ai_route) (route_upstream_model_consumer_metric_output_token)`
- **业务意义**: 监控模型输出的数据量和响应长度

#### 3. 输入Token每秒消耗数 (input-token-consumption-rate)
- **含义**: 输入Token的实时消耗速率
- **计算方法**: `rate(route_upstream_model_consumer_metric_input_token[5m])`
- **业务意义**: 监控模型调用频率和输入数据流量

#### 4. 输出Token每秒消耗数 (output-token-consumption-rate)
- **含义**: 输出Token的实时生成速率
- **计算方法**: `rate(route_upstream_model_consumer_metric_output_token[5m])`
- **业务意义**: 监控模型响应速度和输出数据流量

### Token消费者统计指标组

#### 5-6. 消费者Token使用统计（输入/输出）
- **含义**: 按消费者维度统计的Token使用量
- **计算方法**: `sum by (ai_route, ai_consumer) (route_upstream_model_consumer_metric_*_token)`
- **业务意义**: 分析不同消费者的使用模式和成本分摊

### Token服务统计指标组

#### 7-8. 服务Token使用统计（输入/输出）
- **含义**: 按服务维度统计的Token使用量
- **计算方法**: `sum by (ai_route, ai_cluster) (route_upstream_model_consumer_metric_*_token)`
- **业务意义**: 分析不同后端服务的Token消耗情况

### Token模型统计指标组

#### 9-10. 模型Token使用统计（输入/输出）
- **含义**: 按模型维度统计的Token使用量
- **计算方法**: `sum by (ai_route, ai_model) (route_upstream_model_consumer_metric_*_token)`
- **业务意义**: 分析不同AI模型的使用情况和性能对比

## 服务维度监控指标

### 请求相关指标组

#### 1. 请求成功数 (request-success-count)
- **含义**: 按服务统计的成功请求数量
- **计算方法**: `increase(istio_requests_total{response_code=~"2.."}[5m]) by (destination_service_name)`
- **业务意义**: 监控各服务的处理能力和业务量分布

#### 2. 请求成功率 (request-success-rate)
- **含义**: 按服务统计的请求成功率
- **正常范围**: > 95%
- **业务意义**: 监控各服务的稳定性和错误率

#### 3. 每秒请求数QPS (qps-service)
- **含义**: 按服务统计的每秒请求数
- **业务意义**: 监控各服务的实时负载和流量分布

#### 4. 请求平均时延RT (request-avg-latency-service)
- **含义**: 按服务统计的平均响应时间
- **正常范围**: < 500ms
- **业务意义**: 监控各服务的性能表现

### 流量相关指标组

#### 5-6. 入站/出站流量
- **含义**: 按服务统计的流量速率
- **业务意义**: 监控各服务的数据传输量和带宽使用

### 连接相关指标组

#### 7. TCP连接数 (tcp-connections-service)
- **含义**: 按服务统计的TCP连接数
- **业务意义**: 监控各服务的连接池使用情况和连接泄漏

### Token相关指标组

#### 8-15. Token消耗和统计指标
- **含义**: 按服务维度的各种Token使用统计
- **业务意义**: 分析各服务的AI模型使用情况和成本分布

## 图表样式说明

### 基础指标 (metricType: 'basic')
- 使用普通折线图
- 无面积填充
- 适用于基础监控指标

### 业务指标 (metricType: 'business')
- 使用面积图样式
- 渐变色填充，增强视觉效果
- 适用于业务核心指标
- 状态码分布和响应时间除外（保持折线图）

## 告警建议

### 关键指标告警阈值
1. **请求成功率** < 90%
2. **P99响应时间** > 2000ms
3. **QPS异常** 超过历史峰值的150%或低于正常值的50%
4. **TCP连接数** 超过配置上限的80%
5. **5xx错误率** > 5%

### 告警级别
- **P0**: 请求成功率 < 85%，P99 > 5000ms
- **P1**: 请求成功率 < 90%，P99 > 2000ms
- **P2**: QPS异常，连接数过高
- **P3**: 其他性能指标异常

## 更新日志

### 2024-07-30
- 实现动态step配置
- 添加业务指标面积图样式
- 重新组织指标面板分组
- 添加详细的指标说明文档
- 新增请求成功数指标到网关维度
