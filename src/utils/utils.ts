import moment from 'moment';

import {CookieService} from './cookie';

export const isSubUser = () => {
  return !!CookieService.get('bce-subuser-info');
};
/**
 * 给数字增加千位分割符
 */
export function addThousandSeparator(num: number | string) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/** 生成随机报表的名称 */
export function genRandomReportName() {
  let result = '';
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return `ar-${result}`;
}

/** 根据文件路径获取文件名称 */
export function getFileNameFromPath(filePath: string) {
  const pathArray = filePath.split('/');
  const fileName = pathArray[pathArray.length - 1];
  return fileName;
}

export const formatTime = (time: string) => {
  if (
    !time ||
    time === '0001-01-01T00:00:00Z' ||
    time === '0001-01-01 00:00:00'
  ) {
    return '-';
  }
  return moment.utc(time).utcOffset(8).format('YYYY-MM-DD HH:mm:ss');
};

/** 默认最小页码 */
const DEFAULT_MIN_PAGE_SIZE = 10;
/** 获取分页器显示状态 */
export const getPaginationVisible = (
  total: number,
  minPageSize = DEFAULT_MIN_PAGE_SIZE
) => total > minPageSize;

const toNumberValue = (v) => (v ? parseInt(v, 10) : 0);

export const compareVersion = (source, dest) => {
  const srcArr = source.split('.');
  const destArr = dest.split('.');
  const index = Math.max(srcArr.length, destArr.length);
  let result = false;
  for (let i = 0; i < index; i++) {
    const src = toNumberValue(srcArr[i]);
    const dst = toNumberValue(destArr[i]);
    if (src > dst) {
      result = true;
      break;
    }
    if (src === dst) {
      continue;
    }
    break;
  }
  return result;
};
