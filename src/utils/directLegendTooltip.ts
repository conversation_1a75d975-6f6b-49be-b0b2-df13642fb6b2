/**
 * 直接的图例工具提示实现
 * 通过修改ECharts配置直接支持图例工具提示
 */

/**
 * 创建带工具提示的图例格式化函数
 * @param originalDataMap 原始数据映射
 * @param maxTextLength 最大文本长度
 * @param descriptions 描述映射
 */
export function createTooltipFormatter(
  originalDataMap: Record<string, string>,
  maxTextLength: number = 25,
  descriptions: Record<string, string> = {}
) {
  return function(name: string) {
    // 获取原始文本
    const originalText = descriptions[name] || originalDataMap[name] || name;
    
    // 判断是否需要截断
    const isTruncated = originalText.length > maxTextLength;
    const displayText = isTruncated ? originalText.substring(0, maxTextLength) + '...' : originalText;
    
    // 如果被截断，返回带title属性的HTML
    if (isTruncated) {
      return `<span title="${originalText}" style="cursor: help;">${displayText}</span>`;
    }
    
    return displayText;
  };
}

/**
 * 设置图例DOM工具提示
 * @param chartInstance ECharts实例
 * @param originalDataMap 原始数据映射
 * @param maxTextLength 最大文本长度
 */
export function setupDirectLegendTooltip(
  chartInstance: any,
  originalDataMap: Record<string, string>,
  maxTextLength: number = 25
) {
  if (!chartInstance || !originalDataMap) {
    console.warn('setupDirectLegendTooltip: missing parameters');
    return;
  }

  console.log('Setting up direct legend tooltip');

  // 等待图表渲染完成
  const setupTooltips = () => {
    setTimeout(() => {
      const chartDom = chartInstance.getDom();
      if (!chartDom) {
        console.warn('Chart DOM not found');
        return;
      }

      console.log('Chart DOM found, setting up tooltips');

      // 查找所有文本元素
      const textElements = chartDom.querySelectorAll('text');
      console.log(`Found ${textElements.length} text elements`);

      textElements.forEach((textEl: any, index: number) => {
        const textContent = textEl.textContent || textEl.innerText;
        if (!textContent) return;

        // 检查是否是被截断的图例文本
        if (textContent.includes('...')) {
          // 查找对应的原始文本
          const matchedKey = Object.keys(originalDataMap).find(key => {
            const originalText = originalDataMap[key];
            const truncatedText = originalText.substring(0, maxTextLength);
            return textContent.startsWith(truncatedText) || 
                   originalText.toLowerCase().includes(textContent.replace('...', '').toLowerCase());
          });

          if (matchedKey) {
            const originalText = originalDataMap[matchedKey];
            console.log(`Adding tooltip for: "${textContent}" -> "${originalText}"`);
            
            // 添加title属性
            textEl.setAttribute('title', originalText);
            textEl.style.cursor = 'help';
            
            // 添加自定义工具提示
            addCustomTooltip(textEl, originalText);
          }
        }
      });

      // 也检查SVG元素
      const svgTexts = chartDom.querySelectorAll('svg text');
      console.log(`Found ${svgTexts.length} SVG text elements`);
      
      svgTexts.forEach((textEl: any) => {
        const textContent = textEl.textContent || textEl.innerText;
        if (!textContent || !textContent.includes('...')) return;

        Object.keys(originalDataMap).forEach(key => {
          const originalText = originalDataMap[key];
          if (originalText.length > maxTextLength) {
            const truncatedText = originalText.substring(0, maxTextLength);
            if (textContent.startsWith(truncatedText)) {
              console.log(`Adding SVG tooltip for: "${textContent}" -> "${originalText}"`);
              textEl.setAttribute('title', originalText);
              textEl.style.cursor = 'help';
              addCustomTooltip(textEl, originalText);
            }
          }
        });
      });
    }, 1500); // 增加延迟确保图表完全渲染
  };

  // 添加自定义工具提示
  const addCustomTooltip = (element: HTMLElement, text: string) => {
    let tooltipEl: HTMLElement | null = null;

    const showTooltip = (e: MouseEvent) => {
      if (tooltipEl) {
        document.body.removeChild(tooltipEl);
      }

      tooltipEl = document.createElement('div');
      tooltipEl.className = 'custom-legend-tooltip';
      tooltipEl.style.cssText = `
        position: fixed;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        max-width: 300px;
        word-wrap: break-word;
        z-index: 9999;
        pointer-events: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      `;
      tooltipEl.textContent = text;
      tooltipEl.style.left = e.clientX + 10 + 'px';
      tooltipEl.style.top = e.clientY - 10 + 'px';
      
      document.body.appendChild(tooltipEl);
      console.log('Custom tooltip shown:', text);
    };

    const hideTooltip = () => {
      if (tooltipEl && tooltipEl.parentNode) {
        tooltipEl.parentNode.removeChild(tooltipEl);
        tooltipEl = null;
        console.log('Custom tooltip hidden');
      }
    };

    element.addEventListener('mouseenter', showTooltip);
    element.addEventListener('mouseleave', hideTooltip);
  };

  // 初始设置
  setupTooltips();

  // 监听图表更新
  chartInstance.on('finished', () => {
    console.log('Chart finished, re-setting up tooltips');
    setupTooltips();
  });

  // 监听图表重绘
  chartInstance.on('rendered', () => {
    console.log('Chart rendered, re-setting up tooltips');
    setupTooltips();
  });

  // 返回清理函数
  return () => {
    console.log('Direct legend tooltip cleanup');
  };
}

/**
 * 强制设置图例工具提示（最后的备用方案）
 * @param chartInstance ECharts实例
 * @param originalDataMap 原始数据映射
 * @param maxTextLength 最大文本长度
 */
export function forceSetupLegendTooltip(
  chartInstance: any,
  originalDataMap: Record<string, string>,
  maxTextLength: number = 25
) {
  if (!chartInstance || !originalDataMap) return;

  console.log('Force setting up legend tooltip');

  // 多次尝试设置工具提示
  const attempts = [500, 1000, 2000, 3000];
  
  attempts.forEach(delay => {
    setTimeout(() => {
      setupDirectLegendTooltip(chartInstance, originalDataMap, maxTextLength);
    }, delay);
  });

  // 监听窗口大小变化
  const resizeHandler = () => {
    setTimeout(() => {
      setupDirectLegendTooltip(chartInstance, originalDataMap, maxTextLength);
    }, 500);
  };

  window.addEventListener('resize', resizeHandler);

  return () => {
    window.removeEventListener('resize', resizeHandler);
  };
}
