现在，我需要开发 1 个接口（关联集群），你需要给每个接口中的参数起字段名，并规定这个字段的相关格式和限制。写 1 份接口文档，以 md 格式存储，存储在接口文档文件夹中，如果没有这个文件夹你需要新建一个。

1. 关联集群的接口路径为：/api/aigw/v1/aigateway/instance/{instanceId}/clusterList

2. 需要根据实例的 ID 将选择的 CCE 集群数组传给接口，CCE 集群数组的格式参考：
	1. {
	2.     "clusters": [
	3.         {
	4.             "clusterId": "cce-qhjz40ds",
	5.             "clusterName": "zhoujun-user-1"
	6.         }
	7.     ]
	8. }

|   |   |   |
|---|---|---|
|**参数**|**说明**|**相关限制**|
|所属实例|回显当前网关实例名称以及实例 ID||
|所属 VPC|回显创建网关实例时选择的 VPC||
|备注|选填，可输入本次关联集群的备注信息，若选择了多个集群，则这一批次的集群备注信息一样|1. 至多 64 个字符|
|CCE 集群|必填|1. 关联集群总数有限制，仅支持至多 1 个2. 需要和网关实例在同一地域、同一VPC下|