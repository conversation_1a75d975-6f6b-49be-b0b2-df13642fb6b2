import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

import {BASE_URL} from './index';

export interface ServiceInstanceItemType {
  id: string;
  host: string;
  isolateEnable: boolean;
  service: string;
}

/**
 * 获取服务列表
 */
export function getServiceAdminList(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/services`,
    params: data
  });
}

/**
 * 获取服务实例列表
 */
export function getServiceInstanceList(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/service/${data.serviceId}/serviceInstances`,
    params: data
  });
}

/**
 * 创建服务实例
 */
export function createServiceInstance(
  param: any,
  data: any
): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${param.instanceId}/service/${param.serviceId}/serviceInstance`,
    data,
    method: 'POST'
  });
}

/**
 * 创建服务实例
 */
export function editServiceInstance(
  param: any,
  data?: any
): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${param.instanceId}/service/${param.serviceId}/serviceInstance/${param.serviceInstanceId}`,
    data,
    method: 'PUT'
  });
}

/**
 * 删除服务实例
 */
export function deleteServiceInstance(param: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${param.instanceId}/service/${param.serviceId}/serviceInstance/${param.serviceInstanceId}`,
    method: 'DELETE'
  });
}

/**
 * 删除服务实例
 */
export function batchUpdateServiceInstance(
  param: any,
  data: any
): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${param.instanceId}/service/${param.serviceId}/serviceInstances?Action=${param.action}`,
    method: 'POST',
    data
  });
}
