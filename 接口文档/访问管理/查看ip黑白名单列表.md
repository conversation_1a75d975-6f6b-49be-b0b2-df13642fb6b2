# 查看IP黑白名单列表接口

## 接口基本信息

- **接口说明**：查询指定实例下的IP黑白名单规则列表
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/ipRestrictionList`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 查询参数

| 参数名      | 类型 | 是否必填 | 描述                                | 示例值 |
|----------| --- | --- |-----------------------------------| --- |
| name     | String | 否 | 过滤类型，规则名称，支持模糊查询                  |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": [
        {
            "id": "ip-restriction-001",
            "name": "internal-whitelist",
            "enabled": true,
            "type": "whitelist",
            "scope": "global",
            "ipAddresses": [
                "***********/24",
                "********"
            ]
        },
        {
            "id": "ip-restriction-002",
            "name": "external-blacklist",
            "enabled": false,
            "type": "blacklist",
            "scope": "global",
            "ipAddresses": [
                "*******",
                "*******/24"
            ]
        },
        {
            "id": "ip-restriction-003",
            "name": "test-whitelist",
            "enabled": true,
            "type": "whitelist",
            "scope": "global",
            "ipAddresses": [
                "**********/16"
            ]
        }
    ]
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Array | IP黑白名单列表数据 |
| result[].id | String | IP黑白名单规则ID |
| result[].name | String | 名称 |
| result[].enabled | Boolean | 启用状态，true表示启用，false表示禁用 |
| result[].type | String | 类型，whitelist表示白名单，blacklist表示黑名单 |
| result[].scope | String | 生效粒度 |
| result[].ipAddresses | Array[String] | IP地址/地址段列表 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/gw-ist9vvin/ipRestrictionList?name=whitelist
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": [
        {
            "id": "ip-restriction-001",
            "name": "internal-whitelist",
            "enabled": true,
            "type": "whitelist",
            "scope": "global",
            "ipAddresses": [
                "***********/24",
                "********"
            ]
        },
        {
            "id": "ip-restriction-002",
            "name": "external-blacklist",
            "enabled": false,
            "type": "blacklist",
            "scope": "global",
            "ipAddresses": [
                "*******",
                "*******/24"
            ]
        },
        {
            "id": "ip-restriction-003",
            "name": "test-whitelist",
            "enabled": true,
            "type": "whitelist",
            "scope": "global",
            "ipAddresses": [
                "**********/16"
            ]
        }
    ]
}
``` 