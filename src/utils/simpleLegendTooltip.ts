/**
 * 简单的图例工具提示实现
 * 通过DOM操作直接为图例元素添加原生title属性
 */

/**
 * 为图例添加简单的工具提示
 * @param chartInstance ECharts实例
 * @param originalDataMap 原始数据映射
 * @param maxTextLength 最大文本长度
 */
export function addSimpleLegendTooltip(
  chartInstance: any,
  originalDataMap: Record<string, string>,
  maxTextLength: number = 25
) {
  if (!chartInstance || !originalDataMap) {
    console.warn('addSimpleLegendTooltip: chartInstance or originalDataMap is missing');
    return;
  }

  console.log('Adding simple legend tooltip with data:', originalDataMap);

  const setupTooltips = () => {
    setTimeout(() => {
      const chartContainer = chartInstance.getDom();
      if (!chartContainer) {
        console.warn('Chart container not found');
        return;
      }

      // 查找所有可能的图例文本元素
      const textElements = chartContainer.querySelectorAll('text');
      console.log('Found text elements:', textElements.length);

      textElements.forEach((textEl: any) => {
        const textContent = textEl.textContent || textEl.innerText;
        if (!textContent) return;

        // 检查是否是图例文本（通过内容匹配）
        const matchedKey = Object.keys(originalDataMap).find(key => {
          const originalText = originalDataMap[key];
          // 检查是否是截断后的文本
          return textContent.includes('...') && originalText.startsWith(textContent.replace('...', ''));
        });

        if (matchedKey) {
          const originalText = originalDataMap[matchedKey];
          if (originalText.length > maxTextLength) {
            // 添加原生title属性
            textEl.setAttribute('title', originalText);
            textEl.style.cursor = 'help';
            console.log('Added tooltip for:', textContent, '->', originalText);
          }
        }
      });

      // 也尝试查找SVG中的其他可能的图例元素
      const svgElements = chartContainer.querySelectorAll('g[clip-path] text, .echarts-legend text');
      svgElements.forEach((el: any) => {
        const textContent = el.textContent || el.innerText;
        if (!textContent) return;

        Object.keys(originalDataMap).forEach(key => {
          const originalText = originalDataMap[key];
          if (textContent.includes('...') && originalText.length > maxTextLength) {
            el.setAttribute('title', originalText);
            el.style.cursor = 'help';
            console.log('Added SVG tooltip for:', textContent, '->', originalText);
          }
        });
      });
    }, 1000); // 增加延迟确保图表完全渲染
  };

  // 初始设置
  setupTooltips();

  // 监听图表更新事件
  chartInstance.on('finished', () => {
    console.log('Chart finished rendering, setting up tooltips');
    setupTooltips();
  });

  // 返回清理函数
  return () => {
    console.log('Simple legend tooltip cleanup');
  };
}

/**
 * 创建自定义工具提示元素
 * @param chartInstance ECharts实例
 * @param originalDataMap 原始数据映射
 * @param maxTextLength 最大文本长度
 */
export function createCustomLegendTooltip(
  chartInstance: any,
  originalDataMap: Record<string, string>,
  maxTextLength: number = 25
) {
  if (!chartInstance || !originalDataMap) return;

  let tooltipEl: HTMLElement | null = null;

  const createTooltip = () => {
    if (tooltipEl) return tooltipEl;
    
    tooltipEl = document.createElement('div');
    tooltipEl.className = 'custom-legend-tooltip';
    tooltipEl.style.cssText = `
      position: fixed;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      max-width: 300px;
      word-wrap: break-word;
      z-index: 9999;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    `;
    document.body.appendChild(tooltipEl);
    return tooltipEl;
  };

  const showTooltip = (text: string, x: number, y: number) => {
    const tooltip = createTooltip();
    tooltip.textContent = text;
    tooltip.style.left = x + 10 + 'px';
    tooltip.style.top = y - 10 + 'px';
    tooltip.style.opacity = '1';
  };

  const hideTooltip = () => {
    if (tooltipEl) {
      tooltipEl.style.opacity = '0';
    }
  };

  const setupCustomTooltips = () => {
    setTimeout(() => {
      const chartContainer = chartInstance.getDom();
      if (!chartContainer) return;

      // 移除之前的监听器
      chartContainer.removeEventListener('mouseover', handleMouseOver);
      chartContainer.removeEventListener('mouseout', handleMouseOut);

      // 添加事件监听器
      chartContainer.addEventListener('mouseover', handleMouseOver);
      chartContainer.addEventListener('mouseout', handleMouseOut);
    }, 1000);
  };

  const handleMouseOver = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target) return;

    const textContent = target.textContent || target.innerText;
    if (!textContent || !textContent.includes('...')) return;

    // 查找匹配的原始文本
    const matchedKey = Object.keys(originalDataMap).find(key => {
      const originalText = originalDataMap[key];
      return originalText.length > maxTextLength && 
             (textContent.includes(originalText.substring(0, maxTextLength - 3)) ||
              originalText.includes(textContent.replace('...', '')));
    });

    if (matchedKey) {
      const originalText = originalDataMap[matchedKey];
      showTooltip(originalText, e.clientX, e.clientY);
    }
  };

  const handleMouseOut = (e: MouseEvent) => {
    hideTooltip();
  };

  // 初始设置
  setupCustomTooltips();

  // 监听图表更新
  chartInstance.on('finished', setupCustomTooltips);

  // 返回清理函数
  return () => {
    const chartContainer = chartInstance.getDom();
    if (chartContainer) {
      chartContainer.removeEventListener('mouseover', handleMouseOver);
      chartContainer.removeEventListener('mouseout', handleMouseOut);
    }
    if (tooltipEl && tooltipEl.parentNode) {
      tooltipEl.parentNode.removeChild(tooltipEl);
      tooltipEl = null;
    }
  };
}
