.container {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px 12px;
  // border-bottom: 1px solid #F0F1F2;
}

.titleSection {
  flex: 1;
  min-width: 0;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: #1D2129;
  line-height: 22px;
  margin-bottom: 4px;
}

.description {
  font-size: 12px;
  color: #86909C;
  line-height: 18px;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  transition: color 0.2s ease;
  cursor: help;

  &:hover {
    color: #5C5F66;
  }
}

.actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}



.chart {
  width: 100%;
  position: relative;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #86909C;
  font-size: 14px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 4px;
}

// 响应式样式
@media (max-width: 768px) {
  .header {
    padding: 12px 16px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .titleSection {
    width: 100%;
  }

  .actions {
    width: 100%;
    justify-content: flex-end;
  }



  .description {
    font-size: 11px;
    line-height: 16px;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #86909C;
  font-size: 14px;
  z-index: 10;
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    padding: 12px 16px 8px;
  }
  
  .title {
    font-size: 13px;
  }
  
  .chart {
    height: 250px !important;
  }
}
