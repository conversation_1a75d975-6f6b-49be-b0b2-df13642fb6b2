# 根据集群ID查询命名空间列表接口（查询集群中命名空间列表）

## 接口基本信息

- **接口说明**：根据集群ID查询集群中所有命名空间
- **接口地址**：`/api/aigw/v1/aigateway/cluster/namespace`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| clusterId | String | 是 | 集群ID | cce-qhjz40ds |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": [
        "default",
        "kube-system",
        "istio-system",
        "monitoring"
    ]
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Array | 命名空间名称列表 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查集群ID格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询集群命名空间的权限 |
| 404 | 集群不存在 | 确认集群ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/cluster/namespace?clusterId=cce-qhjz40ds
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": [
        "default",
        "kube-system",
        "istio-system",
        "monitoring",
        "app-system",
        "ingress-nginx"
    ]
}
```

## 注意事项

1. 集群ID必须符合规范格式，通常以特定前缀（如`cce-`）开头
2. 接口仅返回命名空间名称列表，不包含命名空间的详细信息
3. 返回结果中的命名空间列表按字母顺序排序
4. 如果集群中没有命名空间，将返回空数组`[]`
5. 用户需要具有对应集群的查询权限才能获取命名空间列表 