# 更新实例接口

## 接口基本信息

- **接口说明**：更新AI网关实例的基本信息
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}`
- **请求方式**：PUT
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |
| Content-Type | String | 是 | 内容类型 | application/json |

### 请求体

```json
{
    "name": "gateway-test-updated",
    "description": "测试环境网关-已更新",
    "deleteProtection": true,
    "publicAccessible": false
}
```

### 请求体字段说明

| 字段名 | 类型 | 是否必填 | 描述 | 示例值 | 限制 |
| --- | --- | --- | --- | --- | --- |
| name | String | 否 | 实例名称 | gateway-test-updated | 1. 必须以字母或中文开头<br>2. 支持大小写字母、数字、中文以及-_/.特殊字符<br>3. 长度范围：1-64个字符<br>4. 不能与已有网关名称重复|
| description | String | 否 | 实例描述 | 测试环境网关-已更新 | 最多64个字符 |
| deleteProtection | Boolean | 否 | 是否启用删除保护 | true | true: 启用, false: 不启用 |
| publicAccessible | Boolean | 否 | 是否允许公网访问 | false | true: 允许, false: 不允许 |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": {
        "instanceId": "gw-ist9vvin",
        "name": "gateway-test-updated",
        "description": "测试环境网关-已更新",
        "deleteProtection": true,
        "publicAccessible": false,
        "updateTime": "2025-04-17 14:30:25"
    }
}
```

### 错误响应体结构

```json
{
    "success": false,
    "status": 400,
    "message": "实例名称格式不正确"
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 更新后的实例信息 |
| result.instanceId | String | 实例ID |
| result.name | String | 更新后的实例名称 |
| result.description | String | 更新后的实例描述 |
| result.deleteProtection | Boolean | 更新后的删除保护设置 |
| result.publicAccessible | Boolean | 更新后的公网访问设置 |
| result.updateTime | String | 更新时间，格式：YYYY-MM-DD HH:mm:ss |
| message | String | 错误信息，仅在失败时返回 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有更新实例的权限 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 409 | 资源冲突 | 实例可能正在被其他操作修改，请稍后重试 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
PUT /api/aigw/v1/aigateway/gw-ist9vvin
X-Region: gz
Content-Type: application/json

{
    "name": "gateway-test-updated",
    "description": "测试环境网关-已更新",
    "deleteProtection": true,
    "publicAccessible": false
}
```

## 响应示例

### 成功响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "instanceId": "gw-ist9vvin",
        "name": "gateway-test-updated",
        "description": "测试环境网关-已更新",
        "deleteProtection": true,
        "publicAccessible": false,
        "updateTime": "2025-04-17 14:30:25"
    }
}
```

### 错误响应示例

```json
{
    "success": false,
    "status": 400,
    "message": "实例名称格式不正确"
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 请求体中的字段都是可选的，只需要包含需要更新的字段
3. 如果某个字段不需要更新，可以不在请求体中包含该字段
4. 更新公网访问（publicAccessible）的设置可能会有以下影响：
   - 当从`true`变更为`false`时，将会释放公网负载均衡资源，外部将无法通过公网访问网关
   - 当从`false`变更为`true`时，将会创建公网负载均衡资源，可能需要额外付费
5. 当开启删除保护（deleteProtection=true）后，实例将无法被删除，需要先关闭删除保护才能删除实例
6. 实例名称对外可见，建议使用有意义的名称便于识别
7. 用户需要具有对应实例的管理权限才能执行更新操作
8. 如果实例正在其他操作中（如创建、调整等），可能无法立即更新 