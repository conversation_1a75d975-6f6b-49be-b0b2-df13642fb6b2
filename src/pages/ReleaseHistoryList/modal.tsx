import React, {FC, useCallback, useRef, useState} from 'react';
import {But<PERSON>, Drawer, Form, Radio, Tag} from 'acud';

import {MonacoEditorComponent} from '@/components/MonacoEditorComponent';
import {FormatOptions, ReleaseHistoryType} from '@/utils/enums';

interface ViewModalProps {
  initialValues: any;
}

export const ViewModal: FC<ViewModalProps> = ({initialValues}) => {
  const editorRef = useRef<any>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [formDataInstante] = Form.useForm();

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    formDataInstante.setFieldsValue(initialValues);
  }, [initialValues]);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
  }, [formDataInstante]);

  return (
    <>
      <Button type="actiontext" onClick={showModal}>
        发布详情
      </Button>

      <Drawer
        title="发布历史详情"
        footer={null}
        onClose={handleCancel}
        visible={isModalVisible}
        width={800}
        className="mse-detail-drawer"
      >
        <Form labelAlign="left" form={formDataInstante} labelWidth="100px">
          <Form.Item label="命名空间">
            <span>{initialValues.namespace}</span>
          </Form.Item>
          <Form.Item label="配置分组">
            <span>{initialValues.group}</span>
          </Form.Item>
          <Form.Item label="配置文件名">
            <span>{initialValues.file}</span>
          </Form.Item>
          <Form.Item label="版本号">
            <span>{initialValues.release}</span>
          </Form.Item>
          <Form.Item label="发布类型">
            <div className="drawer-release-type-wrap">
              {ReleaseHistoryType[initialValues.type]}
              <Tag
                color="transparent"
                className="table-status"
                style={{paddingLeft: 0}}
                icon={
                  <span
                    className={
                      initialValues.status === 'success'
                        ? 'circle status-success'
                        : 'circle status-error'
                    }
                  />
                }
              >
                {initialValues.status === 'success' ? '成功' : '失败'}
              </Tag>
            </div>
          </Form.Item>
          <Form.Item name="format" label="配置格式" inputMaxWidth={800}>
            <Radio.Group options={FormatOptions} disabled={true}></Radio.Group>
          </Form.Item>
          <Form.Item name="content" label="配置内容" inputMaxWidth={800}>
            <MonacoEditorComponent
              language={initialValues?.format}
              width="560px"
              height="250px"
              value={initialValues?.content}
              onMount={handleEditorDidMount}
              options={{
                scrollBeyondLastLine: false,
                readOnly: true,
                wordWrap: 'on',
                domReadOnly: true,
                lineNumbers: 'on',
                contextmenu: false
              }}
            />
          </Form.Item>
          <Form.Item label="备注">
            <span>{initialValues.comment || '-'}</span>
          </Form.Item>
          <Form.Item label="最后操作人">
            <span>{initialValues.createBy}</span>
          </Form.Item>
          <Form.Item label="最后发布时间">
            <span>{initialValues.createTime}</span>
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
};
