现在，我需要开发 2 个接口（根据实例 ID 查询服务列表和根据服务名称查询服务来源详情），你需要给每个接口中的参数起字段名，并规定这个字段的相关格式和限制。写 2 份接口文档，以 md 格式存储，存储在接口文档文件夹中，如果没有这个文件夹你需要新建一个。
1.  ==根据实例 ID 查询服务列表==
	1. 这个接口路径是：/api/aigw/v1/aigateway/{instanceId}/service/list
	2. 查询参数需要标准化，需要 keyword 传入服务名称进行模糊搜索
	3. 需要以下返回字段：
		1. 服务名称
		2. 服务来源（容器引擎 CCE）
		3. 关联路由数
		4. 创建时间
		5. 服务状态
2. 查询服务详情：
	1. 这个接口路径是：/api/aigw/v1/aigateway/{instanceId}/{serviceName}/service
	2. ==根据服务名称查询服务来源详情==
	3. 需要以下返回字段：
		1. 集群 ID
		2. 命名空间名
		3. 已关联路由数
		4. 服务来源