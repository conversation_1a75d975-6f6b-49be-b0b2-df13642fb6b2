import {Button, Checkbox, Dropdown, Menu} from 'acud';
import {OutlinedSetting} from 'acud-icon';
import React, {useCallback, useEffect, useMemo, useState} from 'react';

import styles from './index.module.less';

interface TableItem {
  titleLabel?: string;
  title: React.ReactNode | (() => React.ReactNode);
}

interface TableColumnToggleProps {
  dataSource: TableItem[];
  onChange: (a: any[]) => void;
}

const getItemTitle = (item: TableItem) => {
  return item.titleLabel || (item.title as string);
};

const TableColumnToggle: React.FC<TableColumnToggleProps> = ({
  dataSource,
  onChange
}) => {
  const defaultCheckedData = useMemo(() => {
    return dataSource.map((item, index) => ({
      checked: true,
      label: getItemTitle(item) || index
    }));
  }, [dataSource]);

  const [checkedData, setCheckedData] = useState(defaultCheckedData);

  useEffect(() => {
    const newCheckedData = defaultCheckedData.map((item) => {
      const oldItem = checkedData.find((it) => it.label === item.label);
      return {
        ...item,
        checked: oldItem ? oldItem.checked : item.checked
      };
    });

    setCheckedData(newCheckedData);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultCheckedData]);

  const onCheckboxChange = useCallback(
    (e: {target: {checked: boolean}}, index: number) => {
      setCheckedData((items) => {
        items.splice(index, 1, {...items[index], checked: e.target.checked});
        return [...items];
      });
    },
    []
  );

  useEffect(() => {
    const tableData = dataSource.filter(
      (_, index) => checkedData[index].checked
    );
    onChange(tableData);
  }, [checkedData, dataSource, onChange]);

  const menu = useMemo(() => {
    return (
      <Menu>
        {checkedData.map((item, index) => (
          <Menu.Item
            key={item.label}
            disabled
            className={styles['table-column-toggle-menu-item']}
          >
            <Checkbox
              checked={item.checked}
              onChange={(e) => onCheckboxChange(e, index)}
              className={styles['table-column-toggle-menu-item-checkbox']}
            >
              {item.label}
            </Checkbox>
          </Menu.Item>
        ))}
      </Menu>
    );
  }, [checkedData, onCheckboxChange]);

  return (
    <Dropdown overlay={menu} trigger={['click']}>
      <Button icon={<OutlinedSetting />} />
    </Dropdown>
  );
};

export default TableColumnToggle
