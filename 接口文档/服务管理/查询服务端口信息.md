# 查询服务端口信息接口

## 接口基本信息

- **接口说明**：根据集群ID、服务名称和命名空间查询服务端口信息
- **接口地址**：`/api/aigw/v1/aigateway/{clusterId}/{serviceName}/{namespace}/port`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 | 限制 |
| --- | --- | --- | --- | --- | --- |
| clusterId | String | 是 | 集群ID | cce-qhjz40ds | 长度为1-64位字符，只允许字母、数字、中划线和下划线 |
| serviceName | String | 是 | 服务名称 | model-server | 长度为1-64位字符，只允许字母、数字、中划线和下划线 |
| namespace | String | 是 | 命名空间名称 | default | 长度为1-64位字符，只允许字母、数字、中划线和下划线 |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": [
        "53 UDP",
        "53 TCP",
        "9153 TCP"
    ]
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Array | 服务端口信息列表，格式为"端口号 协议类型" |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查集群ID、服务名称或命名空间格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询服务的权限 |
| 404 | 集群、服务或命名空间不存在 | 确认集群ID、服务名称和命名空间是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/cce-qhjz40ds/model-server/default/port
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": [
        "53 UDP",
        "53 TCP",
        "9153 TCP",
        "8080 TCP"
    ]
}
```

## 注意事项

1. 集群ID必须符合规范格式，通常以特定前缀（如`cce-`）开头
2. 服务名称和命名空间名称区分大小写，需要精确匹配
3. 接口仅返回服务端口和协议类型列表，不包含其他服务详细信息
4. 返回结果按照端口号从小到大排序
5. 如果指定服务没有开放端口，将返回空数组`[]`
6. 用户需要具有对应集群、服务和命名空间的查询权限才能获取端口信息 