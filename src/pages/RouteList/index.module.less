.route-list-container {
  padding: 16px;
  background-color: #fff;
  height: 100%;
  
  .operation-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .left-actions {
      display: flex;
      gap: 8px;
    }
    
    .right-actions {
      display: flex;
      gap: 8px;
    }
    
    .refresh-button {
      margin-left: 8px;
    }
  }
  
  .route-table {
    margin-top: 16px;
    
    // 确保表格能够正确显示固定列
    :global {
      .acud-table {
        table-layout: fixed;
      }
      
      .acud-table-thead > tr > th,
      .acud-table-tbody > tr > td {
        word-break: break-word;
        word-wrap: break-word;
      }
      
      // 固定列的阴影效果
      .acud-table-fixed-left {
        box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
      }
      
      .acud-table-fixed-right {
        box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.15);
      }
    }
  }
  
  .path-info {
    display: flex;
    flex-direction: column;
    min-width: 0; // 确保能够正确收缩
    
    .match-type {
      color: #666;
      font-size: 12px;
      margin-top: 2px;
    }
  }
  
  .target-service-info {
    display: flex;
    flex-direction: column;
    min-width: 0; // 确保能够正确收缩
    
    .namespace {
      color: #666;
      font-size: 12px;
      margin-top: 2px;
    }
  }
}
