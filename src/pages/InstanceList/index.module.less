.aigw-instance-list-page {
  height: 100%;
  box-sizing: border-box;
  flex-grow: 1; //把剩余空间都分配给当前元素
  flex-wrap: nowrap; // 禁止换行
  position: relative;
  overflow: auto;
  display: flex;
  flex-direction: column;

  .aigw-instance-list-page-header {
    display: flex;
    flex-direction: column;
    width: inherit;

    .notification-bar {
      height: 48px;
      width: inherit;
      // min-width: 1180px;
      background-image: url('../../assets/img/notice-bg.png');
      background-position: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      padding: 0 0 0 24px;
      display: flex;
      align-items: center;

      .notification-icon {
        width: 38px;
        height: 38px;
        margin-right: 12px;
        margin-top: 10px;
        background-image: url('../../assets/img/notice-icon.png');
        background-size: contain;
        background-repeat: no-repeat;
      }

      .notification-text {
        font-size: 12px;
        color: #151B26;
        line-height: 22px;
      }
    }

    .page-header {
      // height: 48px;
      width: inherit;
      background: #FFFFFF;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #151B26;
        line-height: 24px;
        font-weight: 500;
      }

      .page-actions {
        display: flex;
        align-items: center;
        
        .feature-intro-btn {
          cursor: pointer;
          display: flex;
          align-items: center;

          .product-intro-icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
          
          &.active {
            color: #2468F2;
          }

          &.inactive {
            color: #151B26;
          }
        }
      }
    }
  }
  
  .aigw-instance-list-page-content {
    width: inherit !important;
    padding: 24px !important;
    background-color: #fff !important;
    flex-grow: 1 !important; //把右侧垂直排列的剩余空间都分配给当前元素
    margin: 16px !important;
    border-radius: 6px !important;
  }
}

.introduction {
  margin: 16px 16px 0 16px;
  background: #f6f9ff;
  border-radius: 6px;
  padding: 24px;
  position: relative;
  background-image: url("../../assets/img/introduction.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50%;
  overflow: hidden;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;

  .introduction-title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .introduction-title {
    font-size: 20px;
    color: #151B26;
    line-height: 28px;
    font-weight: 500;
  }

  .hide-btn {
    color: #2468f2;
    cursor: pointer;
    font-size: 12px;
    right: 24px;
    top: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    
    
    &:hover {
      color: #6392ee;
    }
  }

  .introduction-desc {
    margin-bottom: 24px;
    font-size: 12px;
    color: #5C5F66;
    line-height: 20px;
    font-weight: 400;
  }

  .introduction-items {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    // margin: 0 0 24px 24px;

    a {
      color: #2468f2;
      margin-right: 24px;
      cursor: pointer;
      font-size: 14px;
      
      &:hover {
        opacity: 0.8;
      }
    }

    span {
      display: block;
      position: relative;
      padding-left: 12px;
      font-size: 12px;
      color: #151b26;
      margin-right: 32px;
      font-weight: 400;
      line-height: 20px;
      
      &::before {
        content: "";
        display: inline-block;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border: 2px solid #2468f2;
        border-radius: 50%;
        box-sizing: border-box;
        background-color: transparent;
      }
    }
  }
}

// 编辑Popover样式
.edit-popover {
  min-width: 240px;
  padding: 8px 4px;
}

.error-text {
  color: #F5222D;
  margin-top: 8px;
  font-size: 12px;
}

.tip-text {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  margin-top: 16px;

  button {
    margin-right: 8px;
  }
}

.edit-icon{
  margin-left: 4px;
  cursor: pointer;
  color: #2468F2 !important;
  font-size: 16px !important;
  min-width: 16px !important;
  min-height: 16px !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}
