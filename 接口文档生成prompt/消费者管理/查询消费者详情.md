现在，我需要开发1个接口（查询消费者详情），你需要给每个接口中的参数起字段名，并规定这个字段的相关格式和限制。写1份接口文档，以md格式存储，存储在接口文档的消费者管理文件夹中，如果没有这个文件夹你需要新建一个。

1. 查询消费者详情的接口路径为：/api/aigw/v1/aigateway/{instanceId}/consumer/{consumerId}

2. 需要根据实例ID和消费者ID查询当前实例中指定消费者详情信息，需要返回以下字段：
	1. 消费者名称
	2. 描述
	3. 认证方式
	4. 认证信息（后端自动生成的token）
	5. 已授权路由
		a. 路由名称
		b. 路由创建时间
		c. 路由是否开启消费者认证

|   |   |   |
|---|---|---|
|**参数**|**说明**|**相关限制**|
|实例ID|必填，路径参数，指定要查询的实例ID||
|消费者ID|必填，路径参数，指定要查询的消费者ID|| 