import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { getQueryParams, useRegion } from '@baidu/bce-react-toolkit';
import { Alert, Tabs, Button, Input, Table, Space, Popconfirm, Tooltip, Drawer, Form, Radio, Empty, Tag, Select, toast, Modal, Link } from 'acud';
import { OutlinedSearch, OutlinedRefresh, OutlinedPlusNew } from 'acud-icon';
import type { TablePaginationConfig } from 'acud/lib/table';

import { 
  getInstanceServices, 
  getServiceDetail, 
  getClusterNamespaces, 
  getNamespaceServices, 
  addServices, 
  removeService 
} from '@/apis/service';
import { getRegistrationInstance, getInstanceRelatedClusters } from '@/apis/instance';

import styles from './index.module.less';

const { TabPane } = Tabs;
const { Option } = Select;

// 定义服务列表接口响应类型
interface ServiceListResponse {
  success: boolean;
  status: number;
  page: {
    orderBy: string;
    order: string;
    pageNo: number;
    pageSize: number;
    totalCount: number;
    result: any[];
  };
}

// 定义接口错误响应类型
interface ErrorResponse {
  success: boolean;
  status: number;
  message?: string;
}

const InferenceService: React.FC = () => {
  const { instanceId } = getQueryParams();
  const { region } = useRegion();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('own-model');
  const [keyword, setKeyword] = useState('');
  const [serviceList, setServiceList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [instanceDetail, setInstanceDetail] = useState<any>({});
  const [clusterList, setClusterList] = useState<any[]>([]);
  const [namespaceList, setNamespaceList] = useState<string[]>([]);
  const [servicesList, setServicesList] = useState<string[]>([]);
  const [selectedClusterId, setSelectedClusterId] = useState<string>('');
  const [selectedNamespace, setSelectedNamespace] = useState<string>('');
  const [submitLoading, setSubmitLoading] = useState(false);
  const [form] = Form.useForm();
  
  // 分页配置
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    onChange: (page, pageSize) => {
      fetchServiceList({
        pageNo: page,
        pageSize: pageSize || 10,
        keyword
      });
    }
  });

  // 服务列表列配置
  const columns = [
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 180,
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <div style={{ 
            overflow: 'hidden', 
            textOverflow: 'ellipsis', 
            whiteSpace: 'nowrap' 
          }}>
            {text}
          </div>
        </Tooltip>
      )
    },
    {
      title: '服务状态',
      dataIndex: 'serviceStatus',
      key: 'serviceStatus',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          'running': { text: '运行中', className: 'status-success' },
          'pending': { text: '等待中', className: 'status-active' },
          'error': { text: '错误', className: 'status-error' },
          'unknown': { text: '未知', className: 'status-inactive' },
          'deleted': { text: '已删除', className: 'status-inactive' }
        };
        const currentStatus = statusMap[status] || { text: status || '未知', className: 'status-inactive' };
        return (
          <Tag 
            color="transparent" 
            icon={<span className={`circle ${currentStatus.className}`}></span>}
            style={{ paddingLeft: 0 }}
          >
            {currentStatus.text}
          </Tag>
        );
      }
    },
    {
      title: '服务来源',
      dataIndex: 'serviceSource',
      key: 'serviceSource',
      width: 150,
      render: (text: string, record: any) => (
        <Tooltip
          title={
            <div style={{ whiteSpace: 'pre-line' }}>
              <span>集群ID: {record._serviceDetail?.clusterId || ''}</span>
            </div>
          }
          overlayStyle={{ maxWidth: '300px' }}
        >
          <div style={{ 
            width: '100%', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis', 
            whiteSpace: 'nowrap' 
          }}>
            <span>{text && (text.toUpperCase() === 'CCE') ? '容器引擎 CCE' : text}</span>
          </div>
        </Tooltip>
      )
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
      width: 150,
      render: (text: string, record: any) => (
        <Tooltip title={record.namespace || '-'}>
          <div style={{ 
            width: '100%', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis', 
            whiteSpace: 'nowrap' 
          }}>
            <span>{ record.namespace || '-'}</span>
          </div>
        </Tooltip>
      )
    },
    {
      title: '关联路由数',
      dataIndex: 'routeCount',
      key: 'routeCount',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 200
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record: any) => (
        <Space size="middle">
          {record.routeCount > 0 ? (
            <Tooltip title="当前服务已和路由关联，无法移除，请先删除相关路由规则后再移除服务。">
              <span style={{ color: '#00000040', cursor: 'not-allowed' }}>移除</span>
            </Tooltip>
          ) : (
            <a onClick={() => {
              Modal.confirm({
                title: '确定要移除当前服务吗？',
                content: (
                  <div>
                    服务移除后无法恢复，请谨慎操作！请确定是否要移除服务"{record.serviceName}"？
                  </div>
                ),
                okText: '确定',
                cancelText: '取消',
                width: 400,
                onOk: () => handleRemoveService(record.serviceName, record.namespace)
              });
            }}>移除</a>
          )}
        </Space>
      )
    }
  ];

  // 获取实例详情
  const fetchInstanceDetail = async () => {
    try {
      // console.log('请求实例详情，instanceId:', instanceId);
      const res = await getRegistrationInstance(instanceId);
      // console.log('获取实例详情成功:', res);
      if (res?.success && res?.result) {
        setInstanceDetail(res.result);
      }
    } catch (error) {
      console.error('获取实例详情失败:', error);
   
    }
  };

  // 加载服务详情信息的函数
  const loadServiceDetails = useCallback(async (services) => {
    try {
      if (!services || services.length === 0) return services;
      
      const updatedList = await Promise.all(
        services.map(async (service) => {
          if (!service._serviceDetail) {
            try {
              const detail = await getServiceDetailInfo(service.serviceName);
              return { ...service, _serviceDetail: detail };
            } catch (error) {
              // 静默处理单个服务的错误，不显示toast
              console.error(`获取服务详情失败: ${service.serviceName}`, error);
              return { ...service, _serviceDetail: null };
            }
          }
          return service;
        })
      );
      return updatedList;
    } catch (error) {
      console.error('加载服务详情失败:', error);
      // 不显示toast，只记录错误日志
      return services;
    }
  }, []);

  // 获取服务列表
  const fetchServiceList = async (params?: any) => {
    try {
      setLoading(true);
     // console.log('请求服务列表，instanceId:', instanceId, '参数:', params);
      const res = await getInstanceServices(instanceId, {
        pageNo: params?.pageNo || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize,
        keyword: params?.keyword || keyword,
        orderBy: 'createTime',
        order: 'desc'
      });
     // console.log('获取服务列表成功:', res);
      // 确保返回数据符合预期结构
      const response = res as unknown as ServiceListResponse;
      if (response?.success && response?.page) {
        const services = response.page.result || [];
        
        // 为服务列表加载详情信息
        const servicesWithDetails = await loadServiceDetails(services);
        setServiceList(servicesWithDetails);
        
        setPagination({
          ...pagination,
          current: response.page.pageNo,
          pageSize: response.page.pageSize,
          total: response.page.totalCount
        });
      }
    } catch (error) {
      console.error('获取服务列表失败:', error);
      toast.error({
        message: '获取服务列表失败',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取实例关联的集群列表
  const fetchInstanceClusters = async () => {
    try {
     // console.log('请求实例关联的集群列表, instanceId:', instanceId);
      const res = await getInstanceRelatedClusters(instanceId, {
        pageNo: 1,
        pageSize: 100,
        orderBy: 'relationTime',
        order: 'desc'
      });
     // console.log('获取实例关联的集群列表成功:', res);
      // 处理返回数据
      const response = res as any;
      if (response?.success && response?.page) {
        // 确保集群数据包含所需的字段
        const clusters = (response.page.result || []).map(cluster => ({
          ...cluster,
          remark: cluster.remark || '-',
          relationTime: cluster.relationTime || '-'
        }));
        setClusterList(clusters);
      }
    } catch (error) {
      console.error('获取实例关联的集群列表失败:', error);
      toast.error({
        message: '获取关联集群失败',
        duration: 3
      });
    }
  };

  // 获取集群中的命名空间列表
  const fetchNamespaceList = async (clusterId: string) => {
    try {
     // console.log('请求集群中的命名空间列表, clusterId:', clusterId);
      const res = await getClusterNamespaces(clusterId);
     // console.log('获取集群中命名空间列表成功:', res);
      if (res?.success && res?.result) {
        setNamespaceList(res.result || []);
      }
    } catch (error) {
      console.error('获取集群中命名空间列表失败:', error);
      toast.error({
        message: '获取命名空间失败',
        duration: 3
      });
      setNamespaceList([]);
    }
  };

  // 获取命名空间中的服务列表
  const fetchNamespaceServices = async (clusterId: string, namespace: string) => {
    try {
     // console.log('请求命名空间中的服务列表, clusterId:', clusterId, 'namespace:', namespace);
      const res = await getNamespaceServices(clusterId, namespace);
     // console.log('获取命名空间中服务列表成功:', res);
      if (res?.success && res?.result) {
        setServicesList(res.result || []);
      }
    } catch (error) {
      console.error('获取命名空间中服务列表失败:', error);
      toast.error({
        message: '获取服务列表失败',
        duration: 3
      });
      setServicesList([]);
    }
  };

  // 移除服务
  const handleRemoveService = async (serviceName: string, namespace: string) => {
    try {
      //console.log('移除服务, instanceId:', instanceId, 'serviceName:', serviceName, 'namespace:', namespace);
      const res = await removeService(instanceId, serviceName, namespace);
      //console.log('移除服务成功:', res);
      if (res?.success) {
        // 显示成功提示
        toast.success({
          message: `移除服务 ${serviceName} 成功`,
          duration: 3
        });
        // 刷新列表
        fetchServiceList();
      } else {
        toast.error({
          message: '移除服务失败',
          duration: 3
        });
      }
    } catch (error) {
      console.error('移除服务失败:', error);
      toast.error({
        message: '移除服务失败',
        duration: 3
      });
    }
  };

  // 添加服务
  const handleAddService = async () => {
    try {
      const values = await form.validateFields();
    //  console.log('添加服务，表单数据:', values);
      
      // 设置加载状态
      setSubmitLoading(true);
      
      // 构建请求数据
      const requestData = {
        clusterId: selectedClusterId,
        serviceSource: values.serviceSource,
        namespace: selectedNamespace,
        serviceList: values.serviceList
      };
      
      // 打印原始请求数据
     // console.log('添加服务，原始请求数据:', {
      //  url: `/api/aigw/v1/aigateway/cluster/${instanceId}/serviceList`,
      //  method: 'POST',
      //  instanceId,
      //  requestData
      //});
      
      const res = await addServices(instanceId, requestData);
      
      //console.log('添加服务成功:', res);
      if (res?.success && res?.result) {
        // 显示成功提示，提示添加的服务数量
        const addedCount = res.result.addedCount || 0;
        toast.success({
          message: `已成功添加 ${addedCount} 个服务`,
          duration: 3,
        });
        
        // 先关闭抽屉
        setDrawerVisible(false);
        // 重置表单
        form.resetFields();
        // 刷新列表
        fetchServiceList();
      } else {
        // 接口返回success为false的情况
        const errorRes = res as unknown as ErrorResponse;
        console.error('添加服务接口返回失败:', errorRes?.message || '请稍后重试');
        toast.error({
          message: errorRes?.message || '添加服务失败，请稍后重试',
          duration: 3
        });
      }
    } catch (error) {
      console.error('添加服务失败:', error);
      toast.error({
        message: '添加服务失败',
        duration: 3
      });
    } finally {
      // 关闭加载状态
      setSubmitLoading(false);
    }
  };

  // 查询服务详情
  const getServiceDetailInfo = async (serviceName: string) => {
    try {
     // console.log('查询服务详情, instanceId:', instanceId, 'serviceName:', serviceName);
      const res = await getServiceDetail(instanceId, serviceName);
     // console.log('获取服务详情成功:', res);
      return res?.success ? res.result : null;
    } catch (error) {
      console.error('获取服务详情失败:', error);
      // 不显示错误提示，因为这是由loadServiceDetails调用的，已处理为静默错误
      return null;
    }
  };

  // 刷新按钮点击
  const handleRefresh = () => {
    setKeyword('');
    fetchServiceList({ keyword: '', pageNo: 1 });
  };

  // 搜索
  const handleSearch = () => {
    fetchServiceList({ keyword, pageNo: 1 });
  };

  // 打开添加服务抽屉
  const openDrawer = () => {
    setDrawerVisible(true);
    // 初始化表单
    form.setFieldsValue({
      serviceSource: 'CCE',
      serviceList: []
    });
    // 清空选中的集群和命名空间
    setSelectedClusterId('');
    setSelectedNamespace('');
    setServicesList([]);
  };

  // 关闭抽屉
  const closeDrawer = () => {
    setDrawerVisible(false);
    form.resetFields();
  };

  // 集群选择变更
  const handleClusterChange = (clusterId: string) => {
   // console.log('集群选择变更:', clusterId);
    setSelectedClusterId(clusterId);
    setSelectedNamespace('');
    setServicesList([]);
    form.setFieldsValue({ serviceList: [] });
    
    if (clusterId) {
      fetchNamespaceList(clusterId);
    } else {
      setNamespaceList([]);
    }
  };

  // 命名空间选择变更
  const handleNamespaceChange = (namespace: string) => {
   // console.log('命名空间选择变更:', namespace);
    setSelectedNamespace(namespace);
    form.setFieldsValue({ serviceList: [] });
    
    if (selectedClusterId && namespace) {
      fetchNamespaceServices(selectedClusterId, namespace);
    } else {
      setServicesList([]);
    }
  };

  // 跳转到实例详情关联信息页面
  const goToInstanceRelation = () => {
    const url = `/instance/base/info?instanceId=${instanceId}&activeMenu=instance-detail&activeTab=instance-relation`;
    navigate(url);
  };

  // 初始化
  useEffect(() => {
    if (instanceId) {
      fetchInstanceDetail();
      fetchServiceList();
      fetchInstanceClusters();
    }
  }, [instanceId]);

  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="自有模型服务" key="own-model">
          <Alert
            message={
              <span>
                您可在自有模型服务中添加您自行部署的推理服务，从而通过网关对您的推理服务进行统一代理。若您需要添加来自 CCE 集群中的推理服务，请先前往网关&nbsp;
                <a 
                  onClick={goToInstanceRelation}
                  style={{ color: '#2468f2', cursor: 'pointer' }}
                >
                  实例详情-关联信息
                </a>
                 &nbsp;关联容器集群。
              </span>
            }
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
          
          <div className={styles.actionBar}>
            <Button type="primary" icon={<OutlinedPlusNew />}
 onClick={openDrawer}>添加服务</Button>
            <div className={styles.rightActions}>
              <Input
                placeholder="请输入服务名称"
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                onPressEnter={handleSearch}
                style={{ width: 320, marginRight: 8 }}
                suffix={<OutlinedSearch onClick={handleSearch} style={{ cursor: 'pointer' }} />}
              />
              <Button 
                icon={<OutlinedRefresh />} 
                onClick={handleRefresh}
              />
            </div>
          </div>
          
          <Table
            columns={columns}
            dataSource={serviceList}
            rowKey="serviceName"
            loading={loading}
            pagination={pagination}
            locale={{
              emptyText: (
                <Empty
                  description={
                    <span>
                      暂未添加推理服务。
                      <a onClick={openDrawer} style={{ marginLeft: '4px', cursor: 'pointer' }}>
                        <OutlinedPlusNew style={{ marginRight: '4px' }} />
                        添加服务
                      </a>
                    </span>
                  }
                />
              )
            }}
          />
          
          {/* 添加服务弹窗 */}
          <Modal
            title="添加推理服务"
            visible={drawerVisible}
            onCancel={closeDrawer}
            onOk={handleAddService}
            confirmLoading={submitLoading}
            className='modalHeight'
            width={840}
          >
            <Form
              form={form}
              layout="horizontal"
              labelCol={{ span: 3 }}
                          labelAlign="left"
              initialValues={{ serviceSource: 'CCE' }}
              inputMaxWidth='100%'
            >
              <div className={styles.formSection}>
                <div className={styles.sectionTitle}>基本信息</div>
                
                <Form.Item label="所属实例/ID" name="instance">
                  <div className={styles.readonlyField}>
                    {instanceDetail?.name || '-'} ({instanceId || '-'})
                  </div>
                </Form.Item>
                
                <Form.Item label="所属VPC" name="vpc" style={{marginBottom: 0}}>
                  <div className={styles.readonlyField}>
                  {instanceDetail?.vpcId ? (
                        <a
                          href={`https://console.bce.baidu.com/network/#/vpc/instance/detail?vpcId=${instanceDetail.vpcId}`}
                          target="_blank"
                          rel="noreferrer"
                          style={{ color: '#2468f2', cursor: 'pointer' }}
                        >
                          {instanceDetail.vpcId}
                        </a>
                      ) : (
                        '-'
                      )}
                  </div>
                </Form.Item>
              </div>
              
              <div className={styles.formSectionLast}>
                <div className={styles.sectionTitle}>模型服务配置</div>
                
                <Form.Item 
                  label="服务来源" 
                  name="serviceSource"
                  rules={[{ message: '请选择服务来源' }]}
                >
                  <Radio.Group>
                    <Radio.Button value="CCE">容器引擎 CCE</Radio.Button>
                  </Radio.Group>
                </Form.Item>
                
                <Form.Item 
                  label="关联集群" 
                  required
                >
                  <div className={styles.clusterListContainer}>
                    {clusterList && clusterList.length > 0 ? (
                      <Table
                        rowSelection={{
                          type: 'radio',
                          selectedRowKeys: selectedClusterId ? [selectedClusterId] : [],
                          onChange: (selectedRowKeys) => {
                            handleClusterChange(selectedRowKeys[0] as string);
                          }
                        }}
                        columns={[
                          {
                            title: '集群名称/ID',
                            dataIndex: 'clusterName',
                            key: 'clusterName',
                      
                            render: (_, record) => (
                              <Tooltip title={record.clusterName} placement="topLeft">
                                <div style={{display:'flex',flexDirection:'column'}}>
                                  <a
                                    href={`https://console.bce.baidu.com/cce/#/cce/cluster/detail?clusterUuid=${record.clusterId}&clusterName=${encodeURIComponent(record.clusterName)}`}
                                    target="_blank"
                                    rel="noreferrer"
                                    style={{
                                      color: '#2468f2',
                                      cursor: 'pointer',
                                      display: 'inline-block',
                                      maxWidth: '100%',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap'
                                    }}
                                  >
                                    {record.clusterName}
                                  </a>
                                  <div style={{ color: '#151B26', fontSize: '12px' }}>
                                  {record.clusterId}
                                </div>
                                </div>
                               
                              </Tooltip>
                            )
                          },
                          {
                            title: '运行状态',
                            dataIndex: 'status',
                            key: 'status',
                       
                            render: (status) => {
                              const statusMap = {
                                'RUNNING': { text: '运行中', className: 'status-success' },
                                'ERROR': { text: '访问异常', className: 'status-error' }
                              };
                              const currentStatus = statusMap[status] || { text: status || '-', className: 'status-warning' };
                              return (
                                <Tag 
                                  color="transparent" 
                                  icon={<span className={`circle ${currentStatus.className}`}></span>}
                                  style={{ paddingLeft: 0 }}
                                >
                                  {currentStatus.text}
                                </Tag>
                              );
                            }
                          },
                          {
                            title: '备注',
                            dataIndex: 'remark',
                            key: 'remark',
                         
                            render: (text) => (
                              <Tooltip title={text} placement="topLeft">
                                <div style={{ 
                                  overflow: 'hidden', 
                                  textOverflow: 'ellipsis', 
                                  whiteSpace: 'nowrap',
                                  color: 'rgba(0, 0, 0, 0.65)'
                                }}>
                                  {text === '-' ? '-' : text}
                                </div>
                              </Tooltip>
                            )
                          },
                          {
                            title: '关联时间',
                            dataIndex: 'relationTime',
                            key: 'relationTime',
                          
                            render: (text) => (
                              <div className={`${styles.cellContent} ${styles.timeCell}`}>
                                {text || '-'}
                              </div>
                            )
                          }
                        ]}
                        dataSource={clusterList}
                        rowKey="clusterId"
                        pagination={false}
                        className={styles.clusterTable}
                        onRow={(record) => ({
                          onClick: () => {
                            handleClusterChange(record.clusterId);
                          }
                        })}
                      />
                    ) : (
                      <span>
                        暂未关联集群，请先前往&nbsp;
                        <a 
                          onClick={goToInstanceRelation}
                          style={{ color: '#2468f2', cursor: 'pointer' }}
                        >
                          实例详情-关联信息
                        </a>
                         &nbsp;关联容器集群
                      </span>
                    )}
                  </div>
                </Form.Item>
                
                <Form.Item
                  label="命名空间"
                  name="namespace"
                  rules={[{ required: true, message: '请选择命名空间' }]}
                >
                  <Select
                    placeholder="请选择命名空间"
                    disabled={!selectedClusterId}
                    onChange={handleNamespaceChange}
                    value={selectedNamespace}
                    style={{ width: '360px' }}
                    className={styles.serviceSelector}
                  >
                    {namespaceList.map(ns => (
                      <Option key={ns} value={ns}>
                        <div className={styles.serviceOption}>
                          {ns}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item
                  label="推理服务"
                  name="serviceList"
                  rules={[{ required: true, message: '请选择至少一个推理服务' }]}
                >
                  <Select
                    mode="multiple"
                    placeholder="请选择推理服务"
                    disabled={!selectedNamespace}
                    optionFilterProp="children"
                    style={{ width: '360px' }}
                    listHeight={250}
                    maxTagCount={3}
                    maxTagTextLength={15}
                    className={styles.serviceSelector}
                    // dropdownMatchSelectWidth={false}
                    // dropdownStyle={{ minWidth: '200px' }}
                  >
                    {servicesList.map(service => (
                      <Option key={service} value={service}>
                        <div className={styles.serviceOption}>
                          {service}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </Form>
          </Modal>
        </TabPane>
        
        {/* <TabPane tab="第三方模型服务" key="third-party-model">
          <Empty
            description="功能开发中"
          />
        </TabPane> */}
      </Tabs>
    </div>
  );
};

export default InferenceService; 