现在，我需要开发 1 个接口（查询同 VPC 中的 CCE 集群），你需要给每个接口中的参数起字段名，并规定这个字段的相关格式和限制。写 1 份接口文档，以 md 格式存储，存储在接口文档文件夹中，如果没有这个文件夹你需要新建一个。

1. 查询和实例同 VPC 中的 CCE 集群，接口路径为 /api/aigw/v1/aigateway/cluster/clusterList?vpcid={} 2. region在默认的请求头中

需要根据实例的 VPC 和所处地域查询当前 VPC 下的所有有权限（admin）的 CCE 集群

| 参数     | 说明             |
| ------ | -------------- |
| 集群名称   | CCE 集群的名称      |
| 集群 ID  | CCE 集群的 ID     |
| 运行状态   | 集群的运行状态        |
| VPC 网段 | 集群所处的 VPC CIDR |
| 地域     | 集群所属的地域        |