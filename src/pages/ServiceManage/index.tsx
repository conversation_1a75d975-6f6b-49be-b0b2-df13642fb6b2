import React, {useCallback, useEffect, useRef, useState} from 'react';
import {getQueryParams, useRegion} from '@baidu/bce-react-toolkit';
import {Link, Search, Select, Table, Tag} from 'acud';
import {OutlinedInfoCircle} from 'acud-icon';
import {useRequest} from 'ahooks';
import cx from 'classnames';

import {getInstanceList} from '@/apis/instance';
import {getServiceAdminList} from '@/apis/serviceAdmin';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';
import RefreshButton from '@/components/RefreshButton';

const filterOption = (input: any, option: any) => {
  return option?.label?.toLowerCase()?.includes(input.toLowerCase());
};

const ServiceManage: React.FC = () => {
  const {instanceId, namespaceName} = getQueryParams();
  const [dataSource, setDataSource] = useState([]);
  const [seachType, setSearchType] = useState('serviceName');
  const [seacherValue, setSeacherValue] = useState('');
  const [instanceList, setInstanceList] = useState<
    Array<{label: string; value: string}>
  >([]);
  const [currentRegistrationInstance, setRegistrationInstance] =
    useState<string>('');

  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const {region} = useRegion();
  const isFirstRun = useRef(true);

  useEffect(() => {
    if (namespaceName) {
      setSearchType('namespace');
      setSeacherValue(namespaceName);
    }
  }, [namespaceName]);

  const columns = [
    {
      title: '服务名',
      dataIndex: 'name',
      width: 200,
      key: 'name',
      render(_, record) {
        const {name, id, namespace} = record || {};
        return (
          <Link
            href={`#/service/instance/list?instanceId=${currentRegistrationInstance}&serviceName=${name}&serviceId=${id}&namespace=${namespace}`}
          >
            {name}
          </Link>
        );
      }
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
      render(value: string) {
        return value || '-';
      }
    },
    {
      title: '状态',
      dataIndex: 'healthCount',
      key: 'healthCount',
      render: (value: number, record) => {
        const {healthCount, totalCount} = record;
        let classType: any = 'circle status-inactive';
        let textType = '无实例';
        if (healthCount > 0) {
          classType = 'circle status-success';
          textType = '运行中';
        } else if (totalCount > 0 && healthCount === 0) {
          classType = 'circle status-error';
          textType = '不可用';
        }
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{paddingLeft: 0}}
            icon={<span className={classType} />}
          >
            {textType || '-'}
          </Tag>
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 240
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 240
    },
    {
      title: '健康实例/总实例',
      dataIndex: 'count',
      key: 'clunt',
      render(value, row) {
        return `${row.healthCount ?? '-'}/${row.totalCount ?? '-'}`;
      }
    }
  ];

  const getServiceListBack = useCallback(
    async (params: any) => {
      return getServiceAdminList({
        instanceId: currentRegistrationInstance,
        pageNo,
        pageSize,
        keywordType: seachType,
        keyword: seacherValue,
        ...params
      });
    },
    [pageNo, pageSize, currentRegistrationInstance, seacherValue, seachType]
  );

  const {
    run,
    loading,
    refresh: refreshInstanceList
  } = useRequest(getServiceListBack, {
    manual: true,
    onSuccess: (res) => {
      setDataSource(res?.result?.result || []);
      setTotal(res?.result?.totalCount || 0);
    }
  });

  const {run: _getInstanceList} = useRequest(
    () => getInstanceList({pageNo: 1, pageSize: 1000}),
    {
      onSuccess: (res) => {
        const list = (res?.result?.result || [])
          .filter((e) => e.status === 2)
          ?.map((item) => ({
            label: item.name,
            value: item.id
          }));
        setInstanceList(list || []);
        if (list.length) {
          setRegistrationInstance(list[0].value);
        }
      }
    }
  );

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }
    _getInstanceList();
  }, [region]);

  useEffect(() => {
    if (instanceList?.length) {
      if (instanceList.find((e) => e.value === instanceId)) {
        setRegistrationInstance(instanceId);
      } else {
        setRegistrationInstance(instanceList[0].value);
      }
    }
  }, [instanceList]);

  useEffect(() => {
    if (currentRegistrationInstance) {
      run({});
    }
  }, [currentRegistrationInstance]);

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    refreshInstanceList();
  }, [refreshInstanceList]);

  const onRegistrationInstanceChange = (value: string) => {
    setRegistrationInstance(value);
  };

  const handleChangeSearchType = (value: string) => {
    setSearchType(value);
  };

  const onConfirmSearch = (searchParam: any) => {
    const {value} = searchParam;
    setSeacherValue(value);
    setPageNo(1);
    run({
      keyword: value,
      pageNo: 1
    });
  };

  return (
    <div className={cx(['custom-layout'])}>
      <div className="mse-custom-header-wrap">
        <div className="mse-custom-header-left">
          <div className="custom-header-title">服务管理</div>
          <div className="mse-custom-title-filter">
            所属实例：
            <Select
              options={instanceList}
              value={currentRegistrationInstance || undefined}
              showSearch
              placeholder="请选择实例"
              filterOption={filterOption}
              style={{minWidth: 100}}
              onChange={onRegistrationInstanceChange}
            ></Select>
          </div>
        </div>
        <div className="mse-custom-header-right">
          <Link
            className="mse-custom-header-link"
            href="https://cloud.baidu.com/doc/MSE/index.html"
            target="_blank"
            icon={<OutlinedInfoCircle />}
          >
            产品文档
          </Link>
        </div>
      </div>
      <div className="mse-custom-page-content">
        <div className="mse-custom-page-operation-container">
          <div className="mse-custom-page-operation-left"></div>
          <div className="mse-custom-page-operation-right">
            <Search
              multipleOption={[
                {
                  label: '服务名称',
                  value: 'serviceName',
                  key: 'serviceName'
                },
                {
                  label: '命名空间',
                  value: 'namespace',
                  key: 'namespace'
                }
              ]}
              placeholder={`请输入${seachType === 'serviceName' ? '服务名称' : '命名空间'}关键字搜索`}
              onChangeMultiple={(value) => {
                handleChangeSearchType(value as string);
              }}
              defaultValue={namespaceName}
              multipleValue={seachType}
              style={{width: 300}}
              onSearch={onConfirmSearch}
            />
            <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          </div>
        </div>

        <Table
          dataSource={dataSource}
          columns={columns}
          rowKey="id"
          loading={{
            loading: loading,
            size: 'small'
          }}
          pagination={false}
        />

        <div className="paginationContainer">
          <PaginationOfAutoHide
            showSizeChanger={true}
            showQuickJumper={true}
            current={pageNo}
            pageSize={pageSize}
            total={total}
            onChange={(page, pageSize) => {
              setPageNo(page);
              setPageSize(pageSize!);
              run({pageNo: page, pageSize});
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ServiceManage;
