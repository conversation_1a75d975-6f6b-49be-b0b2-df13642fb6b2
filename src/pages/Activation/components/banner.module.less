.container {
  border-radius: 6px;
  padding: 40px 0;
  background-image: url('../../../assets/img/banner.jpg');
  background-size: cover;
  position: relative;
  background-repeat: no-repeat;
  height: 280px;
  display: flex;

  .contentContainer {
    padding-left: 40px;
  }

  .title,
  .desc {
    position: relative;
    max-width: 880px;
    word-break: break-all;
    z-index: 10;
  }

  .title {
    font-size: 24px;
    color: #151b26;
    line-height: 32px;
    font-weight: 500;
    margin-bottom: 32px;
  }

  .desc {
    font-size: 12px;
    color: #151b26;
    line-height: 22px;
    font-weight: 400;
    display: flex;
    width: 100%;
    max-width: 1000px;
    .svgWrap {
      width: 16px;
      height: 16px;
      color: #2468f2;
      margin-right: 8px;
      padding-top: 3px;
    }
  }

  .operateContainer {
    width: calc(~'70% - 40px');
    position: absolute;
    bottom: 40px;
  }

  .protocolContainer {
    margin-top: 12px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    .agreeText {
      margin-left: 8px;
    }
  }

  .buttonWrapper {
    display: flex;
    align-items: center;
  }

  .activateBtn {
    width: 160px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
  }

  .betaTag {
    display: inline-block;
    margin-left: 16px;
    font-size: 12px;
    color: #FF9326;
    line-height: 40px;
    font-weight: 500;
    vertical-align: middle;
  }

  .contentContainer {
    width: 70%;
  }

  .bannerWrap {
    height: 100%;
    width: 30%;
    position: relative;
  }

  .banner {
    width: 280px;
    height: 280px;
    background-image: url('../../../assets/img/banner2.png');
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 0;
  }
}

.agreeTip {
  font-size: 12px;
  color: #f33e3e;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}
