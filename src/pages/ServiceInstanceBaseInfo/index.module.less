.create-instance-wrap {
  width: 100%;
  height: 100%;
  .create-instance-header {
    height: 50px;
    display: flex;
    align-items: center;
    background-color: #fff;
  }
  .create-instance-content {
    background-color: #fff;
    border-radius: 6px;
    margin: 16px;
    .form-item-group-wrap {
      :global {
        .acud-form-item-control {
          min-width: 500px !important;
        }
      }
    }
  }
  .create-instance-footer {
    background-color: #fff;
    bottom: 0;
    box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    display: flex;
    left: 0;
    padding: 10px 16px;
    position: fixed;
    width: 100vw;
    z-index: 10;
    gap: 16px;
    :global {
      .acud-btn {
        height: 40px;
      }
    }
  }
  .detail-base-card {
    background: #fff;
    border-radius: 6px;
    margin-bottom: 16px;
    overflow: auto;
    padding: 24px;
    .detail-base-card-title {
      align-items: center;
      display: flex;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      margin-bottom: 16px;
      position: relative;
    }
    .detail-base-card-content {
      .detail-base-card-item {
        display: flex;
        width: 100%;
        .detail-base-card-label1 {
          color: #666;
          width: 116px;
        }
        .detail-base-card-label2 {
          color: #666;
          width: 68px;
        }
        .detail-base-table-wrap {
          width: 100%;
        }
      }
    }
  }
}

.tag-content {
  max-width: 1000px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.wrap-content {
  width: 400px !important;
  max-height: 400px !important;
  overflow: hidden;
  overflow-y: scroll;
}

.wrap-tag {
  background: #ecedf6;
  margin-right: 20px;
  line-height: 30px;
  border-radius: 15px;
  display: inline-block;
  padding: 0 10px;
  max-width: 150px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.instance-detail-border {
  height: 1px;
  background-color: #e8e9eb;
  margin: 24px 0;
}
