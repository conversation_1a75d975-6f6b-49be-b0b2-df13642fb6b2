import React, {useCallback, useMemo, useState} from 'react';
import {Form, Modal, Popover, Select} from 'acud';
import {OutlinedEditingSquare, OutlinedQuestionCircle} from 'acud-icon';
import {useRequest} from 'ahooks';

import {getEsgInstanceList, putRegistrationInstance} from '@/apis/instance';

import styles from './index.module.less';

const ChangeEsg = (props: {
  esgId: string;
  id: string; // 服务网卡ID
  instanceId: string; // 实例ID
  requestDetail: () => void;
}) => {
  const {esgId, id, instanceId, requestDetail} = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formDataInstante] = Form.useForm();

  const {
    data: _esgInstanceList,
    loading: esgLoading,
    run: getInstanceList
  } = useRequest(
    () =>
      getEsgInstanceList({
        pageNo: 1,
        pageSize: 1000
      }),
    {
      manual: true
    }
  );

  const esgOptions = useMemo(() => {
    return (
      _esgInstanceList?.page?.result.map(
        ({esgId, name}: {name: string; esgId: string}) => ({
          label: `${name}(${esgId})`,
          value: esgId
        })
      ) ?? []
    );
  }, [_esgInstanceList]);

  const onSubmit = useCallback(async () => {
    const esgValue = formDataInstante.getFieldValue('esgId');
    const payload = {
      registrationhInstanceId: instanceId,
      endpoints: [
        {
          id,
          enterpriseSecurityGroupId: esgValue
        }
      ]
    };
    await putRegistrationInstance(payload)
      .then(() => {
        setIsModalOpen(false);
      })
      .then(() => {
        requestDetail();
      });
  }, [instanceId, id, formDataInstante, requestDetail]);

  const getEsgInstanceListBack = useCallback(async () => {
    await getInstanceList();
    formDataInstante.setFieldsValue(esgId);
    setIsModalOpen(true);
  }, [getInstanceList, formDataInstante, esgId]);

  return (
    <>
      <OutlinedEditingSquare
        className={styles['edit-icon']}
        onClick={getEsgInstanceListBack}
      />
      {isModalOpen && (
        <Modal
          title="编辑访问策略"
          visible
          // eslint-disable-next-line react/jsx-no-bind
          onCancel={() => setIsModalOpen(false)}
          onOk={onSubmit}
          width={610}
        >
          <Form form={formDataInstante}>
            <Form.Item
              name="esgId"
              label={
                <>
                  访问策略：
                  {
                    <Popover content={'选择需要绑定的企业安全组'}>
                      <OutlinedQuestionCircle />
                    </Popover>
                  }
                </>
              }
              colon={false}
            >
              <Select
                loading={esgLoading}
                defaultValue={esgId}
                options={esgOptions}
                placeholder="请选择"
                style={{width: 400}}
              />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
};

export default ChangeEsg;
