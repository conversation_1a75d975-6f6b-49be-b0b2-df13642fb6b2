import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import {Button, Form, Input, Tag} from 'acud';
import {OutlinedPlusNew} from 'acud-icon';
import cloneDeep from 'lodash/cloneDeep';
import each from 'lodash/each';
import filter from 'lodash/filter';
import map from 'lodash/map';
import trim from 'lodash/trim';
import uniq from 'lodash/uniq';

import styles from './index.module.less';

interface tagEditPanelProps {
  defaultTags?: any[];
  validator?: (value: string, tags: string) => string;
  checkKeyUnique?: boolean;
  width?: number;
  isView?: boolean;
}

const MSETagEditPanel = forwardRef(
  (
    {
      defaultTags = [],
      validator,
      checkKeyUnique = true,
      width = 550,
      isView = false
    }: tagEditPanelProps,
    ref
  ) => {
    const [index, setIndex] = useState(0);
    const [forms, setForms] = useState([
      {
        data: {
          key: '',
          value: ''
        },
        error: null,
        key: index
      }
    ]);
    const [errorMsg, setErrorMsg] = useState('');

    const refs: any = useRef([React.createRef()]);

    useEffect(() => {
      if (defaultTags?.length) {
        loadData();
      }
    }, [defaultTags]);

    useEffect(() => {
      if (forms.length > refs.current.length) {
        refs.current.push(React.createRef());
      }
    }, [forms]);

    function loadData() {
      loadDefaultTags();
    }

    /**
     * 删除标签
     */
    function deleteHandler(form: any, index: number) {
      const newForms = filter(forms, (f: any) => f.key !== form.key);
      refs.current.splice(index, 1);
      setForms(cloneDeep(newForms));
    }

    // 父组件最终获取tag的方法，如果业务方获取时出错，则是校验不通过
    // 如果需要校验所有的tag，则获取后自己校验，有错误信息填入errorMsg中，做统一展示
    // errorMsg会随着用户输入的变动清除一次
    // 非dialog下，直接使用此方法获取页面输入的tag
    async function getTags() {
      try {
        await validate();
        const tags = pickTags();
        return filter(tags, (t) => t.key);
      } catch {}
    }

    /**
     * 从form数据中拿出所有用户填写的tag
     */
    function pickTags() {
      return map(forms, (form) => ({
        key: trim(form.data.key || ''),
        value: trim(form.data.value || '')
      }));
    }

    /**
     * 校验函数
     * @param allowEmpty
     */
    function validate(allowEmpty = false) {
      const validators: any[] = [];
      const tags: any[] = [];
      setErrorMsg('');
      if (forms?.length === 1) {
        const emptyKey = !trim(forms[0].data.key || '');
        const emptyValue = !trim(forms[0].data.value || '');
        if (emptyKey && emptyValue) {
          return Promise.resolve();
        }
      }
      each(forms, (form, index) => {
        const validateFields = refs?.current[index]?.current?.validateFields();
        validateFields && validators.push(validateFields);
        tags.push({
          key: form.data.key,
          value: form.data.value
        });
      });
      return Promise.all(validators)
        .then(() => {
          const keys = filter(
            map(forms, (form) => trim(form.data.key)),
            (key) => !!key
          );

          if (
            checkKeyUnique &&
            keys.length > 1 &&
            uniq(keys).length !== keys.length
          ) {
            // 如果需要检查 key 的唯一性，且存在重复的 key，则设置错误信息并拒绝 Promise。
            setErrorMsg('不能填写重复的标签键，请修改');
            return Promise.reject();
          }
        })
        .then(() => {
          if (typeof validator === 'function') {
            // @ts-ignore
            return validator(tags).catch((msg: any) => {
              setErrorMsg(msg);
              return Promise.reject();
            });
          }
        })
        .catch((e) => {
          return Promise.reject(e);
        });
    }

    /**
     * 添加标签
     */
    async function addHandler() {
      try {
        await validate();
        setForms([
          ...forms,
          {
            data: {
              key: '',
              value: ''
            },
            error: null,
            key: index + 1
          }
        ]);
        setIndex(index + 1);
        refs.current.push(React.createRef());
      } catch {}
    }

    function handleKeyChange(index: number, newValue: string) {
      const updatedForms = forms.map((form, idx) => {
        if (idx === index) {
          return {...form, data: {...form.data, key: newValue}};
        }
        return form;
      });
      setForms(updatedForms);
    }

    function handleValueChange(index: number, newValue: string) {
      const updatedForms = forms.map((form, idx) => {
        if (idx === index) {
          return {...form, data: {...form.data, value: newValue}};
        }
        return form;
      });

      setForms(updatedForms);
    }

    /**
     * 设置默认选中标签
     * @returns form
     */
    async function loadDefaultTags() {
      let forms: any = [];

      // 只有单个编辑时，才有已有数据的编辑
      if (defaultTags?.length) {
        // refs.current = [];
        forms = map(defaultTags, (item, index: number) => {
          refs.current = defaultTags.map(
            (_: any, index: number) => refs.current[index] || React.createRef()
          );
          return {
            data: {
              key: item.key,
              value: item.value
            },
            error: null,
            key: index
          };
        });

        setIndex(defaultTags.length - 1);
      }

      if (!forms || !forms.length) {
        forms = [
          {
            data: {
              key: '',
              value: ''
            },
            error: null,
            key: index
          }
        ];
      }

      each(forms, (form, index) => {
        refs?.current[index]?.current?.setFieldsValue({
          ...form.data
        });
      });
      setForms(forms);
    }

    const validKeyInput = async (_m: any, value: any) => {
      value = trim(value || '');

      if (!value) {
        return Promise.reject('标签键不能为空');
      }

      const matchStrArr = value.match(/[^0-9a-zA-Z_\-:.]+/g);

      if (matchStrArr && matchStrArr.length > 0) {
        return Promise.reject(`不允许包含"${matchStrArr[0]}"`);
      }

      return Promise.resolve();
    };

    const checkKey: any = [
      {
        max: 127,
        message: '标签键长度在127个字符以内'
      },
      {
        validator: validKeyInput
      }
    ];

    const validValueInput = async (_m: any, value: any) => {
      value = trim(value || '');

      if (!value) {
        return Promise.reject('标签值不能为空');
      }

      const matchStrArr = value.match(/[^0-9a-zA-Z_\-:.]+/g);
      if (matchStrArr && matchStrArr.length > 0) {
        return Promise.reject(`不允许包含"${matchStrArr[0]}"`);
      }

      return Promise.resolve();
    };

    const checkValue: any = [
      {
        max: 255,
        message: '标签值长度在255个字符以内'
      },
      {
        validator: validValueInput
      }
    ];

    useImperativeHandle(ref, () => ({
      getTags,
      pickTags,
      validate
    }));

    if (isView) {
      if (!defaultTags || !defaultTags.length) {
        return <span>-</span>;
      }
      return (
        <div className={styles['label-view-wrap']} style={{width: width}}>
          {defaultTags.map((d) => {
            return <Tag>{`${d.key}：${d.value}`}</Tag>;
          })}
        </div>
      );
    }

    return (
      <div className={styles['tag-edit-panel']} style={{width: width}}>
        <div className={styles['tag-form']}>
          {forms.map((form: any, index: number) => {
            return (
              <div key={form.key}>
                <Form
                  className={styles['inline-form']}
                  ref={refs.current[index]}
                >
                  <Form.Item
                    label={null}
                    initialValue={form.data.key}
                    name="key"
                    rules={checkKey}
                  >
                    <Input.AutoComplete
                      value={form.data.key}
                      style={{width: 220}}
                      placeholder="请输入标签键"
                      className={styles['tag-form-item']}
                      onChange={(value) => handleKeyChange(index, value)}
                    ></Input.AutoComplete>
                  </Form.Item>
                  <Form.Item
                    label={null}
                    initialValue={form.data.value}
                    name="value"
                    rules={checkValue}
                  >
                    <Input.AutoComplete
                      value={form.data.value}
                      style={{width: 220}}
                      placeholder="请输入标签值"
                      className={styles['tag-form-item']}
                      onChange={(value) => handleValueChange(index, value)}
                    ></Input.AutoComplete>
                  </Form.Item>
                  {forms.length > 1 && (
                    <Button
                      type="actiontext"
                      onClick={() => deleteHandler(form, index)}
                    >
                      删除
                    </Button>
                  )}
                </Form>
              </div>
            );
          })}
          {errorMsg && <span className={styles['error-msg']}>{errorMsg}</span>}
          <div className={styles['footer']}>
            <Button
              type="actiontext"
              className={styles['tag-button-add']}
              onClick={addHandler}
            >
              {
                // @ts-ignore
                <OutlinedPlusNew width="16px" />
              }
              添加标签
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

export default MSETagEditPanel;
