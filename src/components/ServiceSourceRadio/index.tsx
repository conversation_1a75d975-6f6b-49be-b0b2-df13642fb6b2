import React from 'react';
import { Radio } from 'acud';
import classNames from 'classnames';
import styles from './index.module.less';

interface ServiceSourceRadioProps {
  className?: string;
  value?: string;
  disabled?: boolean;
  description?: string;
  style?: React.CSSProperties;
}

const ServiceSourceRadio: React.FC<ServiceSourceRadioProps> = ({
  className,
  value = 'CCE',
  disabled = false,
  description = '用户部署在 CCE 上的推理服务',
  style
}) => {
  return (
    <Radio.Group value={value}>
      <Radio.Button
        value="CCE"
        className={classNames(styles['custom-radio-btn'], className)}
        disabled={disabled}
      >
        <div>
          <div className={styles['radio-title']}>容器引擎 CCE</div>
          <div className={styles['radio-desc']}>{description}</div>
        </div>
      </Radio.Button>
    </Radio.Group>
  );
};

export default ServiceSourceRadio;
