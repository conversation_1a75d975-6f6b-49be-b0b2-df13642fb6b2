.page-content:has(.custom-layout) {
  background-color: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
}

.page-wrapper:has(.custom-layout) .page-header {
  display: none;
}

// 左侧导航栏的宽度（全局生效）
.app-menu-container{
  .app-menu{
    width: 180px !important;
    transition: all 0.2s ease-in-out;
  }
}

.hidden-app-menu{
  .app-menu{
    width: 0px !important;
    transition: all 0.2s ease-in-out;
  }
}

.acud-tabs-nav{
  height: 40px;
}

.menu-aside-hide-bar::before{
  border-radius: 0 6px 6px 0 !important;
}



.acud-table-tbody > tr > td.action-cell {
  padding: 0 6px;

  .acud-btn {
    padding: 0 6px;
    height: 24px;
    min-width: auto;
  }

  .empty-text {
    padding: 0 6px;
  }
}
.custom-layout {
  height: 100%;
}
.mse-open-page-content {
  padding: 16px;
}
.mse-custom-header-wrap {
  background-color: #fff;
  padding: 0 16px;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .mse-custom-header-left {
    display: flex;
    align-items: center;
    .mse-custom-title-filter {
      font-size: 12px;
      margin-left: 12px;
      color: #5c5f66;
      font-weight: 400;
      .acud-select-selector {
        border: none !important;
        cursor: pointer !important;
        padding-left: 4px !important;
      }
      .acud-select-arrow {
        top: 48%;
      }
    }
  }
  .mse-custom-header-right {
    .mse-custom-header-link {
      color: #151a26; 
      display: none; // 隐藏帮助文档按钮
      align-items: center;
      svg {
        font-size: 14px;
      }
    }
  }
}
.mse-custom-page-content {
  height: calc(100% - 50px);
  background-color: #fff;
  border-radius: 6px;
  flex-grow: 1;
  margin: 16px;
  padding: 24px;
  .mse-custom-page-content-title {
    color: #000;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    margin-top: -4px;
    position: relative;
    z-index: 100;
  }
}

.mse-custom-page-operation-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  .mse-custom-page-operation-left,
  .mse-custom-page-operation-right {
    display: flex;
    gap: 8px;
  }
  .acud-input-search-multiple
    .acud-input-addon
    .acud-select:not(.acud-select-customize-input) {
    width: 100px;
  }
}

.common-back-btn {
  && {
    margin-right: 4px;
    font-size: 14px;
    color: #84878c;
  }
}

.custom-header-title {
  color: #151a26;
  font-weight: 500;
  font-size: 16px;
  .acud-link {
    font-weight: 500;
    font-size: 16px;
  }
}

.mse-detail-content-wrap {
  padding: 24px;
  .mse-detail-content-title {
    align-items: center;
    display: flex;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 16px;
    position: relative;
  }
  .mse-detail-operation-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    .mse-detail-operation-left,
    .mse-detail-operation-right {
      display: flex;
      gap: 8px;
    }
  }
}

.aigw-detail-content-wrap {
  padding: 0px;
  .aigw-detail-content-title {
    align-items: center;
    display: flex;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 16px;
    margin-top: 16px;
    position: relative;
  }
  .aigw-detail-operation-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    .aigw-detail-operation-left,
    .aigw-detail-operation-right {
      display: flex;
      gap: 8px;
    }
  }
}

svg {
  cursor: pointer;
}

.paginationContainer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;

  :global {
    .acud-pagination {
      width: fit-content;
    }
  }
}

.mse-create-modal {
  .acud-select {
    width: 100%;
  }
}

.mse-custom-form-error-wrap {
  color: #f33e3e;
  margin: 4px 0 0;
}

.mse-delete-modal-table {
  margin-top: 16px;
  .acud-table {
    min-height: auto !important;
  }
}

a {
  color: #2468f2;
}

.mse-font-weight {
  font-weight: 500;
}

.mse-detail-operation-total {
  font-size: 12px;
  min-height: 32px;
  color: #5c5f66;
  font-weight: 400;
  display: flex;
  align-items: center;
}

.mse-list-alert {
  margin-bottom: 16px;
}

.mse-detail-drawer {
  .drawer-release-type-wrap {
    display: flex;
    .acud-tag {
      margin-left: 8px;
    }
  }
}

.monaco-editor {
  .monaco-editor-background {
    background-color: #f7f7f9 !important;
  }
  .margin {
    background-color: #f7f7f9 !important;
  }
}

.react-diff-wrap {
  .react-diff-wrap-line {
    width: 1px;
    height: 280px;
    background-color: #e8e9eb;
    position: absolute;
    top: 40px;
    left: 475px;
  }
  pre {
    word-break: break-all !important;
  }
  tr td {
    vertical-align: baseline;
  }
}

.app-layout-container .page-wrapper .page-header-container {
  padding-bottom: 0;
  .acud-menu-horizontal > .acud-menu-item,
  .acud-menu-horizontal > .acud-menu-submenu {
    padding: 0 16px;
  }
  .page-header {
    height: 50px !important;
    background-color: #fff !important;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .page-title {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      padding: 0;
    }
  }
}


.acud-drawer-body {
  font-size: 12px !important;
}


.app-layout-container .page-wrapper .nav-page-content-container {
  border-radius: 6px !important;
}

.acudicon-outlined-question-circle {
  font-size: 16px !important;
}

.acud-table {
  .acud-empty {
    margin-top: 132px;
  }
}

.header-menu-container {
  .acud-menu-horizontal > .acud-menu-item-selected::after {
    width: 56px;
    left: 16px !important;
  }
}
.app-layout-container {
  min-width: 1280px;
}
.app-layout-container .page-wrapper {
  min-width: 1075px;
}


/* Radio.Button选中状态样式自定义 */
.acud-radio-button-checked:not(.acud-radio-button-disabled) {
  background: #EEF3FE !important;
  border-color: #2468f2 !important;
  color: #2468f2 !important;
  
  &::before {
    background-color: #2468f2 !important;
  }
}

/* 确保悬停状态也保持一致 */
.acud-radio-button-checked:not(.acud-radio-button-disabled):hover {
  background: #EEF3FE !important;
  border-color: #2468f2 !important;
  color: #2468f2 !important;
}
