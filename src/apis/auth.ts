/* eslint-disable @typescript-eslint/no-unused-vars */
import {request} from '@baidu/bce-react-toolkit';

/** 判断 mse 是否已经激活 */
export function queryIamStsRole(data): Promise<{
  success: boolean;
  status: number;
  result: {
    id: string;
    name: string;
    type: string;
    grantType: string;
    description: string;
    domain_id: string;
    create_time: string;
  };
}> {
  return request({
    url: '/api/iam/sts/role/query',
    method: 'POST',
    data
  });
}

/** 激活产品角色 */
export function activateIamStsRole(params: {
  roleName: string;
  accountId: string;
  serviceId: string;
  policyId: string;
}): Promise<{
  status: number;
  success: boolean;
}> {
  return request({
    url: '/api/iam/sts/role/activate',
    method: 'POST',
    data: params
  });
}

// 通知mse后端产品已激活
export function activeMse() {
  return request({
    url: `/api/mse/activate`,
    method: 'POST'
  });
}
