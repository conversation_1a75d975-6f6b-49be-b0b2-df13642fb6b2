import React, {createContext, ReactNode, useState} from 'react';

const SmartContext = createContext<{
  configInstance: string;
  setConfigInstance: any;
}>({configInstance: '', setConfigInstance: () => {}});

interface SmartProviderProps {
  children: ReactNode;
}

const SmartProvider: React.FC<SmartProviderProps> = ({children}) => {
  const [configInstance, setConfigInstance] = useState('');

  return (
    <SmartContext.Provider
      value={{
        configInstance: configInstance,
        setConfigInstance: setConfigInstance
      }}
    >
      {children}
    </SmartContext.Provider>
  );
};

export {SmartContext, SmartProvider};
