/**
 * 数值格式化工具函数
 * 解决图表Y轴小数值显示精度问题
 */

/**
 * 智能数值格式化，确保小数值能够正确显示
 * @param value 数值
 * @param minSignificantDigits 最小有效数字位数（默认3位）
 * @returns 格式化后的数值字符串
 */
export const formatNumberWithPrecision = (value: number, minSignificantDigits: number = 3): string => {
  // 处理特殊值
  if (value === 0) return '0';
  if (!isFinite(value) || isNaN(value)) return '0';
  
  const absValue = Math.abs(value);
  
  // 对于非常小的数值（小于0.001），使用科学计数法
  if (absValue < 0.001) {
    return value.toExponential(2);
  }
  
  // 对于小数值（0.001 <= value < 1），确保至少显示minSignificantDigits位有效数字
  if (absValue < 1) {
    // 计算需要的小数位数来显示指定位数的有效数字
    const log10Value = Math.log10(absValue);
    const decimalPlaces = Math.max(minSignificantDigits - Math.floor(log10Value) - 1, 0);
    const maxDecimalPlaces = Math.min(decimalPlaces, 8); // 最多8位小数，避免过长
    
    const formatted = value.toFixed(maxDecimalPlaces);
    
    // 移除尾随的0，但保留至少minSignificantDigits位有效数字
    const trimmed = parseFloat(formatted);
    
    // 如果trimmed为0，说明精度不够，增加小数位数
    if (trimmed === 0 && value !== 0) {
      return value.toFixed(8).replace(/\.?0+$/, '');
    }
    
    return trimmed.toString();
  }
  
  // 对于大于等于1的数值，使用2位小数
  if (absValue < 100) {
    return value.toFixed(2);
  }
  
  // 对于大数值，使用1位小数
  if (absValue < 1000) {
    return value.toFixed(1);
  }
  
  // 对于非常大的数值，不使用小数
  return Math.round(value).toString();
};

/**
 * 根据单位格式化数值
 * @param value 数值
 * @param unit 单位
 * @returns 格式化后的字符串
 */
export const formatValueWithUnit = (value: number | string | any, unit: string): string => {
  // 类型检查和转换
  if (value === null || value === undefined || value === '') {
    return '-';
  }

  const numValue = typeof value === 'number' ? value : parseFloat(value);
  if (isNaN(numValue)) {
    return '-';
  }

  // 根据单位进行格式化
  switch (unit) {
    case '%':
      return `${formatNumberWithPrecision(numValue * 100)}%`;
      
    case 'ms':
      return `${formatNumberWithPrecision(numValue)}ms`;
      
    case 'req/s':
    case '次/秒':
      return `${formatNumberWithPrecision(numValue)} ${unit}`;
      
    case 'bytes/s':
    case 'B/s':
      return formatBytesPerSecond(numValue);
      
    case 'MiB':
      return formatMemory(numValue);
      
    case '个':
      // 对于连接数等整数类型，保持整数显示
      return `${Math.round(numValue)}个`;
      
    case 'req':
      return `${Math.round(numValue)}`;
      
    default:
      return formatNumberWithPrecision(numValue);
  }
};

/**
 * 格式化字节/秒单位
 * @param bytes 字节数
 * @returns 格式化后的字符串
 */
export const formatBytesPerSecond = (bytes: number): string => {
  const absBytes = Math.abs(bytes);
  
  if (absBytes >= 1024 * 1024 * 1024) {
    return `${formatNumberWithPrecision(bytes / (1024 * 1024 * 1024))} GB/s`;
  } else if (absBytes >= 1024 * 1024) {
    return `${formatNumberWithPrecision(bytes / (1024 * 1024))} MB/s`;
  } else if (absBytes >= 1024) {
    return `${formatNumberWithPrecision(bytes / 1024)} KB/s`;
  } else {
    return `${formatNumberWithPrecision(bytes)} B/s`;
  }
};

/**
 * 格式化内存单位
 * @param mib MiB值
 * @returns 格式化后的字符串
 */
export const formatMemory = (mib: number): string => {
  const absMib = Math.abs(mib);
  
  if (absMib >= 1024) {
    return `${formatNumberWithPrecision(mib / 1024)} GiB`;
  } else {
    return `${formatNumberWithPrecision(mib)} MiB`;
  }
};

/**
 * 专门用于Y轴标签的格式化函数
 * Y轴标签通常需要更简洁的显示，避免过长的标签
 * @param value 数值
 * @param unit 单位
 * @returns 格式化后的字符串
 */
export const formatYAxisLabel = (value: number, unit: string): string => {
  // 对于Y轴标签，使用稍微简化的格式
  if (unit === '个') {
    // 连接数等整数类型，Y轴只显示数字，不显示单位
    return Math.round(value).toString();
  }
  
  // 其他单位使用标准格式化
  return formatValueWithUnit(value, unit);
};

/**
 * 检查数值是否需要使用科学计数法显示
 * @param value 数值
 * @returns 是否需要科学计数法
 */
export const shouldUseScientificNotation = (value: number): boolean => {
  const absValue = Math.abs(value);
  return absValue < 0.001 || absValue >= 1000000;
};

/**
 * 获取数值的有效数字位数
 * @param value 数值
 * @returns 有效数字位数
 */
export const getSignificantDigits = (value: number): number => {
  if (value === 0) return 1;
  
  const absValue = Math.abs(value);
  const log10Value = Math.log10(absValue);
  
  if (absValue >= 1) {
    return Math.floor(log10Value) + 1;
  } else {
    return Math.abs(Math.floor(log10Value));
  }
};
