.mse-import-modal {
  .import-dialog-footer {
    background: #ffffff;
    text-align: right;
    flex: none;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    :global {
      .acud-btn + .acud-btn {
        margin-left: 12px;
      }
    }
  }
  :global {
    .acud-modal-body {
      min-height: 260px;
      margin: 24px;
    }
    .acud-loading-loading-context {
      top: 46px;
      height: calc(100% - 46px);
    }
    .acud-row {
      flex-flow: row nowrap;
    }
    .acud-upload-list-text {
      max-width: 380px;
    }
  }
  .import-result-wrap {
    .import-result-item {
      margin-bottom: 16px;
      :global {
        .acud-table {
          min-height: auto;
        }
      }
      .import-result-title {
        font-size: 12px;
        color: #151b26;
        line-height: 20px;
        font-weight: 400;
        margin-bottom: 8px;
      }
    }
  }
}

.edit-cell-wrap {
  display: flex;
}

.edit-cell-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #2468f2;
}

.table-update-comment {
  width: 300px;
}
