import React from 'react';
import {useFrameworkContext, useTranslation} from '@baidu/bce-react-toolkit';
import {Alert} from 'acud';
import cx from 'classnames';

import Banner from './components/Banner';
import styles from './index.module.less';

const Activation: React.FC = () => {
  const {isRealNameVerified} = useFrameworkContext();

  const {t} = useTranslation();

  return (
    <div className="custom-layout" style={{overflow: 'auto'}}>
      <div className="mse-open-page-content">
        {!isRealNameVerified ? (
          <Alert
            message={
              <div className={styles.alertContentContainer}>
                <span>您的账号未通过实名认证，将无法开通，建议尽快进行</span>
                <a
                  className={styles.verifyRealNameBtn}
                  href="https://console.bce.baidu.com/qualify/#/qualify/index"
                >
                  实名认证
                </a>
                。
              </div>
            }
            banner
            className={styles.alertContainer}
          />
        ) : null}

        <Banner></Banner>

        <div className={styles.introModule}>
          <div className={styles.title}>产品优势</div>

          <div
            className={cx(
              styles.introItemContainer,
              styles.introItemContainerMaxGap
            )}
          >
            <div className={styles.introItem}>
              <div className={styles.header}>
                <img
                  className={styles.icon}
                  src="https://bce.bdstatic.com/cce-frontend/mse/operation.png"
                />
                <div className={styles.headerTitle}>推理服务管理</div>
              </div>
              <div className={styles.desc}>
                云原生网关实现推理服务的负载均衡，您无需投入大量人力进行运维，让您专注于 AI 应用的开发与创新。
              </div>
            </div>

            <div className={styles.introItem}>
              <div className={styles.header}>
                <img
                  className={styles.icon}
                  src="https://bce.bdstatic.com/cce-frontend/mse/extension.png"
                />
                <div className={styles.headerTitle}>高效扩展</div>
              </div>
              <div className={styles.desc}>
                云原生网关支持动态扩展和收缩，能够智能应对不同规模的流量需求，保障业务的高可用和低延迟运行。
              </div>
            </div>

            <div className={styles.introItem}>
              <div className={styles.header}>
                <img
                  className={styles.icon}
                  src="https://bce.bdstatic.com/cce-frontend/mse/safe.png"
                />
                <div className={styles.headerTitle}>安全可靠</div>
              </div>
              <div className={styles.desc}>
                云原生网关提供 Key Auth 等认证鉴权策略以及消费者管理能力，确保您的服务持续、稳定、安全地运行。
              </div>
            </div>
          </div>
        </div>

        <div className={styles.introModule}>
          <div className={styles.title}>{t('核心能力')}</div>

          <div className={styles.introItemContainer}>
            <div className={cx(styles.introItemWithBorder)}>
              <div className={styles.header}>
                <img
                  className={styles.icon}
                  src="https://bce.bdstatic.com/cce-frontend/mse/registry.png"
                />
                <div className={styles.headerTitle}>容器服务流量管控</div>
              </div>
              <div className={styles.desc}>
              支持 K8s 等多种发现方式，具备高集成、易使用、易扩展、热更新的特点。支持一键导入 K8s SVC，请求直连 Pod IP，并提供负载均衡等服务治理能力。              </div>
            </div>

            <div className={cx(styles.introItemWithBorder)}>
              <div className={styles.header}>
                <img
                  className={styles.icon}
                  src="https://bce.bdstatic.com/cce-frontend/mse/manager.png"
                />
                <div className={styles.headerTitle}>AI 应用流量入口与集成</div>
              </div>
              <div className={styles.desc}>
              面向 Kubernetes 自有模型服务，提供高性能/高可用/热更新的网关服务；面向 AI 应用开发者，通过低代码方式高效快捷地构建 AI 应用。
              </div>
            </div>

            {/* <div className={cx(styles.introItemWithBorder)}>
              <div className={styles.header}>
                <img
                  className={styles.icon}
                  src="https://bce.bdstatic.com/cce-frontend/mse/warning.png"
                />
                <div className={styles.headerTitle}>实时监控与告警</div>
              </div>
              <div className={styles.desc}>
                支持实时监控 AI 服务的性能指标，包括请求延迟、吞吐量和错误率等，有助于用户掌握服务的运行状况，从而能够及时处理潜在风险并提高AI应用的稳定性。
              </div>
            </div>

            <div className={cx(styles.introItemWithBorder)}>
              <div className={styles.header}>
                <img
                  className={styles.icon}
                  src="https://bce.bdstatic.com/cce-frontend/mse/center.png"
                />
                <div className={styles.headerTitle}>安全与流量控制</div>
              </div>
              <div className={styles.desc}>
                提供全面的安全防护和流量控制能力，包括认证授权、限流熔断和防DDoS攻击等，保障 AI 服务的安全稳定运行。
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Activation;
