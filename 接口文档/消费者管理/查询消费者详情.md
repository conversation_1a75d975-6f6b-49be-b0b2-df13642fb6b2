# 查询消费者详情接口

## 接口基本信息

- **接口说明**：根据消费者ID查询消费者详情
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/consumer/{consumerId}`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| instanceId | String | 是 | 实例ID |
| consumerId | String | 是 | 消费者ID |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "result": {
        "consumerId": "consumer-001",
        "consumerName": "test-consumer",
        "description": "测试用消费者",
        "authType": "KeyAuth",
        "authInfo": {
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        },
        "unlimitedQuota": true,
        "totalQuota": -1,
        "quotaValue": -1,
        "routes": [
            {
                "routeName": "route-001",
                "createTime": "2023-06-10 09:15:30",
                "authEnabled": true
            },
            {
                "routeName": "route-002",
                "createTime": "2023-06-12 14:30:25",
                "authEnabled": false
            }
        ]
    },
    "status": 200
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 消费者详情信息 |
| result.consumerId | String | 消费者ID |
| result.consumerName | String | 消费者名称 |
| result.description | String | 消费者描述，若无则为空字符串 |
| result.authType | String | 认证方式 |
| result.authInfo | Object | 认证信息 |
| result.authInfo.token | String | 认证token（后端自动生成） |
| result.unlimitedQuota | Boolean | 是否开启不限额，true表示不限额，false表示限额 |
| result.totalQuota | Integer | 总配额值，若开启不限额则返回-1，否则返回用户设置的总配额数值 |
| result.quotaValue | Integer | 剩余配额值，若开启不限额则返回-1，否则返回当前剩余的配额数值 |
| result.routes | Array | 已授权路由列表 |
| result.routes[].routeName | String | 路由名称 |
| result.routes[].createTime | String | 路由创建时间，格式：YYYY-MM-DD HH:mm:ss |
| result.routes[].authEnabled | Boolean | 路由是否开启消费者认证 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | 实例或消费者不存在 | 确认实例ID和消费者ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/i-a1b2c3d4/consumer/consumer-001
```

## 响应示例

### 示例1：不限额消费者

```json
{
    "success": true,
    "result": {
        "consumerId": "consumer-001",
        "consumerName": "test-consumer",
        "description": "测试用消费者",
        "authType": "KeyAuth",
        "authInfo": {
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        },
        "unlimitedQuota": true,
        "totalQuota": -1,
        "quotaValue": -1,
        "routes": [
            {
                "routeName": "route-001",
                "createTime": "2023-06-10 09:15:30",
                "authEnabled": true
            }
        ]
    },
    "status": 200
}
```

### 示例2：限额消费者

```json
{
    "success": true,
    "result": {
        "consumerId": "consumer-002",
        "consumerName": "limited-consumer",
        "description": "限额消费者",
        "authType": "KeyAuth",
        "authInfo": {
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        },
        "unlimitedQuota": false,
        "totalQuota": 5000,
        "quotaValue": 2500,
        "routes": [
            {
                "routeName": "route-001",
                "createTime": "2023-06-10 09:15:30",
                "authEnabled": true
            }
        ]
    },
    "status": 200
}
``` 