import React from 'react';
import {MenuItem, recursiveMenus} from '@baidu/bce-react-toolkit';

import ConfigManageHeader from '@/components/ConfigManageHeader';
import urls from '@/utils/urls';

const InstanceList = React.lazy(
  () => import(/* webpackChunkName: "InstanceList" */ '@/pages/InstanceList')
);

const ConfigList = React.lazy(
  () => import(/* webpackChunkName: "InstanceList" */ '@/pages/ConfigList')
);

const HistoryList = React.lazy(
  () =>
    import(/* webpackChunkName: "InstanceList" */ '@/pages/ReleaseHistoryList')
);

const ServiceManage = React.lazy(
  () => import(/* webpackChunkName: "InstanceList" */ '@/pages/ServiceManage')
);

const CreateInstance = React.lazy(
  () => import(/* webpackChunkName: "InstanceList" */ '@/pages/CreateInstance')
);

const InstanceDetail = React.lazy(
  () => import(/* webpackChunkName: "InstanceList" */ '@/pages/InstanceDetail')
);

const ConfigDetail = React.lazy(
  () => import(/* webpackChunkName: "InstanceList" */ '@/pages/ConfigDetail')
);

const ServiceTnstanceList = React.lazy(
  () =>
    import(/* webpackChunkName: "InstanceList" */ '@/pages/ServiceInstanceList')
);

const ServiceInstanceBaseInfo = React.lazy(
  () =>
    import(
      /* webpackChunkName: "InstanceList" */ '@/pages/ServiceInstanceBaseInfo'
    )
);

// 添加推理服务组件导入
const InferenceService = React.lazy(
  () => import(/* webpackChunkName: "InferenceService" */ '@/pages/InferenceService')
);

// 添加路由创建组件导入
const RouteCreate = React.lazy(
  () => import(/* webpackChunkName: "RouteCreate" */ '@/pages/Route/Create')
);

// 添加路由编辑组件导入
const RouteEdit = React.lazy(
  () => import(/* webpackChunkName: "RouteEdit" */ '@/pages/Route/Edit')
);

// 添加路由详情组件导入
const RouteDetail = React.lazy(
  () => import(/* webpackChunkName: "RouteDetail" */ '@/pages/RouteDetail')
);

// 添加IP黑白名单组件导入
const IpBlackWhiteList = React.lazy(
  () => import(/* webpackChunkName: "IpBlackWhiteList" */ '@/pages/IpBlackWhiteList')
);

// 添加监控面板组件导入
const MonitorDashboard = React.lazy(
  () => import(/* webpackChunkName: "MonitorDashboard" */ '@/pages/MonitorDashboard')
);

/** 菜单定义 */
const menus: MenuItem[] = [
  {
    menuName: '网关实例',
    key: urls.registrationList,
    isNavMenu: true,
    Component: InstanceList,
    isPageLayoutCustomized: true, // 是否自定义页面布局
  },
  {
    menuName: '监控面板',
    key: urls.monitorDashboard,
    isNavMenu: true,
    Component: MonitorDashboard,
    isPageLayoutCustomized: true, // 是否自定义页面布局
  },
  // {
  //   menuName: '服务管理',
  //   key: urls.serviceManage,
  //   isNavMenu: false, // 导航栏不显示服务管理页面,设置为 true 表示这是一个导航菜单项，会在左侧导航栏显示
  //   Component: ServiceManage
  // },
  {
    menuName: '服务列表',
    key: urls.serviceInstanceList,
    isNavMenu: false,
    activeMenuKey: urls.serviceManage,
    Component: ServiceTnstanceList
  },
  // 添加推理服务菜单项
  {
    menuName: '推理服务',
    key: urls.inferenceService,
    isNavMenu: false,
    Component: InferenceService,
    isPageWrapperNotRequired: true
  },
  // {
  //   menuName: '配置管理',
  //   key: '/config/manage',
  //   isNavMenu: false, // 导航栏不显示配置管理页面
  //   hasHeaderMenu: true,
  //   renderSubtitle: () => <ConfigManageHeader />,
  //   children: [
  //     {
  //       menuName: '配置分组',
  //       activeMenuKey: '/config/manage',
  //       key: urls.configList,
  //       isHeaderNav: true,
  //       isNavMenu: true,
  //       Component: ConfigList
  //     },
  //     {
  //       menuName: '发布历史',
  //       activeMenuKey: '/config/manage',
  //       key: urls.releaseHistory,
  //       isHeaderNav: true,
  //       isNavMenu: true,
  //       Component: HistoryList
  //     }
  //   ]
  // },
  // {
  //   menuName: '服务实例详情',
  //   key: urls.serviceInstanceBaseInfo,
  //   isNavMenu: false,
  //   Component: ServiceInstanceBaseInfo,
  //   isPageWrapperNotRequired: true
  // },
  {
    menuName: '创建实例',
    key: urls.createRegistration,
    isNavMenu: false,
    Component: CreateInstance,
    isPageWrapperNotRequired: true
  },
  {
    menuName: '实例详情',
    key: urls.registrationBaseInfo,
    isNavMenu: false,
    Component: InstanceDetail,
    isPageWrapperNotRequired: true
  },
  // {
  //   menuName: 'Prometheus 监控',
  //   key: urls.monitor,
  //   isNavMenu: false,
  //   Component: InstanceDetail,
  //   isPageWrapperNotRequired: true
  // },
  // {
  //   menuName: '配置文件',
  //   key: urls.configFileList,
  //   isNavMenu: false,
  //   Component: ConfigDetail,
  //   isPageWrapperNotRequired: true
  // },
  // {
  //   menuName: '历史版本',
  //   key: urls.historyVersion,
  //   isNavMenu: false,
  //   Component: ConfigDetail,
  //   isPageWrapperNotRequired: true
  // },
  {
    menuName: '创建路由',
    key: urls.routeCreate,
    isNavMenu: false,
    Component: RouteCreate,
    isPageWrapperNotRequired: true
  },
  {
    menuName: '编辑路由',
    key: urls.routeEdit,
    isNavMenu: false,
    Component: RouteEdit,
    isPageWrapperNotRequired: true
  },
  {
    menuName: '路由详情',
    key: urls.routeDetail,
    isNavMenu: false,
    Component: RouteDetail,
    isPageWrapperNotRequired: true
  },
  {
    menuName: 'IP黑白名单',
    key: urls.ipBlackWhiteList,
    isNavMenu: false,
    Component: IpBlackWhiteList,
    isPageWrapperNotRequired: true
  }
];

/** 打平之后的菜单列表 */
export const flattenedMenuList = recursiveMenus(menus);

export default menus;
