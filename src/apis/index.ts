import {request} from '@baidu/bce-react-toolkit';
import type {InternalAxiosRequestConfig} from 'axios';

export const BASE_URL = '/api/aigw/v1';

// 目前灰度环境为gz，但是前端展示只有 bj，先这样测试，上线前去掉
// 全局的Axios请求拦截器，它会强制将所有请求的X-Region请求头设置为'gz'：
// request.interceptors.request.use(
//   (config: InternalAxiosRequestConfig) => {
//     config.headers['X-Region'] = 'gz';

//     return config;
//   },
//   (err) => Promise.reject(err)
// );
