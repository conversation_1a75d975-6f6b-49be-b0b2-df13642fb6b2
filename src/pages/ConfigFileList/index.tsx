import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {Button, Search, Table, Tag, Tooltip} from 'acud';
import {useRequest} from 'ahooks';

import {getConfigFileList} from '@/apis/configManage';
import EllipsisWrap from '@/components/Ellipsis';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';
import RefreshButton from '@/components/RefreshButton';
import {ConfigFileStatusDict} from '@/utils/enums';

import {CreateModal, deleteConfirm, ReleaseModal, ViewDrawer} from './modals';

const ConfigFileList = () => {
  const {instanceId, configGroupName, namespace} = getQueryParams();
  const [dataSource, setDataSource] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [seacherValue, setSeacherValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<React.Key[]>([]);
  const [status, setStatus] = useState('');

  const _getConfigFileList = useCallback(
    async (params: any) => {
      return getConfigFileList({
        instanceId: instanceId,
        pageNo,
        pageSize,
        group: configGroupName,
        namespace,
        keywordType: 'name',
        keyword: seacherValue,
        status,
        ...params
      });
    },
    [
      pageNo,
      pageSize,
      seacherValue,
      instanceId,
      configGroupName,
      namespace,
      status
    ]
  );

  const setRowEmpty = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const {
    run,
    loading,
    refresh: refreshInstanceList
  } = useRequest(_getConfigFileList, {
    manual: true,
    onSuccess: (res) => {
      setRowEmpty();
      setDataSource(res?.result?.configFiles || []);
      setTotal(res?.result?.totalCount || 0);
    }
  });

  useEffect(() => {
    if (configGroupName && namespace) {
      run({});
    }
  }, [configGroupName, namespace]);

  const onConfirmSearch = (value: any) => {
    setSeacherValue(value);
    setPageNo(1);
    run({
      keyword: value,
      pageNo: 1
    });
  };

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    refreshInstanceList();
  }, [refreshInstanceList]);

  const columns = useMemo(() => {
    return [
      {
        title: '配置文件名',
        dataIndex: 'name',
        width: 150,
        className: 'action-cell',
        render(value, record) {
          return (
            <ViewDrawer
              initialValues={record}
              groupInfo={{namespace, group: configGroupName}}
              instanceId={instanceId}
              width={125}
            />
          );
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 150,
        filters: [
          {text: '待发布', value: 'to-be-released'},
          {text: '已发布', value: 'normal'},
          {text: '发布失败', value: 'failure'}
        ],
        filterMultiple: false,
        render: (value: string) => {
          const data = ConfigFileStatusDict.get(value);
          return (
            <Tag
              color="transparent"
              className="table-status"
              style={{paddingLeft: 0}}
              icon={<span className={data?.iconClass} />}
            >
              {data?.text || '-'}
            </Tag>
          );
        }
      },
      {
        title: '使用中版本号',
        dataIndex: 'currentReleaseName',
        width: 150,
        render: (value: string) => {
          return <EllipsisWrap data={value} width={125} />;
        }
      },
      {
        title: '最后修改时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: '20%'
      },
      {
        title: '最后发布时间',
        dataIndex: 'releaseTime',
        key: 'releaseTime',
        width: '20%',
        render: (value: string) => {
          if (value === '0001-01-01 00:00:00') {
            return '-';
          }
          return value || '-';
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: 160,
        className: 'action-cell',
        render(name, record) {
          return (
            <React.Fragment>
              <ReleaseModal
                fileInfo={record}
                instanceId={instanceId}
                refresh={refreshInstanceList}
              />
              <CreateModal
                isEdit={true}
                initialValues={record}
                instanceId={instanceId}
                refresh={refreshInstanceList}
                groupInfo={{namespace, group: configGroupName}}
              />
              <Tooltip
                trigger={selectedRows?.length > 1 ? 'hover' : ''}
                title="批量删除请使用列表上方的删除按钮"
              >
                <Button
                  type="actiontext"
                  disabled={selectedRows?.length > 1}
                  onClick={deleteConfirm(instanceId, refreshInstanceList, [
                    record
                  ])}
                >
                  删除
                </Button>
              </Tooltip>
            </React.Fragment>
          );
        }
      }
    ];
  }, [selectedRows, instanceId, refreshInstanceList]);

  const onChange = (pag, filter) => {
    const {status} = filter;
    setStatus(status?.[0] || '');
    run({
      status: status?.[0] || ''
    });
  };

  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    newSelectedRows: any
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(newSelectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange
  };

  return (
    <div className="mse-detail-content-wrap">
      <div className="mse-detail-content-title">配置文件列表</div>
      <div className="mse-detail-operation-container">
        <div className="mse-detail-operation-left">
          <CreateModal
            refresh={refreshInstanceList}
            instanceId={instanceId}
            groupInfo={{namespace, group: configGroupName}}
          />
          <Button
            disabled={selectedRows?.length === 0}
            onClick={deleteConfirm(
              instanceId,
              refreshInstanceList,
              selectedRows
            )}
          >
            删除
          </Button>
          <div className="mse-detail-operation-total">
            已选中{selectedRows.length}条，共{total}条
          </div>
        </div>
        <div className="mse-detail-operation-right">
          <Search
            placeholder="请输入配置文件名搜索"
            style={{width: 300}}
            onSearch={onConfirmSearch}
          />
          <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
        </div>
      </div>
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="name"
        onChange={onChange}
        loading={{
          loading: loading,
          size: 'small'
        }}
        pagination={false}
        rowSelection={{
          // type: selectionType,
          ...rowSelection
        }}
      />

      <div className="paginationContainer">
        <PaginationOfAutoHide
          showSizeChanger={true}
          showQuickJumper={true}
          current={pageNo}
          pageSize={pageSize}
          total={total}
          onChange={(page, pageSize) => {
            setPageNo(page);
            setPageSize(pageSize!);
            run({pageNo: page, pageSize});
          }}
        />
      </div>
    </div>
  );
};

export default ConfigFileList;
