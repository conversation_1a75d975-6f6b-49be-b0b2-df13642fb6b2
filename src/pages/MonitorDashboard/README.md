# 监控面板功能模块

## 功能概述

监控面板是网关控制台的核心功能模块，用于实时展示网关实例的各项监控指标，帮助用户了解网关的运行状态和性能表现。

## 功能特性

### 1. 导航结构
- 在左侧导航栏"网关实例"下方新增"监控面板"选项
- 点击后切换到监控面板页面

### 2. 控制面板
- **地域选择**：支持选择不同地域（当前仅支持华北-北京）
- **实例选择**：动态加载并选择网关实例
- **时间筛选**：支持7个时间范围（近1小时、近6小时、近1天、近7天、近14天、近28天）
- **刷新功能**：一键刷新所有监控数据

### 3. 监控图表
支持7个核心监控指标：
1. 网关CPU使用率（%）
2. 网关内存使用率（%）
3. 内存负载（MiB）
4. 网络入方向IO负载（B/s）
5. 网络出方向IO负载（B/s）
6. 磁盘Read负载（B/s）
7. 磁盘Write负载（B/s）

### 4. 图表功能
- **时间轴滑动条**：支持缩放和平移查看不同时间段的数据
- **全屏显示**：每个图表都支持全屏查看
- **数据格式化**：自动格式化数值显示（B/KB/MB/GB转换）
- **响应式布局**：支持不同屏幕尺寸的自适应显示

## 技术实现

### 1. 文件结构
```
src/pages/MonitorDashboard/
├── index.tsx                    # 主组件
├── index.module.less           # 主样式文件
├── components/
│   ├── MonitorChart.tsx        # 图表组件
│   └── MonitorChart.module.less # 图表样式文件
└── README.md                   # 说明文档
```

### 2. 核心组件

#### MonitorDashboard（主组件）
- 负责页面布局和状态管理
- 处理地域、实例、时间范围的选择
- 协调各个图表组件的数据刷新

#### MonitorChart（图表组件）
- 基于ECharts实现的可复用图表组件
- 支持数据查询、格式化、全屏等功能
- 自动处理图表的初始化和更新

### 3. API接口

#### 监控数据查询
- **接口路径**：`/v1/aigateway/{instanceId}/metrics/query_range`
- **请求方法**：GET
- **参数说明**：
  - `metricType`: 指标类型（固定为'basic'）
  - `query`: PromQL查询语句
  - `start`: 开始时间（Unix时间戳）
  - `end`: 结束时间（Unix时间戳）
  - `step`: 数据步长（秒）

#### PromQL查询映射
每个监控指标对应特定的PromQL查询语句，通过替换`$namespace`变量实现实例级别的数据查询。

### 4. 数据处理

#### 命名空间规则
- 格式：`istio-system-{instanceId}`
- 示例：实例ID为`aigw-j9xpshig`时，namespace为`istio-system-aigw-j9xpshig`

#### 时间范围计算
- 根据选择的时间范围自动计算开始和结束时间戳
- 支持小时和天两种时间单位

#### 数据格式化
- CPU/内存使用率：百分比显示
- 网络/磁盘IO：自动转换B/KB/MB/GB单位
- 内存负载：MiB单位显示

## 使用说明

### 1. 访问监控面板
1. 登录网关控制台
2. 在左侧导航栏点击"监控面板"
3. 系统自动加载监控面板页面

### 2. 选择监控目标
1. 选择地域（当前仅支持华北-北京）
2. 从下拉列表中选择要监控的网关实例
3. 选择时间范围（默认为近1小时）

### 3. 查看监控数据
1. 系统自动加载并显示7个监控图表
2. 可以通过图表下方的滑动条调整显示的时间范围
3. 点击图表右上角的全屏按钮可以全屏查看
4. 点击刷新按钮可以更新所有图表数据

### 4. 图表交互
- **缩放**：拖动滑动条两端可以调整显示的时间范围
- **平移**：拖动滑动条中间部分可以平移时间窗口
- **悬停**：鼠标悬停在图表上可以查看具体时间点的数值
- **全屏**：点击全屏按钮可以放大查看图表详情

## 注意事项

1. **数据延迟**：监控数据可能存在1-2分钟的延迟
2. **网络依赖**：需要确保网络连接正常，否则可能无法加载数据
3. **权限要求**：需要有相应的网关实例访问权限
4. **浏览器兼容**：建议使用现代浏览器以获得最佳体验

## 故障排查

### 1. 图表无数据显示
- 检查网关实例是否正常运行
- 确认选择的时间范围内是否有数据
- 检查网络连接是否正常

### 2. 页面加载失败
- 检查浏览器控制台是否有错误信息
- 确认API接口是否正常响应
- 检查用户权限是否足够

### 3. 图表显示异常
- 尝试刷新页面
- 检查浏览器是否支持ECharts
- 确认屏幕分辨率是否过低

## 更新日志

### 2024-01-XX v1.1
- 统一页面样式，与网关实例列表页面保持一致
- 添加统一的页面Header，标题为"监控大盘"
- 修改实例列表获取逻辑，正确调用查询实例列表接口
- 优化实例选择下拉框，只显示运行中的实例
- 统一图标使用：全屏图标使用 OutlinedArrowsAlt，退出全屏使用 OutlinedShrink
- 修复刷新图标，使用 OutlinedRefresh

### 2024-01-XX v1.0
- 初始版本创建
- 实现基础监控面板功能
- 添加7个核心监控指标
- 实现时间范围选择和实例筛选功能
