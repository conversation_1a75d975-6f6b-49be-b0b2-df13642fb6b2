.mse-create-config-file-modal {
  max-height: 80vh;
  :global {
    .acud-modal-content {
      max-height: 80vh;
    }
  }
}

.mse-release-config-file-modal {
  .step-wrap {
    width: 320px;
    margin: 0 auto;
    margin-bottom: 30px;
  }
  .import-dialog-footer {
    background: #ffffff;
    text-align: right;
    flex: none;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    :global {
      .acud-btn + .acud-btn {
        margin-left: 12px;
      }
    }
  }
  .mse-release-diff-editor-wrap {
    position: relative;
    .diff-top-wrap {
      display: flex;
      width: 100%;
      height: 40px;
      border-radius: 4px 4px 0px 0px;
      border: 1px solid rgba(232, 233, 235, 1);
      border-bottom: none;
      .diff-top-item {
        width: 475px;
        height: 100%;
        padding-left: 16px;
        border-right: 1px solid rgba(232, 233, 235, 1);
        line-height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:last-child {
          border-right: none;
        }
      }
    }
    table {
      tr {
        td:nth-child(1) {
          min-width: 49px;
        }
        td:nth-child(6),
        td:nth-child(3) {
          width: 400px;
        }
        td:nth-child(2),
        td:nth-child(5) {
          pre {
            width: 5px;
          }
        }
      }
    }
  }
  .release-visible {
    visibility: hidden;
    height: 0;
  }
}

.eddipsis-wrap {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
