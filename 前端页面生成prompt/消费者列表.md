现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、路由配置、消费者管理），点击每个导航栏右侧会切换成当前导航栏下对应的页面
2. 需求描述：现在，我期望在这 4 个导航栏最下方新增一个导航栏【消费者列表】，用于展示当前实例中的所有消费者以及相关的属性，接下来在第 3 点页面交互中我会描述选中消费者列表这个导航栏后右侧页面的样式和交互，你需要在遵循相关规范的前提下生成这个页面，另外，这个页面中所有字段和字段值来源于查询消费者列表这个接口，接口文档请参考接口文档文件夹中 查询消费者列表接口.md，你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数生成这个页面并和接口对应上。
3. 页面交互：消费者列表页面包含 1 个子=标签页=，为消费者列表，默认选中这个标签页。在这个标签页下方有 1. 创建消费者=按钮= 2. 刷新=按钮=，创建消费者按钮靠页面左侧，刷新按钮靠页面右侧，这个布局可以参考实例详情导航中的基本信息页面。在这两个元素下方有一个=列表=，用于展示当前实例中的所有消费者，列表包含 4 列（消费者名称/ID、描述、创建时间、操作），列表支持=分页=，默认 10 条/页。其中消费者名称/ID 这一列中消费者名称和 ID 需要分成 2 行展示，操作列中包含 2 个操作：编辑、删除。消费者名称/ID、描述、创建时间这 3 个参数来自于查询消费者列表接口中的返回字段，所以，在刚开始进入消费者列表页面的时候就需要调用查询消费者列表这个接口用于回显字段值，同时，当用户点击刷新按钮的时候也需要重新调用查询消费者列表。另外，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。因为现在只有点击实例名称才会进入实例详情页，才会包含消费者列表这个导航栏。
4. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。
5. 接口文档：参见附件
6. 遵守原则：尽量不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你生成消费者列表这个页面，所以你不应该再自动为我完善点击创建消费者按钮之后的交互页面。