# 创建IP黑白名单接口

## 接口基本信息

- **接口说明**：创建一个新的IP黑白名单规则
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/ipRestriction`
- **请求方式**：POST
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 请求体

```json
{
    "enabled": true,
    "name": "internal-whitelist",
    "description": "内网IP白名单规则",
    "type": "whitelist",
    "scope": "global",
    "ipAddresses": [
        "***********/24",
        "********",
        "**********/16"
    ]
}
```

### 请求体字段说明

| 字段名 | 类型 | 是否必填 | 描述 | 示例值 | 限制 |
| --- | --- | --- | --- | --- | --- |
| enabled | Boolean | 是 | 是否开启 | true | true: 开启, false: 关闭 |
| name | String | 是 | 名称 | internal-whitelist | 长度为2-64个字符，可包含字母、数字、下划线(_)和连字符(-) |
| description | String | 否 | 备注 | 内网IP白名单规则 | 最大长度200个字符 |
| type | String | 是 | 类型 | whitelist | 可选值：whitelist(白名单)、blacklist(黑名单) |
| scope | String | 否 | 生效粒度 | global | 默认值为global（全局生效） |
| ipAddresses | Array[String] | 是 | IP地址/地址段 | ["***********/24", "********"] | 支持单个IP地址或CIDR格式的网段，数组长度不超过100 |

## 响应参数

### 成功响应

```json
{
    "success": true,
    "status": 200,
    "result": {
        "id": "ip-restriction-001",
        "enabled": true,
        "name": "internal-whitelist",
        "description": "内网IP白名单规则",
        "type": "whitelist",
        "scope": "global",
        "ipAddresses": [
            "***********/24",
            "********",
            "**********/16"
        ],
        "createTime": "2023-06-15 10:30:00"
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 创建的IP黑白名单信息 |
| result.id | String | IP黑白名单规则ID |
| result.enabled | Boolean | 是否开启 |
| result.name | String | 名称 |
| result.description | String | 备注 |
| result.type | String | 类型，whitelist表示白名单，blacklist表示黑名单 |
| result.scope | String | 生效粒度 |
| result.ipAddresses | Array[String] | IP地址/地址段列表 |
| result.createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 409 | 名称已存在 | 使用不同的名称 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
POST /api/aigw/v1/aigateway/gw-ist9vvin/ipRestriction
Content-Type: application/json
X-Region: gz
```

```json
{
    "enabled": true,
    "name": "internal-whitelist",
    "description": "内网IP白名单规则",
    "type": "whitelist",
    "scope": "global",
    "ipAddresses": [
        "***********/24",
        "********",
        "**********/16"
    ]
}
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "id": "ip-restriction-001",
        "enabled": true,
        "name": "internal-whitelist",
        "description": "内网IP白名单规则",
        "type": "whitelist",
        "scope": "global",
        "ipAddresses": [
            "***********/24",
            "********",
            "**********/16"
        ],
        "createTime": "2023-06-15 10:30:00"
    }
}
``` 