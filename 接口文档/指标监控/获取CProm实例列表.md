# 获取CProm实例列表

## 接口描述

获取当前用户可用的CProm（Cloud Prometheus）实例列表，用于配置AI网关的业务指标监控。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/cprom/instances
```

### 请求方法
GET

### 请求参数

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": [
    {
      "instanceId": "cprom-x35s7xaq7ezx7",
      "instanceName": "my-prometheus-instance",
      "region": "bj",
      "metadata": {
        "name": "",
        "labels": {
          "cprom-account-id": "1093",
          "cprom-instance-template": "advance-v2",
          "cprom-instance-type": "default",
          "cprom-namespace": "cprom-80h84dy147ra7",
          "cprom-project-id": "0"
        },
        "annotations": null,
        "creationTimestamp": "2025-07-17T20:19:38+08:00"
      },
      "spec": {
        "instanceID": "cprom-x35s7xaq7ezx7",
        "instanceName": "my-prometheus-instance",
        "region": "bj",
        "vmClusterConfig": {
          "retentionPeriod": "15d"
        },
        "grafanaConfig": {
          "enable": false,
          "adminPassword": ""
        },
        "tags": null
      },
      "status": {
        "phase": "Running",
        "ready": true,
        "message": "all components are ready",
        "accessEndpoint": {
          "privateDomain": "",
          "publicDomain": "cprom.bj.baidubce.com"
        }
      },
      "monitorGrafanaId": "",
      "monitorGrafanaName": ""
    }
  ],
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Array | CProm实例列表 |
| result[].instanceId | String | CProm实例ID |
| result[].instanceName | String | CProm实例名称 |
| result[].region | String | 实例所在区域 |
| result[].metadata | Object | 实例元数据信息 |
| result[].metadata.name | String | 实例元数据名称 |
| result[].metadata.labels | Object | 实例标签信息 |
| result[].metadata.annotations | Object | 实例注解信息 |
| result[].metadata.creationTimestamp | String | 实例创建时间（ISO 8601格式） |
| result[].spec | Object | 实例规格配置 |
| result[].spec.instanceID | String | 实例ID |
| result[].spec.instanceName | String | 实例名称 |
| result[].spec.region | String | 实例区域 |
| result[].spec.vmClusterConfig | Object | VM集群配置 |
| result[].spec.vmClusterConfig.retentionPeriod | String | 数据保留周期 |
| result[].spec.grafanaConfig | Object | Grafana配置 |
| result[].spec.grafanaConfig.enable | Boolean | 是否启用Grafana |
| result[].spec.grafanaConfig.adminPassword | String | Grafana管理员密码 |
| result[].spec.tags | Object | 实例标签 |
| result[].status | Object | 实例状态信息 |
| result[].status.phase | String | 实例运行阶段（Running/Stopped/Creating等） |
| result[].status.ready | Boolean | 实例是否就绪 |
| result[].status.message | String | 状态描述信息 |
| result[].status.accessEndpoint | Object | 访问端点信息 |
| result[].status.accessEndpoint.privateDomain | String | 私有域名 |
| result[].status.accessEndpoint.publicDomain | String | 公有域名 |
| result[].monitorGrafanaId | String | 关联的Grafana实例ID |
| result[].monitorGrafanaName | String | 关联的Grafana实例名称 |
| status | Integer | HTTP状态码 |

## 请求示例

### 请求

```bash
curl -X GET \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/cprom/instances' \
  -H 'X-Region: bj'
```

### 响应示例

#### 有可用实例

```json
{
  "success": true,
  "result": [
    {
      "instanceId": "cprom-x35s7xaq7ezx7",
      "instanceName": "production-prometheus",
      "region": "bj",
      "metadata": {
        "name": "",
        "labels": {
          "cprom-account-id": "1093",
          "cprom-instance-template": "advance-v2",
          "cprom-instance-type": "default",
          "cprom-namespace": "cprom-80h84dy147ra7",
          "cprom-project-id": "0"
        },
        "annotations": null,
        "creationTimestamp": "2025-07-17T20:19:38+08:00"
      },
      "spec": {
        "instanceID": "cprom-x35s7xaq7ezx7",
        "instanceName": "production-prometheus",
        "region": "bj",
        "vmClusterConfig": {
          "retentionPeriod": "15d"
        },
        "grafanaConfig": {
          "enable": false,
          "adminPassword": ""
        },
        "tags": null
      },
      "status": {
        "phase": "Running",
        "ready": true,
        "message": "all components are ready",
        "accessEndpoint": {
          "privateDomain": "",
          "publicDomain": "cprom.bj.baidubce.com"
        }
      },
      "monitorGrafanaId": "",
      "monitorGrafanaName": ""
    },
    {
      "instanceId": "cprom-y46t8ybr8fay8",
      "instanceName": "development-prometheus",
      "region": "gz",
      "metadata": {
        "name": "",
        "labels": {
          "cprom-account-id": "1062",
          "cprom-instance-template": "advance-v2",
          "cprom-instance-type": "BCM",
          "cprom-namespace": "cprom-9ldhmrf7k0w37",
          "cprom-project-id": "0"
        },
        "annotations": null,
        "creationTimestamp": "2025-07-11T19:47:51+08:00"
      },
      "spec": {
        "instanceID": "cprom-y46t8ybr8fay8",
        "instanceName": "development-prometheus",
        "region": "gz",
        "vmClusterConfig": {
          "retentionPeriod": "15d"
        },
        "grafanaConfig": {
          "enable": false,
          "adminPassword": ""
        },
        "tags": null
      },
      "status": {
        "phase": "Running",
        "ready": true,
        "message": "all components are ready",
        "accessEndpoint": {
          "privateDomain": "",
          "publicDomain": "cprom.gz.baidubce.com"
        }
      },
      "monitorGrafanaId": "grafana-7v0hyglh7",
      "monitorGrafanaName": "bjtest"
    }
  ],
  "status": 200
}
```

#### 无可用实例

```json
{
  "success": true,
  "result": [],
  "status": 200
}
```

## 错误码

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 401 | Unauthorized | 身份认证失败 |
| 403 | Forbidden | 没有访问权限 |
| 500 | InternalError | 内部服务错误 |
| 503 | ServiceUnavailable | 服务暂时不可用 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "Unauthorized",
    "message": "身份认证失败"
  },
  "status": 401
}
```

## 功能说明

### 实例状态说明

| 状态 | 描述 |
|------|------|
| Creating | 实例创建中 |
| Running | 实例运行中，可正常使用 |
| Stopped | 实例已停止 |
| Deleting | 实例删除中 |
| Error | 实例异常 |

### 字段详细说明

#### metadata 字段
- **name**: 实例元数据名称，通常为空
- **labels**: 包含实例的各种标签信息
  - `cprom-account-id`: 账户ID
  - `cprom-instance-template`: 实例模板类型
  - `cprom-instance-type`: 实例类型（default/BCM等）
  - `cprom-namespace`: 实例命名空间
  - `cprom-project-id`: 项目ID
- **annotations**: 实例注解信息，可能为null
- **creationTimestamp**: 实例创建时间戳

#### spec 字段
- **vmClusterConfig**: VM集群配置
  - `retentionPeriod`: 数据保留周期，如"15d"表示15天
- **grafanaConfig**: Grafana配置
  - `enable`: 是否启用Grafana仪表板
  - `adminPassword`: Grafana管理员密码（通常为空）
- **tags**: 用户自定义标签，可能为null

#### status 字段
- **phase**: 实例当前运行阶段
- **ready**: 实例是否完全就绪可用
- **message**: 状态详细描述信息
- **accessEndpoint**: 访问端点配置
  - `privateDomain`: 私有网络访问域名
  - `publicDomain`: 公网访问域名

### 使用场景

1. **配置监控前**：查看可用的CProm实例，选择合适的实例进行监控配置
2. **实例管理**：了解当前拥有的CProm实例状态和详细配置信息
3. **区域选择**：根据AI网关实例所在区域选择合适的CProm实例
4. **容量规划**：通过查看实例的保留周期和配置信息进行容量规划
5. **Grafana集成**：查看哪些实例已关联Grafana仪表板

### 筛选建议

- **区域匹配**：建议选择与AI网关实例相同区域的CProm实例，以降低网络延迟
- **状态检查**：只选择 `status.phase` 为"Running"且 `status.ready` 为true的实例进行监控配置
- **容量考虑**：查看 `spec.vmClusterConfig.retentionPeriod` 确保有足够的数据保留周期
- **访问端点**：根据网络环境选择合适的访问端点（公网或私网）
- **Grafana需求**：如需可视化，优先选择已启用Grafana或已关联Grafana实例的CProm实例

## 注意事项

1. 该接口只返回当前用户有权限访问的CProm实例
2. 实例列表会实时反映当前状态，建议在配置监控前重新获取
3. 不同区域的CProm实例可能有不同的功能特性和限制
4. 使用 `status.accessEndpoint` 中的域名进行数据推送，请确保网络连通性
5. 建议定期检查 `status.phase` 和 `status.ready` 状态，避免使用异常实例
6. `metadata.labels` 中包含重要的实例分类信息，可用于实例筛选和管理
7. `spec.vmClusterConfig.retentionPeriod` 决定了数据保留时间，请根据需求选择合适的实例
8. 如果 `monitorGrafanaId` 不为空，表示该实例已关联Grafana仪表板，可直接用于可视化

## 相关接口

- [开启业务指标监控](./开启业务指标监控.md) - 使用CProm实例配置监控
- [检查监控状态](./检查监控状态.md) - 查看当前使用的CProm实例
- [关闭业务指标监控](./关闭业务指标监控.md) - 停止使用CProm实例
