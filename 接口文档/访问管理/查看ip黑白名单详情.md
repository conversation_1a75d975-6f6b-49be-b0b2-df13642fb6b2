# 查看IP黑白名单详情接口

## 接口基本信息

- **接口说明**：查看指定IP黑白名单规则的详细信息
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/ipRestriction/{id}`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| id | String | 是 | IP黑白名单规则ID | ip-restriction-001 |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 成功响应

```json
{
    "success": true,
    "status": 200,
    "result": {
        "id": "ip-restriction-001",
        "enabled": true,
        "name": "internal-whitelist",
        "description": "内网IP白名单规则",
        "type": "whitelist",
        "scope": "global",
        "ipAddresses": [
            "***********/24",
            "********",
            "**********/16"
        ],
        "createTime": "2023-06-15 10:30:00",
        "updateTime": "2023-06-15 15:20:30"
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | IP黑白名单详细信息 |
| result.id | String | IP黑白名单规则ID |
| result.enabled | Boolean | 是否开启 |
| result.name | String | 名称 |
| result.description | String | 备注 |
| result.type | String | 类型，whitelist表示白名单，blacklist表示黑名单 |
| result.scope | String | 生效粒度 |
| result.ipAddresses | Array[String] | IP地址/地址段列表 |
| result.createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| result.updateTime | String | 更新时间，格式：YYYY-MM-DD HH:mm:ss |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | IP黑白名单不存在 | 确认规则ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/gw-ist9vvin/ipRestriction/ip-restriction-001
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "id": "ip-restriction-001",
        "enabled": true,
        "name": "internal-whitelist",
        "description": "内网IP白名单规则",
        "type": "whitelist",
        "scope": "global",
        "ipAddresses": [
            "***********/24",
            "********",
            "**********/16"
        ],
        "createTime": "2023-06-15 10:30:00",
        "updateTime": "2023-06-15 15:20:30"
    }
}
``` 