import { extractServiceNameFromAiCluster, isAiClusterLabel } from './serviceNameExtractor';

/**
 * 需要清理的标签前缀列表
 */
export const LABEL_PREFIXES_TO_CLEAN = [
  'destination_service:',
  'destination_service_name:',
  'destination_address:',
  'ai_cluster:',
  'source_service:',
  'destination_app:',
  'source_app:',
  'source_cluster:',
  'destination_cluster:',
  'cluster_name:',
  'source_workload:',
  'destination_workload:',
  'source_namespace:',
  'destination_namespace:',
  'request_protocol:',
  'response_code:',
  'grpc_response_status:',
  'connection_security_policy:',
  'source_principal:',
  'destination_principal:',
  'source_version:',
  'destination_version:',
  'reporter:',
  'custom_dimension:',
  '状态码 ',
  '状态码类 ',
  '代码 '
];

/**
 * 清理单个标签名称
 * @param labelName 原始标签名称
 * @returns 清理后的标签名称
 */
export function cleanLabelName(labelName: string): string {
  if (!labelName || typeof labelName !== 'string') {
    console.log('❌ cleanLabelName: 无效输入', labelName);
    return labelName;
  }

  console.log('🔍 cleanLabelName: 处理标签', labelName);

  // 去除前后空格
  let cleanedName = labelName.trim();

  // 特殊处理：如果是 ai_cluster 格式的值，先进行服务名提取
  if (isAiClusterLabel(cleanedName)) {
    const extractedName = extractServiceNameFromAiCluster(cleanedName);
    if (extractedName !== cleanedName) {
      console.log(`🔧 cleanLabelName: ai_cluster 处理 "${cleanedName}" -> "${extractedName}"`);
      cleanedName = extractedName;
    }
  }

  // 检查并移除匹配的前缀
  for (const prefix of LABEL_PREFIXES_TO_CLEAN) {
    if (cleanedName.startsWith(prefix)) {
      cleanedName = cleanedName.substring(prefix.length).trim();
      console.log(`✅ cleanLabelName: 移除前缀 "${prefix}" -> "${cleanedName}"`);
      break; // 只移除第一个匹配的前缀
    }
  }

  // 如果清理后为空，返回原始名称
  if (!cleanedName) {
    console.log('⚠️ cleanLabelName: 清理后为空，返回原始名称');
    return labelName;
  }

  if (cleanedName !== labelName) {
    console.log(`🎯 cleanLabelName: 最终结果 "${labelName}" -> "${cleanedName}"`);
  } else {
    console.log(`⚪ cleanLabelName: 无需清理 "${labelName}"`);
  }

  return cleanedName;
}

/**
 * 批量清理标签名称
 * @param labelNames 标签名称数组
 * @returns 清理后的标签名称数组
 */
export function cleanLabelNames(labelNames: string[]): string[] {
  if (!Array.isArray(labelNames)) {
    return labelNames;
  }

  return labelNames.map(name => cleanLabelName(name));
}

/**
 * 清理系列数据中的标签名称
 * @param seriesData 系列数据数组
 * @returns 清理后的系列数据数组
 */
export function cleanSeriesLabels(seriesData: Array<{ name: string; [key: string]: any }>): Array<{ name: string; [key: string]: any }> {
  if (!Array.isArray(seriesData)) {
    return seriesData;
  }

  return seriesData.map(series => ({
    ...series,
    name: cleanLabelName(series.name),
    // 保留原始名称用于工具提示
    originalName: series.originalName || series.name
  }));
}

/**
 * 创建标签映射（原始名称 -> 清理后名称）
 * @param originalNames 原始标签名称数组
 * @returns 标签映射对象
 */
export function createLabelMapping(originalNames: string[]): Record<string, string> {
  const mapping: Record<string, string> = {};
  
  originalNames.forEach(originalName => {
    const cleanedName = cleanLabelName(originalName);
    mapping[cleanedName] = originalName; // 清理后的名称 -> 原始名称
  });

  return mapping;
}

/**
 * 智能标签清理（保留有意义的前缀）
 * @param labelName 原始标签名称
 * @returns 清理后的标签名称
 */
export function smartCleanLabelName(labelName: string): string {
  if (!labelName || typeof labelName !== 'string') {
    return labelName;
  }

  let cleanedName = labelName.trim();

  // 特殊处理：如果包含多个冒号，只移除第一个标签前缀
  const colonIndex = cleanedName.indexOf(':');
  if (colonIndex > 0) {
    const prefix = cleanedName.substring(0, colonIndex + 1);
    
    // 检查是否是需要清理的前缀
    if (LABEL_PREFIXES_TO_CLEAN.includes(prefix)) {
      cleanedName = cleanedName.substring(colonIndex + 1).trim();
    }
  }

  // 进一步清理：移除常见的服务后缀
  cleanedName = cleanedName
    .replace(/\.svc\.cluster\.local$/, '') // 移除Kubernetes服务后缀
    .replace(/\.default$/, '') // 移除默认命名空间后缀
    .replace(/\.istio-system$/, ''); // 移除istio-system后缀

  return cleanedName || labelName; // 如果清理后为空，返回原始名称
}

/**
 * 获取标签的显示名称和完整名称
 * @param originalName 原始标签名称
 * @returns 包含显示名称和完整名称的对象
 */
export function getLabelDisplayInfo(originalName: string): { displayName: string; fullName: string } {
  const displayName = smartCleanLabelName(originalName);
  
  return {
    displayName,
    fullName: originalName
  };
}

/**
 * 为图表数据添加清理后的标签
 * @param chartData 图表数据
 * @returns 处理后的图表数据
 */
export function processChartDataLabels(chartData: any[]): any[] {
  if (!Array.isArray(chartData)) {
    return chartData;
  }

  return chartData.map(dataPoint => {
    if (dataPoint && typeof dataPoint === 'object' && dataPoint.name) {
      const labelInfo = getLabelDisplayInfo(dataPoint.name);
      
      return {
        ...dataPoint,
        name: labelInfo.displayName,
        originalName: labelInfo.fullName
      };
    }
    
    return dataPoint;
  });
}

/**
 * 检查标签是否需要清理
 * @param labelName 标签名称
 * @returns 是否需要清理
 */
export function shouldCleanLabel(labelName: string): boolean {
  if (!labelName || typeof labelName !== 'string') {
    return false;
  }

  return LABEL_PREFIXES_TO_CLEAN.some(prefix => labelName.startsWith(prefix));
}

/**
 * 获取清理统计信息
 * @param originalNames 原始标签名称数组
 * @returns 清理统计信息
 */
export function getLabelCleaningStats(originalNames: string[]): {
  total: number;
  cleaned: number;
  unchanged: number;
  cleaningRate: number;
} {
  if (!Array.isArray(originalNames)) {
    return { total: 0, cleaned: 0, unchanged: 0, cleaningRate: 0 };
  }

  const total = originalNames.length;
  const cleaned = originalNames.filter(name => shouldCleanLabel(name)).length;
  const unchanged = total - cleaned;
  const cleaningRate = total > 0 ? (cleaned / total) * 100 : 0;

  return {
    total,
    cleaned,
    unchanged,
    cleaningRate: Math.round(cleaningRate * 100) / 100
  };
}
