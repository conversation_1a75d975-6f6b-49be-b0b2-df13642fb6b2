{"name": "console-mse", "version": "1.0.0", "description": "微服务引擎MSE", "main": "src/index.tsx", "scripts": {"dev": "cba-cli dev", "build": "cba-cli build", "i18n:extract": "cba-cli i18n:extract", "i18n:upload": "cba-cli i18n:upload"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/bce-console/console-mse"}, "keywords": ["MSE"], "author": "<EMAIL>", "license": "ISC", "devDependencies": {"@baidu/cba-cli": "1.2.6", "@baidu/cba-preset-console-react": "1.2.7-beta.9", "@baiducloud/i18n": "1.0.0-rc.29", "@types/lodash": "^4.14.202", "@types/react": "^17.0.75", "@types/react-dom": "^17.0.25", "eslint-plugin-prettier": "^5.1.3", "prettier-eslint": "^16.3.0"}, "dependencies": {"@baidu/bce-react-toolkit": "0.0.28", "@monaco-editor/react": "4.5.1", "acud": "^1.4.37", "acud-icon": "^1.0.8", "ahooks": "^3.7.8", "axios": "^1.6.5", "classnames": "^2.5.1", "echarts": "^5.4.3", "lodash": "^4.17.21", "moment": "^2.30.1", "monaco-editor": "^0.52.0", "monaco-editor-webpack-plugin": "7.1.0", "react": "^17.0.2", "react-diff-viewer": "^3.1.1", "react-dom": "^17.0.2", "react-router-dom": "^6.21.1"}, "browserslist": ["IE >= 11"]}