import React, { useState, useEffect, useCallback } from 'react';
import { Select, Button, Radio, DatePicker, Form } from 'acud';
import { OutlinedRefresh } from 'acud-icon';
import { useRequest } from 'ahooks';
import moment from 'moment';
import locale from 'acud/es/date-picker/locale/zh_CN';


import { getInstanceList } from '@/apis/instance';
import MonitorChart from './components/MonitorChart';
import styles from './index.module.less';

const { Option } = Select;

// 时间范围选项 - 根据Figma设计稿更新
const TIME_RANGE_OPTIONS = [
  { label: '近 1 小时', value: 1, unit: 'hour' },
  { label: '近 6 小时', value: 6, unit: 'hour' },
  { label: '近 1 天', value: 24, unit: 'hour' },
  { label: '近 3 天', value: 3, unit: 'day' },
  { label: '近 7 天', value: 7, unit: 'day' },
  { label: '近 14 天', value: 14, unit: 'day' },
  { label: '自定义', value: 'custom', unit: 'custom' }
];

// 监控面板配置
const MONITOR_PANELS = [
  {
    id: 'cpu_usage',
    title: '网关CPU使用率（%）',
    unit: '%',
    query: 'sum(sum(rate(container_cpu_usage_seconds_total{namespace="$namespace",container!="",container!="POD",image!=""}[5m])))'
  },
  {
    id: 'memory_usage',
    title: '网关内存使用率（%）',
    unit: '%',
    query: 'sum by (namespace) (container_memory_working_set_bytes{namespace="$namespace",container!="",container!="POD"}) / sum by (namespace) (container_spec_memory_limit_bytes{namespace="$namespace",container!="",container!="POD"})'
  },
  {
    id: 'memory_load',
    title: '内存负载（MiB）',
    unit: 'MiB',
    query: 'sum(container_memory_working_set_bytes{namespace="$namespace",container!="",container!="POD"})/1024/1024'
  },
  {
    id: 'network_in',
    title: '网络入方向IO负载（B/s）',
    unit: 'B/s',
    query: 'sum(rate(container_network_receive_bytes_total{namespace="$namespace",interface!="lo"}[5m]))'
  },
  {
    id: 'network_out',
    title: '网络出方向IO负载（B/s）',
    unit: 'B/s',
    query: 'sum(rate(container_network_transmit_bytes_total{namespace="$namespace",interface!="lo"}[5m]))'
  },
  {
    id: 'disk_read',
    title: '磁盘Read负载（B/s）',
    unit: 'B/s',
    query: 'sum(rate(container_fs_reads_bytes_total{namespace="$namespace",container!="",container!="POD"}[5m]))'
  },
  {
    id: 'disk_write',
    title: '磁盘Write负载（B/s）',
    unit: 'B/s',
    query: 'sum(rate(container_fs_writes_bytes_total{namespace="$namespace",container!="",container!="POD"}[5m]))'
  }
];

const { RangePicker } = DatePicker;

const MonitorDashboard: React.FC = () => {
  // 状态管理
  const [selectedRegion, setSelectedRegion] = useState('bj'); // 默认华北-北京
  const [selectedInstance, setSelectedInstance] = useState<string>('');
  const [selectedTimeRange, setSelectedTimeRange] = useState<number | string>(1); // 默认近1小时
  const [customTimeRange, setCustomTimeRange] = useState<[moment.Moment, moment.Moment] | null>([
    moment().subtract(1, 'hours'), // 默认1小时前
    moment() // 现在
  ]);
  const [instanceList, setInstanceList] = useState<any[]>([]);
  const [isUpdatingFromButton, setIsUpdatingFromButton] = useState(false); // 标记是否正在从按钮更新日期选择器

  console.log('MonitorDashboard 组件渲染', {
    selectedRegion,
    selectedInstance,
    selectedTimeRange
  });

  // 获取实例列表
  const { run: fetchInstanceList, loading: instanceListLoading } = useRequest(
    () => getInstanceList({
      pageNo: 1,
      pageSize: 100, // 获取足够多的实例
      orderBy: 'createTime',
      order: 'desc'
    }, selectedRegion),
    {
      manual: true,
      onSuccess: (res: any) => {
        console.log('获取实例列表成功:', res);
        // 根据接口文档，数据在 res.page.result 中
        if (res?.page?.result) {
          setInstanceList(res.page.result);
          // 如果没有选中实例且有实例列表，默认选中第一个运行中的实例
          if (!selectedInstance && res.page.result.length > 0) {
            // 优先选择运行中的实例
            const runningInstance = res.page.result.find((instance: any) => instance.ingressStatus === 'running');
            const targetInstance = runningInstance || res.page.result[0];
            setSelectedInstance(targetInstance.instanceId);
            console.log('默认选中实例:', targetInstance.instanceId, targetInstance.name);
          }
        }
      },
      onError: (error) => {
        console.error('获取实例列表失败:', error);
      }
    }
  );

  // 计算时间范围
  const getTimeRange = useCallback(() => {
    // 如果是自定义时间范围
    if (selectedTimeRange === 'custom' && customTimeRange) {
      const start = customTimeRange[0].valueOf();
      const end = customTimeRange[1].valueOf();

      console.log('使用自定义时间范围:', {
        start: new Date(start).toISOString(),
        end: new Date(end).toISOString(),
        startTimestamp: Math.floor(start / 1000),
        endTimestamp: Math.floor(end / 1000)
      });

      return { start, end };
    }

    // 预设时间范围
    const now = Date.now();
    const selectedOption = TIME_RANGE_OPTIONS.find(option => option.value === selectedTimeRange);

    console.log('计算预设时间范围:', {
      selectedTimeRange,
      selectedOption,
      now: new Date(now).toISOString()
    });

    let startTime: number;
    if (selectedOption?.unit === 'hour' && typeof selectedOption.value === 'number') {
      startTime = now - selectedOption.value * 60 * 60 * 1000;
    } else if (selectedOption?.unit === 'day' && typeof selectedOption.value === 'number') {
      startTime = now - selectedOption.value * 24 * 60 * 60 * 1000;
    } else {
      // 默认为1小时
      startTime = now - 60 * 60 * 1000;
    }

    console.log('计算结果:', {
      start: new Date(startTime).toISOString(),
      end: new Date(now).toISOString(),
      startTimestamp: Math.floor(startTime / 1000),
      endTimestamp: Math.floor(now / 1000)
    });

    return {
      start: startTime,
      end: now
    };
  }, [selectedTimeRange, customTimeRange]);

  // 刷新所有数据
  const handleRefresh = useCallback(() => {
    console.log('刷新所有数据');
    fetchInstanceList();
  }, [fetchInstanceList]);

  // 地域变化处理
  const handleRegionChange = useCallback((value: string) => {
    console.log('地域变化:', value);
    setSelectedRegion(value);
    setSelectedInstance(''); // 清空选中的实例
  }, []);

  // 实例变化处理
  const handleInstanceChange = useCallback((value: string) => {
    console.log('实例变化:', value);
    setSelectedInstance(value);
  }, []);

  // 时间范围变化处理
  const handleTimeRangeChange = useCallback((e: any) => {
    console.log('时间范围变化:', e.target.value);
    const newValue = e.target.value;
    setSelectedTimeRange(newValue);

    // 如果不是自定义，需要同步更新日期时间选择器的值
    if (newValue !== 'custom') {
      setIsUpdatingFromButton(true); // 标记正在从按钮更新

      const now = moment();
      let startTime: moment.Moment;

      const selectedOption = TIME_RANGE_OPTIONS.find(option => option.value === newValue);
      if (selectedOption?.unit === 'hour' && typeof selectedOption.value === 'number') {
        startTime = moment().subtract(selectedOption.value, 'hours');
      } else if (selectedOption?.unit === 'day' && typeof selectedOption.value === 'number') {
        startTime = moment().subtract(selectedOption.value, 'days');
      } else {
        startTime = moment().subtract(1, 'hours'); // 默认1小时
      }

      setCustomTimeRange([startTime, now]);
      console.log('同步日期时间选择器:', {
        start: startTime.format('YYYY-MM-DD HH:mm:ss'),
        end: now.format('YYYY-MM-DD HH:mm:ss')
      });

      // 延迟重置标志，确保日期选择器更新完成
      setTimeout(() => {
        setIsUpdatingFromButton(false);
      }, 100);
    }
  }, []);

  // 禁用日期函数 - 限制最多选择近14天
  const disabledDate = useCallback((current: moment.Moment) => {
    if (!current) return false;

    const now = moment();
    const fourteenDaysAgo = moment().subtract(14, 'days');

    // 禁用未来的日期
    if (current.isAfter(now, 'day')) {
      return true;
    }

    // 禁用超过14天前的日期
    if (current.isBefore(fourteenDaysAgo, 'day')) {
      return true;
    }

    return false;
  }, []);

  // 禁用时间函数 - 精确到时分秒的限制
  const disabledTime = useCallback((current: moment.Moment | null, type: 'start' | 'end') => {
    if (!current) return {};

    const now = moment();
    const fourteenDaysAgo = moment().subtract(14, 'days');

    // 如果是今天，禁用未来的时间
    if (current.isSame(now, 'day')) {
      const currentHour = now.hour();
      const currentMinute = now.minute();
      const currentSecond = now.second();

      return {
        disabledHours: () => {
          const hours: number[] = [];
          for (let i = currentHour + 1; i < 24; i++) {
            hours.push(i);
          }
          return hours;
        },
        disabledMinutes: (selectedHour: number) => {
          if (selectedHour === currentHour) {
            const minutes: number[] = [];
            for (let i = currentMinute + 1; i < 60; i++) {
              minutes.push(i);
            }
            return minutes;
          }
          return [];
        },
        disabledSeconds: (selectedHour: number, selectedMinute: number) => {
          if (selectedHour === currentHour && selectedMinute === currentMinute) {
            const seconds: number[] = [];
            for (let i = currentSecond + 1; i < 60; i++) {
              seconds.push(i);
            }
            return seconds;
          }
          return [];
        }
      };
    }

    // 如果是14天前的那一天，禁用早于14天前精确时间的时间
    if (current.isSame(fourteenDaysAgo, 'day')) {
      const limitHour = fourteenDaysAgo.hour();
      const limitMinute = fourteenDaysAgo.minute();
      const limitSecond = fourteenDaysAgo.second();

      return {
        disabledHours: () => {
          const hours: number[] = [];
          for (let i = 0; i < limitHour; i++) {
            hours.push(i);
          }
          return hours;
        },
        disabledMinutes: (selectedHour: number) => {
          if (selectedHour === limitHour) {
            const minutes: number[] = [];
            for (let i = 0; i < limitMinute; i++) {
              minutes.push(i);
            }
            return minutes;
          }
          return [];
        },
        disabledSeconds: (selectedHour: number, selectedMinute: number) => {
          if (selectedHour === limitHour && selectedMinute === limitMinute) {
            const seconds: number[] = [];
            for (let i = 0; i < limitSecond; i++) {
              seconds.push(i);
            }
            return seconds;
          }
          return [];
        }
      };
    }

    // 其他日期不限制时间
    return {};
  }, []);

  // 自定义时间范围变化处理
  const handleCustomTimeRangeChange = useCallback((dates: any) => {
    console.log('自定义时间范围变化:', dates, { isUpdatingFromButton });
    if (dates && dates.length === 2) {
      setCustomTimeRange([dates[0], dates[1]]);
      // 只有在用户手动修改日期选择器时（不是从按钮同步时），才自动切换到自定义选项
      if (!isUpdatingFromButton && selectedTimeRange !== 'custom') {
        console.log('用户手动修改日期选择器，自动切换到自定义选项');
        setSelectedTimeRange('custom');
      }
    } else {
      setCustomTimeRange(null);
    }
  }, [selectedTimeRange, isUpdatingFromButton]);

  // 初始化加载实例列表
  useEffect(() => {
    console.log('初始化加载实例列表, 地域:', selectedRegion);
    fetchInstanceList();
  }, [selectedRegion, fetchInstanceList]);

  // 生成namespace
  const namespace = selectedInstance ? `istio-system-${selectedInstance}` : '';

  return (
    <div className={styles['aigw-monitor-dashboard-page']}>
      {/* 页面头部 */}
      <div className={styles['aigw-monitor-dashboard-page-header']}>
        <div className={styles['page-header']}>
          <div className={styles['page-title']}>监控面板</div>
        </div>
      </div>

      {/* 页面内容 */}
      <div className={styles['aigw-monitor-dashboard-page-content']}>
        {/* 顶部控制区域 */}
        <div className={styles.controlPanel}>
          <Form layout="horizontal" inputMaxWidth="100%" labelWidth={80} labelAlign='left'>
            {/* 第一行：网关实例选择区 */}
            <Form.Item label="网关实例：" className={styles.formItem}>
              <div className={styles.instanceGroup}>

                <Select
                  value={selectedRegion}
                  onChange={handleRegionChange}
                  style={{ width: 150, marginRight: 12 }}
                  placeholder="选择地域"
                >
                  <Option value="bj">华北 - 北京</Option>
                </Select>

                <Select
                  value={selectedInstance}
                  onChange={handleInstanceChange}
                  style={{ width: 300, marginRight: 12 }}
                  placeholder="选择网关实例"
                  loading={instanceListLoading}
                >
                  {instanceList
                    .filter((instance: any) => instance.ingressStatus === 'running') // 只显示运行中的实例
                    .map((instance: any) => (
                      <Option key={instance.instanceId} value={instance.instanceId}>
                        {instance.name || instance.instanceId} ({instance.instanceId})
                      </Option>
                    ))}
                </Select>

                <Button
                  icon={<OutlinedRefresh />}
                  onClick={handleRefresh}
                  loading={instanceListLoading}
           
                >
                </Button>
              </div>
            </Form.Item>

            {/* 第二行：时间筛选区 */}
            <Form.Item label="时间筛选：" className={styles.formItem} tooltip="最多可查询 14 天数据"
>
              <div className={styles.timeFilterGroup}>
                <Radio.Group
                  value={selectedTimeRange}
                  onChange={handleTimeRangeChange}
                  className={styles.timeRangeButtons}
                >
                  {TIME_RANGE_OPTIONS.map(option => (
                    <Radio.Button key={option.value} value={option.value}>
                      {option.label}
                    </Radio.Button>
                  ))}
                </Radio.Group>

                {/* 日期时间范围选择器 - 一直展示 */}
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder={['开始时间', '结束时间']}
                  onChange={handleCustomTimeRangeChange}
                  value={customTimeRange}
                  className={styles.customTimePicker}
                  locale={locale}
                  disabledDate={disabledDate}
                  disabledTime={disabledTime}
                />
              </div>
            </Form.Item>
          </Form>
        </div>

        {/* 监控图表区域 */}
        <div className={styles.chartsContainer}>
          {MONITOR_PANELS.map(panel => (
            <div key={panel.id} className={styles.chartWrapper}>
              <MonitorChart
                title={panel.title}
                unit={panel.unit}
                query={panel.query.replace(/\$namespace/g, namespace)}
                instanceId={selectedInstance}
                timeRange={getTimeRange()}
                refreshTrigger={`${selectedTimeRange}-${selectedInstance}-${customTimeRange ? customTimeRange.map(d => d.valueOf()).join('-') : ''}`} // 用于触发图表刷新
                region={selectedRegion} // 传递region参数
                metricType="basic" // 明确指定为基础指标
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MonitorDashboard;
