import React, {useEffect, useMemo, useRef} from 'react';
import {useNavigate} from 'react-router-dom';
import {getQueryParams, useRegion} from '@baidu/bce-react-toolkit';

import DetailPage from '@/components/DetailPage';
import ConfigFileList from '@/pages/ConfigFileList';
import ConfigFileReleaseList from '@/pages/ConfigFileReleaseHistory';
import urls from '@/utils/urls';

const ConfigDetail = () => {
  const {configGroupName, namespace, instanceId} = getQueryParams();
  const {region} = useRegion();
  const navigate = useNavigate();
  const isFirstRun = useRef(true);

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }
    navigate(urls.registrationList);
  }, [region]);

  const panesData = [
    {
      key: urls.configFileList,
      tab: '配置文件',
      content: <ConfigFileList />
    },
    {
      key: urls.historyVersion,
      tab: '历史版本',
      content: <ConfigFileReleaseList />
    }
  ];

  const whitedPanesData = useMemo(() => {
    const newPanesData = [...panesData];
    return newPanesData;
  }, [configGroupName, namespace]);
  return (
    <>
      <DetailPage
        mode="vertical"
        headerName={configGroupName}
        backUrl={`#/config/list?instanceId=${instanceId}`}
        panesData={whitedPanesData}
      />
    </>
  );
};
export default ConfigDetail;
