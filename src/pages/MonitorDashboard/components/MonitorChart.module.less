.container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  

}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 0;
  // border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: #151B26;
}

.actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart {
  flex: 1;
  min-height: 300px;
  padding: 0 20px 20px;
  transition: opacity 0.3s ease;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 14px;
  z-index: 10;
}

.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;
  z-index: 10;
}


