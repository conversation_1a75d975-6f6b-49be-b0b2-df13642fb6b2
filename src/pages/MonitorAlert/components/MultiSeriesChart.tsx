import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button, Modal } from 'acud';
import { OutlinedArrowsAlt } from 'acud-icon';
import { useRequest } from 'ahooks';
import * as echarts from 'echarts/core';

import { queryMetrics } from '@/apis/monitor';
import { formatXAxisTime, getXAxisTickConfig, formatTooltipTime, validateAndCleanMultiSeriesData } from '@/utils/timeFormatter';
import { formatValueWithUnit, formatYAxisLabel } from '@/utils/numberFormatter';
import { getOptimizedLegendConfig, getOptimizedGridConfig, getOriginalDataMap, processSeriesDataLabels } from '@/utils/chartLegendConfig';
import { cleanLabelName } from '@/utils/labelCleaner';
import { addSimpleLegendTooltip, createCustomLegendTooltip } from '@/utils/simpleLegendTooltip';
import { setupDirectLegendTooltip, forceSetupLegendTooltip } from '@/utils/directLegendTooltip';
import { debugLegendTooltip } from '@/utils/debugLegendTooltip';
import { extractServiceNameFromAiCluster, processAiClusterLabelsInSeriesData, isAiClusterLabel } from '@/utils/serviceNameExtractor';
import '@/utils/testLabelCleaning'; // 注册测试函数到window
import '@/utils/echartsInit'; // 确保ECharts组件已注册
import styles from './AlertChart.module.less';

interface MultiSeriesChartProps {
  title: string;
  unit: string;
  description?: string; // 添加描述字段
  query: string;
  instanceId: string;
  timeRange: {
    start: number;
    end: number;
  };
  refreshTrigger?: any;
  region: string;
  metricType?: 'basic' | 'business';
  allowQueries?: boolean; // 添加是否允许查询参数
}

const MultiSeriesChart: React.FC<MultiSeriesChartProps> = ({
  title,
  unit,
  description,
  query,
  instanceId,
  timeRange,
  refreshTrigger,
  region,
  metricType = 'business',
  allowQueries = true // 默认为true，保持向后兼容
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const modalChartRef = useRef<HTMLDivElement>(null);
  const modalChartInstance = useRef<echarts.ECharts | null>(null);
  const [seriesData, setSeriesData] = useState<any[]>([]);

  // 图表模式状态管理 - 从全局状态获取
  const [chartMode, setChartMode] = useState<'line' | 'area'>(() => {
    const globalSaved = localStorage.getItem('global-chart-mode');
    const panelSaved = localStorage.getItem(`chart-mode-${title}`);
    return (panelSaved as 'line' | 'area') || (globalSaved as 'line' | 'area') || 'area';
  });

  // 监听全局模式变化
  useEffect(() => {
    const handleStorageChange = () => {
      const globalMode = localStorage.getItem('global-chart-mode') as 'line' | 'area';
      const panelMode = localStorage.getItem(`chart-mode-${title}`) as 'line' | 'area';
      const newMode = panelMode || globalMode || 'area';
      setChartMode(newMode);
    };

    window.addEventListener('storage', handleStorageChange);
    // 也监听自定义事件，用于同一页面内的状态同步
    window.addEventListener('chartModeChange', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('chartModeChange', handleStorageChange);
    };
  }, [title]);



  console.log(`MultiSeriesChart ${title} 渲染`, {
    instanceId,
    query,
    timeRange,
    refreshTrigger
  });

  // 动态step配置函数
  const getDynamicStep = (timeRange: { start: number; end: number }): number => {
    const durationMs = timeRange.end - timeRange.start;
    const durationHours = durationMs / (1000 * 60 * 60);

    // 根据时间范围动态设置step值
    if (durationHours <= 1) {
      return 60; // 1分钟
    } else if (durationHours <= 6) {
      return 300; // 5分钟
    } else if (durationHours <= 24) {
      return 900; // 15分钟
    } else if (durationHours <= 168) { // 7天
      return 3600; // 1小时
    } else {
      return 21600; // 6小时
    }
  };

  // 查询监控数据
  const { run: fetchData, loading } = useRequest(
    () => {
      if (!instanceId || !query) {
        console.log(`${title} 跳过数据查询: instanceId=${instanceId}, query=${query}`);
        return Promise.resolve(null);
      }

      const step = getDynamicStep(timeRange); // 使用动态step配置

      console.log(`${title} 开始查询数据:`, {
        instanceId,
        query,
        start: Math.floor(timeRange.start / 1000),
        end: Math.floor(timeRange.end / 1000),
        step,
        timeRangeDuration: `${((timeRange.end - timeRange.start) / (1000 * 60 * 60)).toFixed(1)}h`
      });

      return queryMetrics({
        instanceId,
        metricType,
        query,
        start: Math.floor(timeRange.start / 1000).toString(),
        end: Math.floor(timeRange.end / 1000).toString(),
        step: step.toString()
      }, region);
    },
    {
      manual: true,
      onSuccess: (res: any) => {
        console.log(`${title} 查询数据成功:`, res);

        if (res?.result?.status === 'success' && res?.result?.data?.result) {
          const resultArray = res.result.data.result;
          console.log(`${title} 查询结果数组长度:`, resultArray.length);
          console.log(`${title} 查询结果详情:`, resultArray);

          // 特别针对TCP连接数进行详细调试
          if (title === 'TCP连接数') {
            console.log('🔍 TCP连接数详细调试信息:');
            resultArray.forEach((item, index) => {
              console.log(`  系列 ${index}:`, {
                metric: item.metric,
                values: item.values?.slice(0, 3), // 只显示前3个值
                valuesLength: item.values?.length
              });
            });
          }

          // 处理多系列数据
          const colors = [
            '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2',
            '#eb2f96', '#fa8c16', '#a0d911', '#096dd9', '#36cfc9', '#f759ab',
            '#40a9ff', '#73d13d', '#ffec3d', '#ff7a45', '#9254de', '#87e8de'
          ];
          const processedSeries = resultArray.map((series: any, index: number) => {
            const data = series.values?.map(([timestamp, value]: [number, string]) => [
              timestamp * 1000,
              parseFloat(value) || 0
            ]) || [];

            // 从metric标签中获取系列名称
            let seriesName = `Series ${index + 1}`;
            if (series.metric) {
              // 优先处理包含多个标签的组合情况
              if (series.metric.ai_route && series.metric.ai_consumer) {
                seriesName = `${series.metric.ai_route} - ${series.metric.ai_consumer}`;
              } else if (series.metric.ai_route && series.metric.ai_cluster) {
                // 处理 ai_cluster 标签，提取简洁的服务名
                const cleanServiceName = extractServiceNameFromAiCluster(series.metric.ai_cluster);
                seriesName = `${series.metric.ai_route} - ${cleanServiceName}`;
              } else if (series.metric.ai_route && series.metric.ai_model) {
                seriesName = `${series.metric.ai_route} - ${series.metric.ai_model}`;
              } else if (series.metric.ai_cluster && series.metric.ai_consumer) {
                // 处理 ai_cluster 标签，提取简洁的服务名
                const cleanServiceName = extractServiceNameFromAiCluster(series.metric.ai_cluster);
                seriesName = `${cleanServiceName} - ${series.metric.ai_consumer}`;
              } else if (series.metric.ai_cluster && series.metric.ai_model) {
                // 处理 ai_cluster 标签，提取简洁的服务名
                const cleanServiceName = extractServiceNameFromAiCluster(series.metric.ai_cluster);
                seriesName = `${cleanServiceName} - ${series.metric.ai_model}`;
              } else if (series.metric.ai_route) {
                // 单独的ai_route标签，直接显示路由名称
                seriesName = series.metric.ai_route;
              } else if (series.metric.ai_cluster) {
                // 单独的ai_cluster标签，提取简洁的服务名
                seriesName = extractServiceNameFromAiCluster(series.metric.ai_cluster);
              } else if (series.metric.response_code && series.metric.destination_service_name) {
                const cleanServiceName = cleanLabelName(`destination_service_name: ${series.metric.destination_service_name}`);
                seriesName = `${cleanServiceName} (${series.metric.response_code})`;
              } else if (series.metric.destination_service_name) {
                // 对于TCP连接数等服务相关指标，直接显示服务名，不加前缀
                seriesName = series.metric.destination_service_name;
                console.log(`🔧 destination_service_name 标签处理: "${series.metric.destination_service_name}"`);
              } else if (series.metric.cluster_name) {
                // 对于cluster_name标签，尝试提取服务名
                const clusterName = series.metric.cluster_name;
                // 尝试从cluster_name中提取服务名，格式通常是: outbound|80|v1|service-name.namespace.svc.cluster.local
                const match = clusterName.match(/outbound\|[^|]*\|[^|]*\|([^.]+)\./);
                if (match && match[1]) {
                  seriesName = match[1];
                } else {
                  // 如果正则匹配失败，直接使用cluster_name
                  seriesName = clusterName;
                }
                console.log(`🔧 cluster_name 标签处理: "${clusterName}" -> "${seriesName}"`);
              } else if (series.metric.response_code) {
                // 对于状态码，直接显示数字，不加"状态码"前缀
                seriesName = series.metric.response_code;
              } else if (series.metric.response_code_class) {
                seriesName = `状态码类 ${series.metric.response_code_class}`;
              } else if (series.metric.code) {
                seriesName = `代码 ${series.metric.code}`;
              } else {
                // 如果有其他标签，使用第一个非空标签
                const keys = Object.keys(series.metric);
                if (keys.length > 0) {
                  const key = keys[0];
                  let rawSeriesName = `${key}: ${series.metric[key]}`;

                  // 如果是 ai_cluster 相关的标签，进行特殊处理
                  if (key === 'ai_cluster' || isAiClusterLabel(series.metric[key])) {
                    const cleanServiceName = extractServiceNameFromAiCluster(series.metric[key]);
                    rawSeriesName = `${key}: ${cleanServiceName}`;
                    console.log(`🔧 ai_cluster 标签处理: "${series.metric[key]}" -> "${cleanServiceName}"`);
                  }

                  // 立即清理标签名称
                  seriesName = cleanLabelName(rawSeriesName);
                  console.log(`🧹 标签清理: "${rawSeriesName}" -> "${seriesName}"`);
                }
              }
            }

            console.log(`${title} 系列 ${index}:`, {
              metric: series.metric,
              seriesName,
              dataLength: data.length
            });

            // 特别针对TCP连接数进行详细调试
            if (title === 'TCP连接数') {
              console.log(`🔍 TCP连接数系列 ${index} 详细信息:`, {
                原始metric: series.metric,
                处理后seriesName: seriesName,
                是否有destination_service_name: !!series.metric?.destination_service_name,
                destination_service_name值: series.metric?.destination_service_name
              });
            }

            // 最终清理：确保所有标签都被清理
            const finalCleanedName = cleanLabelName(seriesName);
            console.log(`🔧 最终标签清理: "${seriesName}" -> "${finalCleanedName}"`);

            return {
              name: finalCleanedName,
              originalName: seriesName, // 保留原始名称用于工具提示
              data,
              color: colors[index % colors.length]
            };
          });

          // 验证和清理数据，保持原始时间戳
          const cleanedSeriesData = validateAndCleanMultiSeriesData(processedSeries, timeRange);

          // 处理 ai_cluster 标签
          const aiClusterProcessedData = processAiClusterLabelsInSeriesData(cleanedSeriesData);

          // 清理标签名称，去除冗余前缀
          const labelCleanedData = processSeriesDataLabels(aiClusterProcessedData, true);

          setSeriesData(labelCleanedData);
          console.log(`${title} 处理后的多系列数据:`, labelCleanedData);
          console.log(`${title} 标签清理统计:`, {
            原始数量: cleanedSeriesData.length,
            ai_cluster处理后数量: aiClusterProcessedData.length,
            最终清理后数量: labelCleanedData.length,
            示例清理: labelCleanedData.slice(0, 3).map(s => ({
              原始: s.originalName,
              显示: s.name
            }))
          });
        } else if (res?.result?.status === 'error') {
          console.error(`${title} 查询失败:`, res.result.error);
          setSeriesData([]);
        } else {
          console.log(`${title} 无数据，响应结构:`, res);
          setSeriesData([]);
        }
      },
      onError: (error) => {
        console.error(`${title} 查询数据失败:`, error);
        setSeriesData([]);
      }
    }
  );

  // 格式化数值 - 使用智能精度格式化
  const formatValue = useCallback((value: number | string | any) => {
    return formatValueWithUnit(value, unit);
  }, [unit]);

  // 创建图表配置
  const createChartOption = useCallback((isModal = false) => {
    return {
      title: {
        left: 'left',
        textStyle: {
          fontSize: isModal ? 16 : 14,
          fontWeight: isModal ? 'bold' : 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          if (params && params.length > 0) {
            // 使用数据点的实际时间戳，而不是X轴刻度时间
            const actualTimestamp = Array.isArray(params[0].value) ? params[0].value[0] : params[0].axisValue;
            const time = formatTooltipTime(actualTimestamp, timeRange);
            let content = `${time}<br/>`;

            // 遍历所有系列数据
            params.forEach((param: any) => {
              const value = Array.isArray(param.value) ? param.value[1] : param.value;
              const formattedValue = formatValue(value);
              const color = param.color;
              content += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
              content += `${param.seriesName}: ${formattedValue}<br/>`;
            });

            return content;
          }
          return '';
        }
      },
      legend: getOptimizedLegendConfig({
        seriesCount: seriesData.length,
        isModal,
        maxTextLength: isModal ? 30 : 20,
        enableTooltip: true,
        originalDataMap: getOriginalDataMap(seriesData)
      }),
      grid: getOptimizedGridConfig(seriesData.length, isModal),
      xAxis: (() => {
        const tickConfig = getXAxisTickConfig(timeRange);
        return {
          type: 'time',
          boundaryGap: false,
          min: timeRange.start,
          max: timeRange.end,
          splitNumber: tickConfig.splitNumber,
          minInterval: tickConfig.minInterval,
          maxInterval: tickConfig.maxInterval,
          axisLabel: {
            formatter: function(value: number) {
              return formatXAxisTime(value, timeRange);
            },
            showMinLabel: true,
            showMaxLabel: true,
            interval: 0 // 显示所有标签
          }
        };
      })(),
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            return formatYAxisLabel(value, unit);
          }
        }
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
          height: isModal ? 40 : 30,
          bottom: isModal ? 20 : 10,
          handleStyle: {
            color: '#1890ff'
          },
          textStyle: {
            color: '#1890ff'
          }
        }
      ],
      series: seriesData.map((series) => {
        const seriesConfig: any = {
          name: series.name,
          type: 'line',
          data: series.data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            color: series.color,
            width: 2
          },
          emphasis: {
            focus: 'series'
          }
        };

        // 根据图表模式添加面积图样式
        if (chartMode === 'area') {
          const color = series.color;
          const rgb = color.replace('#', '');
          const r = parseInt(rgb.substr(0, 2), 16);
          const g = parseInt(rgb.substr(2, 2), 16);
          const b = parseInt(rgb.substr(4, 2), 16);

          seriesConfig.areaStyle = {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: `rgba(${r}, ${g}, ${b}, 0.3)` },
                { offset: 1, color: `rgba(${r}, ${g}, ${b}, 0.1)` }
              ]
            }
          };
        }

        return seriesConfig;
      })
    };
  }, [seriesData, formatValue, timeRange, unit, chartMode]);

  // 初始化图表
  const initChart = useCallback(() => {
    if (!chartRef.current) return;

    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    chartInstance.current = echarts.init(chartRef.current);
    const option = createChartOption(false);
    chartInstance.current.setOption(option);

    // 设置图例工具提示（调试版本）
    const originalDataMap = getOriginalDataMap(seriesData, {}, true); // 启用标签清理
    console.log('Setting up tooltips for main chart with data:', originalDataMap);
    console.log('Series data for tooltip:', seriesData.map(s => ({
      display: s.name,
      original: s.originalName
    })));

    // 使用调试版本进行详细的问题排查
    debugLegendTooltip(chartInstance.current, originalDataMap, 20);

    console.log(`${title} 多系列图表初始化完成`);
  }, [createChartOption, title]);

  // 更新图表数据
  const updateChart = useCallback(() => {
    if (!chartInstance.current) return;

    const option = createChartOption(false);
    chartInstance.current.setOption(option, true); // 第二个参数为true表示不合并，完全替换
    console.log(`${title} 多系列图表数据更新完成`);
  }, [createChartOption, title]);

  // 打开弹窗
  const openModal = useCallback(() => {
    console.log(`${title} 打开弹窗`);
    setIsModalVisible(true);
  }, [title]);

  // 关闭弹窗
  const closeModal = useCallback(() => {
    console.log(`${title} 关闭弹窗`);
    if (modalChartInstance.current) {
      modalChartInstance.current.dispose();
      modalChartInstance.current = null;
    }
    setIsModalVisible(false);
  }, [title]);

  // 初始化弹窗中的图表
  const initModalChart = useCallback(() => {
    console.log(`${title} 初始化弹窗图表`, {
      modalChartRef: !!modalChartRef.current,
      seriesDataLength: seriesData.length
    });

    if (!modalChartRef.current || seriesData.length === 0) {
      console.warn(`${title} 弹窗图表容器不存在或无数据`);
      return;
    }

    if (modalChartInstance.current) {
      modalChartInstance.current.dispose();
    }

    modalChartInstance.current = echarts.init(modalChartRef.current);
    const option = createChartOption(true);
    modalChartInstance.current.setOption(option);

    // 设置弹窗图例工具提示（调试版本）
    const originalDataMap = getOriginalDataMap(seriesData, {}, true); // 启用标签清理
    console.log('Setting up tooltips for modal chart with data:', originalDataMap);
    console.log('Modal series data for tooltip:', seriesData.map(s => ({
      display: s.name,
      original: s.originalName
    })));

    // 使用调试版本进行详细的问题排查
    debugLegendTooltip(modalChartInstance.current, originalDataMap, 30);

    console.log(`${title} 弹窗图表初始化完成`);
  }, [createChartOption, title, seriesData.length]);

  // 监听数据变化，触发查询
  useEffect(() => {
    if (allowQueries && instanceId && query && timeRange.start && timeRange.end) {
      console.log(`${title} 触发数据查询`);
      fetchData();
    } else {
      console.log(`${title} 跳过数据查询，缺少必要参数或查询被禁止:`, {
        allowQueries,
        instanceId: !!instanceId,
        query: !!query,
        timeRange: !!(timeRange.start && timeRange.end)
      });
      setSeriesData([]);
    }
  }, [allowQueries, instanceId, query, timeRange.start, timeRange.end, refreshTrigger, fetchData, title]);

  // 初始化图表
  useEffect(() => {
    initChart();

    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [initChart]);

  // 数据变化时更新图表
  useEffect(() => {
    if (chartInstance.current && seriesData.length > 0) {
      updateChart();
    }
  }, [seriesData, updateChart]);

  // 当弹窗打开时初始化图表
  useEffect(() => {
    if (isModalVisible) {
      console.log(`${title} 弹窗打开，准备初始化图表`);

      const timer = setTimeout(() => {
        initModalChart();

        setTimeout(() => {
          if (modalChartRef.current && !modalChartInstance.current) {
            console.log(`${title} 重试初始化弹窗图表`);
            initModalChart();
          }
        }, 200);
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [isModalVisible, initModalChart]);

  // 弹窗中图表的resize处理
  useEffect(() => {
    if (isModalVisible && modalChartInstance.current) {
      const handleModalResize = () => {
        if (modalChartInstance.current) {
          modalChartInstance.current.resize();
        }
      };

      window.addEventListener('resize', handleModalResize);
      return () => {
        window.removeEventListener('resize', handleModalResize);
      };
    }
  }, [isModalVisible]);

  return (
    <>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <div className={styles.title}>{title}</div>
            {description && (
              <div className={styles.description} title={description}>
                {description}
              </div>
            )}
          </div>
          <div className={styles.actions}>
            <Button
              type="text"
              icon={<OutlinedArrowsAlt />}
              onClick={openModal}
            />
          </div>
        </div>
        <div
          ref={chartRef}
          className={styles.chart}
          style={{
            height: '300px',
            opacity: loading ? 0.6 : 1
          }}
        />
        {loading && (
          <div className={styles.loading}>
            数据加载中...
          </div>
        )}
        {!loading && seriesData.length === 0 && instanceId && (
          <div className={styles.noData}>
            暂无数据
          </div>
        )}
        {!instanceId && (
          <div className={styles.noData}>
            请选择网关实例
          </div>
        )}
      </div>

      {/* 弹窗显示放大的图表 */}
      <Modal
        title={title}
        visible={isModalVisible}
        onCancel={closeModal}
        footer={null}
        destroyOnClose={true}
        width='1000px'
        style={{ height: '70vh', overflow: 'hidden' }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%'
          }}
        >
          <div
            ref={modalChartRef}
            style={{
              height: '80%',
              width: '100%'
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default MultiSeriesChart;
