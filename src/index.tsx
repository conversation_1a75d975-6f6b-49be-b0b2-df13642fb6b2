import React from 'react';
import {render} from 'react-dom';
import {
  AppProvider,
  FrameworkProvider,
  i18nInstance,
  I18nProvider,
  I18nUtil,
  toolkitConfig
} from '@baidu/bce-react-toolkit';

import App from '@/App';
import {SmartProvider} from '@/contexts/SmartContext';

import 'acud/dist/acud.min.css';
import '@baidu/bce-react-toolkit/es/styles/_overwrite_acud.css';

import './public-path';
import '@/styles/global.less';
import '@/styles/dbsc.less';
import '@/utils/echartsInit'; // 初始化ECharts组件

toolkitConfig.init({
  enableI18n: APP_ENABLE_I18N,
  enableIndependentI18n: APP_ENABLE_INDEPENDENT_I18N,
  isEmbed: APP_IS_EMBED_MODE,
  publicPath: window.appPublicPath,
  supportedLanguageTypes: APP_ALLOWED_LANGUAGE_TYPES || [],
  appTitle: APP_TITLE
});

export async function bootstrap(initData: any) {
  const i18nUtil = new I18nUtil();
  await i18nUtil.init();

  render(
    <I18nProvider
      i18n={i18nInstance}
      defaultNS={'translation'}
      i18nUtil={i18nUtil}
    >
      <FrameworkProvider frameworkData={initData}>
        <AppProvider>
          <SmartProvider>
            <App />
          </SmartProvider>
        </AppProvider>
      </FrameworkProvider>
    </I18nProvider>,
    document.querySelector('#main')
  );
}
