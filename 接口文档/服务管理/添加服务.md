# 添加服务接口

## 接口基本信息

- **接口说明**：向网关实例添加服务
- **接口地址**：`/api/aigw/v1/aigateway/cluster/{instanceId}/serviceList`
- **请求方式**：POST
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 请求体

```json
{
    "clusterId": "cce-qhjz40ds",
    "serviceSource": "CCE",
    "namespace": "default",
    "serviceList": ["mnist-inference", "text-embedding", "image-recognition"]
}
```

### 请求体字段说明

| 字段名 | 类型 | 是否必填 | 描述 | 示例值 | 限制 |
| --- | --- | --- | --- | --- | --- |
| clusterId | String | 是 | 集群ID | cce-qhjz40ds | 必须是有效的集群ID |
| serviceSource | String | 是 | 服务来源 | CCE | 目前仅支持"CCE"(容器引擎) |
| namespace | String | 是 | 服务所在的命名空间 | default | 必须是集群中存在的命名空间 |
| serviceList | Array[String] | 是 | 要添加的服务列表 | ["mnist-inference"] | 必须是命名空间中存在的服务，可以多选 |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": {
        "addedCount": 2
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 结果对象 |
| result.addedCount | Integer | 成功添加的服务数量 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有添加服务的权限 |
| 404 | 实例或集群不存在 | 确认实例ID和集群ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
POST /api/aigw/v1/aigateway/cluster/gw-ist9vvin/serviceList
X-Region: gz

{
    "clusterId": "cce-qhjz40ds",
    "serviceSource": "CCE",
    "namespace": "ai-services",
    "serviceList": ["mnist-inference", "text-embedding", "image-recognition"]
}
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "addedCount": 3
    }
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 集群ID必须符合规范格式，通常以特定前缀（如`cce-`）开头
3. 服务来源目前仅支持"CCE"（容器引擎）
4. 命名空间必须是集群中已存在的命名空间
5. 服务列表必须是命名空间中已存在的服务