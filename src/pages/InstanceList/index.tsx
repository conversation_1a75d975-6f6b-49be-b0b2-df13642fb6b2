import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFrameworkContext, useRegion,Clipboard } from '@baidu/bce-react-toolkit';
import { Button, Link, Modal, Table, Tag, toast, Search, Tooltip, Pagination, Input, Alert, Popover } from 'acud';
import { OutlinedDown, OutlinedPlusNew, OutlinedUp, OutlinedEditingSquare } from 'acud-icon';
import { useRequest } from 'ahooks';

import { deleteRegistrationInstance, getInstanceList, updateInstance } from '@/apis/instance';
import { getInstanceServices } from '@/apis/service';
import CopyDataWhenHover from '@/components/CopyDataWhenHover';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';
import RefreshButton from '@/components/RefreshButton';
import { RegistractionStatusDict } from '@/utils/enums';
import urls from '@/utils/urls';
import { formatTime } from '@/utils/utils';
import ProductIntroIcon from '@/assets/svg/ProductIntroIcon';

import styles from './index.module.less';

// 统一设置编辑图标大小和样式
OutlinedEditingSquare.defaultProps = {
  width: 16,
  height: 16,
  className: styles['edit-icon']
};

// 定义实例列表项接口
interface InstanceItem {
  instanceId: string;
  name: string;
  ingressStatus: string;
  replicas: number;
  vpcCidr: string;
  vpcId: string;
  subnetId: string;
  gatewayType: string;
  internalIP: string;
  publicAccessible: boolean;
  externalIP: string;
  description: string;
  createTime: string;
  deleteProtection: boolean;
  region: string;
  namespace: string;
}

// 处理后的实例数据结构
interface ProcessedInstanceItem {
  instanceId: string;
  name: string;
  status: number;
  region: string;
  internalIP: string;
  externalIP: string;
  description: string;
  createTime: string;
  deleteProtection: boolean;
  namespace: string;
  rawData: InstanceItem;
}

// 查询服务列表接口返回结构
interface ServiceListResponse {
  success: boolean;
  status: number;
  page: {
    orderBy: string;
    order: string;
    pageNo: number;
    pageSize: number;
    totalCount: number;
    result: any[];
  };
}

// 接口返回数据结构
interface ApiResponse {
  success: boolean;
  status: number;
  page?: {
    orderBy: string;
    order: string;
    pageNo: number;
    pageSize: number;
    totalCount: number;
    result: InstanceItem[];
  };
}

// 导入CreateInstance组件
import CreateInstance from '@/pages/CreateInstance';

const InstanceList: React.FC = () => {
  const [dataSource, setDataSource] = useState<ProcessedInstanceItem[]>([]);
  const { frameworkData } = useFrameworkContext();
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const { region } = useRegion();
  const isFirstRun = useRef(true);
  const navigate = useNavigate();
  const [showIntroduction, setShowIntroduction] = useState(() => {
    const savedPreference = localStorage.getItem('aigwIntroductionShow'); // 获取本地存储的值
    return savedPreference === null ? true : savedPreference === 'true';
  });
  const [keyword, setKeyword] = useState('');
  const [orderBy, setOrderBy] = useState('createTime');
  const [order, setOrder] = useState('desc');
  const [inputValue, setInputValue] = useState('');
  const [hoverRowId, setHoverRowId] = useState<string | null>(null);

  // 添加显示创建表单的状态
  const [showCreateForm, setShowCreateForm] = useState(false);

  // 添加编辑实例名称和描述相关状态
  const [editingField, setEditingField] = useState<{ id: string, field: 'name' | 'description' } | null>(null);
  const [nameInput, setNameInput] = useState('');
  const [nameError, setNameError] = useState('');
  const [descInput, setDescInput] = useState('');
  const [descError, setDescError] = useState('');
  const [submitLoading, setSubmitLoading] = useState(false);

  // 当组件卸载时清理状态
  useEffect(() => {
    return () => {
      setEditingField(null);
      setNameInput('');
      setNameError('');
      setDescInput('');
      setDescError('');
    };
  }, []);

  const onClickDeleteBtn = useCallback((row: ProcessedInstanceItem) => {
    // 如果开启了删除保护，不执行删除操作
    if (row.deleteProtection) {
      return;
    }

    // 创建自定义对话框内容
    const modalContent = (
      //用于抵消Modal组件内置的padding，让警告框和输入框可以完全扩展到边缘
      <div style={{ marginLeft: '-32px' }}>
        <Alert
          message={
            <div>
              <span style={{ fontWeight: 'bold' }}>注意：</span>该操作将删除网关实例并释放相关资源，且不可恢复，请谨慎执行。如您确认此操作，请在下方文本框中输入网关实例的名称：
              <span style={{ color: '#F5222D' }}>{row.name}</span>
            </div>
          }
          type="warning"
          // showIcon
          style={{ marginBottom: '16px', width: '100%' }}
        />


        <Input
          id="confirm-instance-name"
          placeholder={`请输入网关实例名称进行确认`}
          style={{ width: '100%' }}
          onChange={(e) => {
            const value = e.target.value;
            const isMatch = value === row.name;

            // 获取DOM元素并设置状态
            const okBtn = document.querySelector('.acud-modal-confirm .acud-btn-primary');
            const errorText = document.getElementById('name-input-error');

            if (okBtn) {
              if (isMatch) {
                okBtn.removeAttribute('disabled');
                okBtn.className = okBtn.className.replace('acud-btn-disabled', '');
              } else {
                okBtn.setAttribute('disabled', 'disabled');
                if (!okBtn.className.includes('acud-btn-disabled')) {
                  okBtn.className += ' acud-btn-disabled';
                }
              }
            }

            if (errorText) {
              if (value && !isMatch) {
                errorText.style.display = 'block';
              } else {
                errorText.style.display = 'none';
              }
            }
          }}
        />

        <div id="name-input-error" style={{ color: '#F5222D', marginTop: '8px', display: 'none' }}>
          与网关实例名称不一致
        </div>
      </div>

    );

    // 显示确认对话框
    Modal.confirm({
      title: '确认删除当前网关实例吗？',
      content: modalContent,
      width: 520,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 先调用查询实例服务列表接口，只检查是否有服务，所以只需要查询第1页，1条记录即可
          console.log('查询实例中服务列表，ID:', row.instanceId);
          const servicesResponse = await getInstanceServices(
            row.instanceId,
            { pageNo: 1, pageSize: 1 },
            region
          );

          // 类型转换为确保TS类型检查通过
          const serviceListResponse = servicesResponse as unknown as ServiceListResponse;

          // 判断实例中是否存在服务
          if (serviceListResponse?.success && serviceListResponse?.page?.totalCount > 0) {
            // 如果存在服务，提示错误
            toast.error({
              message: '当前实例中仍存在服务，暂无法删除，您需要先移除所有服务后才能执行删除操作',
              duration: 5
            });
            return Promise.reject('实例中仍存在服务');
          }

          // 如果不存在服务，调用删除接口
          console.log('删除实例，ID:', row.instanceId, '地域:', region);
          await deleteRegistrationInstance(row.instanceId, region);
          toast.success({
            message: `网关实例 ${row.name} 删除成功`,
            duration: 5
          });
          onClickRefreshBtn();
        } catch (err) {
          console.error('删除实例失败:', err);
          toast.error({
            message: `网关实例 ${row.name} 删除失败，请重新操作`,
            duration: 5
          });
          return Promise.reject(err);
        }
      }
    });

    // 初始化弹窗后增加样式表，使用CSS选择器禁用确认按钮
    setTimeout(() => {
      // 创建一个样式标签来确保禁用确认按钮
      const styleTag = document.createElement('style');
      styleTag.id = 'disable-modal-ok-btn';
      styleTag.textContent = `
        .acud-modal-confirm .acud-btn-primary[disabled],
        .acud-modal-footer .acud-btn-primary[disabled] {
          cursor: not-allowed;
          pointer-events: none;
        }
      `;
      document.head.appendChild(styleTag);

      // 添加事件监听器，监听确认输入框的变化
      const inputElement = document.getElementById('confirm-instance-name');
      if (inputElement) {
        const updateButtonState = (e) => {
          const value = e.target.value;
          const isMatch = value === row.name;

          // 获取所有可能的确认按钮
          const okButtons = document.querySelectorAll('.acud-modal-confirm .acud-btn-primary, .acud-modal-footer .acud-btn-primary');
          okButtons.forEach(btn => {
            if (isMatch) {
              btn.removeAttribute('disabled');
              btn.classList.remove('acud-btn-disabled');
            } else {
              btn.setAttribute('disabled', 'disabled');
            }
          });

          // 更新错误提示
          const errorText = document.getElementById('name-input-error');
          if (errorText) {
            if (value && !isMatch) {
              errorText.style.display = 'block';
            } else {
              errorText.style.display = 'none';
            }
          }
        };

        // 为输入框添加事件监听器
        inputElement.addEventListener('input', updateButtonState);

        // 初始时禁用按钮
        const initEvent = { target: inputElement };
        updateButtonState(initEvent);
      }
    }, 100); // 增加延迟确保DOM已完全渲染
  }, [region]);

  // 处理API返回的数据，将原始字段映射到UI使用的字段
  const processInstanceData = useCallback((items: InstanceItem[]): ProcessedInstanceItem[] => {
    return items.map(item => ({
      instanceId: item.instanceId,
      name: item.name || '-',
      status: mapStatusToCode(item.ingressStatus),
      region: item.region,
      internalIP: item.internalIP || '-',
      externalIP: item.externalIP || '-',
      description: item.description || '',
      createTime: item.createTime,
      deleteProtection: item.deleteProtection,
      namespace: item.namespace,
      rawData: item
    }));
  }, []);

  // 将状态字符串映射为数字状态码
  const mapStatusToCode = useCallback((status: string): number => {
    const statusMap: { [key: string]: number } = {
      'running': 2, // 运行中
      'creating': 7, // 待就绪 - 这里从1变更为7
      'initializing': 0, // 初始化
      'adjusting': 3, // 调整中
      'releasing': 4, // 释放中
      'error': 5, // 运行异常
      'failed': 6  // 创建失败
    };
    return statusMap[status] || 0;
  }, []);

  // 添加表格行级鼠标悬停事件处理
  const onRowMouseEnter = (record: ProcessedInstanceItem) => {
    setHoverRowId(record.instanceId);
  };

  const onRowMouseLeave = () => {
    setHoverRowId(null);
  };

  // 实例名称编辑内容
  const nameEditContent = (
    <div 
      className={styles['edit-popover']}
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
    >
      <Input
        placeholder="请输入实例名称"
        value={nameInput}
        onChange={(e) => {
          setNameInput(e.target.value);
          validateName(e.target.value);
        }}
        style={{ width: '240px' }}
        maxLength={64}
        autoFocus

      />
      {nameError && <div style={{ color: '#F5222D', marginTop: '8px', fontSize: '12px' }}>{nameError}</div>}
      <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '8px' }}>
        必须以字母或者中文开头，支持大小写字母、中文、数字以及-/.特殊字符
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-start', marginTop: '16px' }}>
        <Button
          style={{ marginRight: '8px' }}
          onClick={(e) => {
            e.stopPropagation();
            setEditingField(null);
            setNameInput('');
            setNameError('');
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          loading={submitLoading}
          disabled={!!nameError || !nameInput.trim()}
          onClick={(e) => {
            e.stopPropagation();
            if (validateName(nameInput) && editingField) {
              updateInstanceInfo(editingField.id, 'name', nameInput);
            }
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );

  // 描述编辑内容
  const descEditContent = (
    <div 
      className={styles['edit-popover']}
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
    >
      <Input.TextArea
        placeholder="请输入描述"
        value={descInput}
        onChange={(e) => {
          const value = e.target.value;
          // 限制输入长度不超过64个字符
          if (value.length <= 64) {
            setDescInput(value);
            validateDesc(value);
          }
        }}
        style={{ width: '240px' }}
        autoSize={{ minRows: 2, maxRows: 6 }}
      />
      {descError && <div style={{ color: '#F5222D', marginTop: '8px', fontSize: '12px' }}>{descError}</div>}
      <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '8px' }}>
        长度为0-64个字符
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-start', marginTop: '16px' }}>
        <Button
          style={{ marginRight: '8px' }}
          onClick={(e) => {
            e.stopPropagation();
            setEditingField(null);
            setDescInput('');
            setDescError('');
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          loading={submitLoading}
          disabled={!!descError}
          onClick={(e) => {
            e.stopPropagation();
            if (validateDesc(descInput) && editingField) {
              updateInstanceInfo(editingField.id, 'description', descInput);
            }
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );

  const columns = [
    {
      title: '实例名称/ID',
      dataIndex: 'name',
      width: 220,
      fixed: 'left' as const,
      key: 'name',
      render(_, record: ProcessedInstanceItem) {
        const { instanceId, name, status } = record;
        const isRunning = status === 2; // 运行中状态
        const isRowHovered = hoverRowId === record.instanceId;
        const isEditing = editingField?.id === instanceId && editingField?.field === 'name';

        if (isRunning) {
          return (
            <>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Tooltip title={name || '-'}>
                  <Link
                    href={`#${urls.registrationBaseInfo}?instanceId=${instanceId}`}
                  >
                    <span
                      style={{
                        display: 'inline-block',
                        maxWidth: 150,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        verticalAlign: 'middle',
                      }}
                    >
                      {name}
                    </span>
                  </Link>
                </Tooltip>
                <Popover
                  content={nameEditContent}
                  trigger="manual"
                  visible={isEditing}
                  placement="rightTop"
                  destroyTooltipOnHide
                  getPopupContainer={() => document.body}
                >
                  <div style={{ width: 24, height: 24, display: 'flex', alignItems: 'center', justifyContent: 'center', opacity: isRowHovered ? 1 : 0 }}>
                    <OutlinedEditingSquare
                      className={styles['edit-icon']}
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingField({ id: instanceId, field: 'name' });
                        setNameInput(name);
                        setNameError('');
                      }}
                    />
                  </div>
                </Popover>
              </div>
              <CopyDataWhenHover copyValue={instanceId} forceShow={isRowHovered} />
            </>
          );
        } else {
          return (
            <>
              <div>
                <Tooltip title="当前状态下不允许查看网关详情">
                  <span
                    style={{
                      color: '#BFBFBF',
                      cursor: 'not-allowed',
                      display: 'inline-block',
                      maxWidth: 180,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      verticalAlign: 'middle',
                    }}
                  >
                    {name}
                  </span>
                </Tooltip>
              </div>
              <CopyDataWhenHover copyValue={instanceId} forceShow={isRowHovered} />
            </>
          );
        }
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (value: number) => {
        const data = RegistractionStatusDict.get(value);
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{ paddingLeft: 0 }}
            icon={<span className={data?.iconClass} />}
          >
            {data?.text || '-'}
          </Tag>
        );
      }
    },
    {
      title: '地域',
      dataIndex: 'region',
      key: 'region',
      width: 120,
      render: (value: string) => {
        return frameworkData?.constants?.REGION[value] || value || '-';
      }
    },
    {
      title: '接入地址',
      dataIndex: 'accessAddress',
      key: 'accessAddress',
      width: 180,
      render(_, record: ProcessedInstanceItem) {
        const internalIP = record.internalIP || '-';
        const externalIP = record.externalIP || '-';
        const isRowHovered = hoverRowId === record.instanceId;
        
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: '4px' }}>内网：</span>
              <CopyDataWhenHover copyValue={internalIP} forceShow={isRowHovered} />
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: '4px' }}>公网：</span>
              <CopyDataWhenHover copyValue={externalIP} forceShow={isRowHovered} />
            </div>
          </div>
        );
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 180,
      render(value: string, record: ProcessedInstanceItem) {
        const isRowHovered = hoverRowId === record.instanceId;
        const isRunning = record.status === 2;
        const isEditing = editingField?.id === record.instanceId && editingField?.field === 'description';

        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title={value || '-'}>
              <span
                style={{
                  display: 'inline-block',
                  maxWidth: 130,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  verticalAlign: 'middle',
                }}
              >
                {value || '-'}
              </span>
            </Tooltip>
            {isRunning && (
              <Popover
                content={descEditContent}
                trigger="manual"
                visible={isEditing}
                placement="rightTop"
                destroyTooltipOnHide
                getPopupContainer={() => document.body}
              >
                <div style={{ width: 24, height: 24, display: 'flex', alignItems: 'center', justifyContent: 'center', opacity: isRowHovered ? 1 : 0 }}>
                  <OutlinedEditingSquare
                    className={styles['edit-icon']}
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingField({ id: record.instanceId, field: 'description' });
                      setDescInput(value || '');
                      setDescError('');
                    }}
                  />
                </div>
              </Popover>
            )}
          </div>
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      // sorter: (a, b) => a.createTime - b.createTime,
      render: (value: string) => value || '-'
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      className: 'action-cell',
      width: 120,
      fixed: 'right' as const,
      render(_, row: ProcessedInstanceItem) {
        if (row.deleteProtection) {
          return (
            <Tooltip 
              title={
                <div>
                  当前实例已开启删除保护功能，请先关闭删除保护后再删除。
                  <a 
                    style={{
                      color: '#2468F2',
                      marginLeft: '5px',
                      cursor: 'pointer'
                    }}
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡
                      onCloseDeleteProtection(row);
                    }}
                  >
                    关闭删除保护
                  </a>
                </div>
              }
            >
              <Button
                type="actiontext"
                disabled={true}
              >
                删除
              </Button>
            </Tooltip>
          );
        }

        return (
          <Button
            type="actiontext"
            onClick={() => {
              onClickDeleteBtn(row);
            }}
          >
            删除
          </Button>
        );
      }
    }
  ];

  const getInstanceListBack = useCallback(
    async (pageNo: number, pageSize: number, searchKeyword: string = keyword) => {
      console.log('查询实例列表，参数:', {
        pageNo,
        pageSize,
        orderBy,
        order,
        keyword: searchKeyword
      }, '地域:', region);

      const params: any = {
        pageNo,
        pageSize,
        orderBy,
        order
      };

      if (searchKeyword) {
        params.keyword = searchKeyword;
      }

      return getInstanceList(params, region);
    },
    [pageNo, pageSize, orderBy, order, keyword, region]
  );

  const {
    run,
    loading,
    refresh: refreshInstanceList
  } = useRequest(getInstanceListBack, {
    manual: true,
    onSuccess: (res: ApiResponse) => {
      console.log('实例列表返回数据:', res);

      if (res?.success && res?.status === 200 && res?.page) {
        const resultData = res.page.result || [];
        const processedData = processInstanceData(resultData);

        setDataSource(processedData);
        setTotal(res.page.totalCount || 0);
        setOrderBy(res.page.orderBy || 'createTime');
        setOrder(res.page.order || 'desc');
      } else {
        console.error('接口返回失败或格式不符合预期:', res);
        setDataSource([]);
        setTotal(0);
        toast.error({
          message: `获取实例列表失败，请重试`,
          duration: 3
        });
      }
    },
    onError: (err) => {
      console.error('获取实例列表接口调用失败:', err);
      setDataSource([]);
      setTotal(0);
      toast.error({
        message: `获取实例列表失败，请重试`,
        duration: 3
      });
    }
  });

  useEffect(() => {
    run(pageNo, pageSize);
  }, []);

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    console.log('点击刷新按钮，使用当前搜索值:', inputValue);
    setKeyword(inputValue);
    // 使用最新的输入框值进行查询
    run(pageNo, pageSize, inputValue);
  }, [refreshInstanceList, inputValue, run, pageNo, pageSize]);

  // 搜索框内容变化时更新inputValue
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  }, []);

  // 搜索框失焦时执行搜索
  const handleSearchBlur = useCallback(() => {
    console.log('搜索框失焦，搜索关键词:', inputValue);
    if (inputValue !== keyword) {
      setKeyword(inputValue);
      setPageNo(1);
      run(1, pageSize, inputValue);
    }
  }, [inputValue, keyword, run, pageSize]);

  /**
   * 执行搜索
   */
  const doSearch = useCallback((value: string) => {
    console.log('执行搜索，搜索关键词:', value);
    setInputValue(value); // 同步更新输入框值
    setKeyword(value);
    setPageNo(1);
    // 直接传递当前搜索值，避免状态更新延迟
    run(1, pageSize, value);
  }, [run, pageSize]);

  // 组件初始化时同步keyword和inputValue
  useEffect(() => {
    setInputValue(keyword);
  }, []);

  const onToCreate = () => {
    // 更改为切换状态而非导航
    setShowCreateForm(true);
  };

  // 添加从创建表单返回列表的方法
  const onBackToList = () => {
    setShowCreateForm(false);
    // 刷新列表数据
    onClickRefreshBtn();
  };

  const toggleIntroduction = () => {
    const newState = !showIntroduction;
    setShowIntroduction(newState);
    localStorage.setItem('aigwIntroductionShow', String(newState));
  };

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }
    run(pageNo, pageSize);
  }, [region]);

  // 添加关闭删除保护的处理函数
  const onCloseDeleteProtection = async (row: ProcessedInstanceItem) => {
    try {
      console.log('关闭删除保护，实例ID:', row.instanceId);
      setSubmitLoading(true);
      const response = await updateInstance(
        row.instanceId,
        { deleteProtection: false },
        region
      );
      console.log('关闭删除保护返回结果:', response);
      
      if (response?.success) {
        toast.success({
          message: `删除保护已关闭`,
          duration: 5
        });
        onClickRefreshBtn();
      } else {
        toast.error({
          message: (response as any)?.message || '关闭删除保护失败，请重试',
          duration: 5
        });
      }
    } catch (error) {
      console.error('关闭删除保护出错:', error);
      toast.error({
        message: '系统错误，请重试',
        duration: 5
      });
    } finally {
      setSubmitLoading(false);
    }
  };

  // 验证实例名称
  const validateName = (value: string) => {
    if (!value.trim()) {
      setNameError('实例名称不能为空');
      return false;
    }

    if (value.length < 1 || value.length > 64) {
      setNameError('实例名称长度为1-64个字符');
      return false;
    }

    // 检查是否以字母或中文开头
    if (!/^[a-zA-Z\u4e00-\u9fa5]/.test(value)) {
      setNameError('实例名称必须以字母或中文开头');
      return false;
    }

    // 检查是否只包含合法字符(支持大小写字母、数字、中文以及-_/.特殊字符)
    if (!/^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\-_\/\.]*$/.test(value)) {
      setNameError('实例名称只能包含字母、数字、中文以及-_/.特殊字符');
      return false;
    }

    setNameError('');
    return true;
  };

  // 验证描述
  const validateDesc = (value: string) => {
    if (value && value.length > 64) {
      setDescError('描述长度不能超过64个字符');
      return false;
    }
    setDescError('');
    return true;
  };

  // 更新实例信息
  const updateInstanceInfo = async (instanceId: string, field: 'name' | 'description', value: string) => {
    console.log(`更新实例${field}:`, value);
    setSubmitLoading(true);

    try {
      if (!instanceId) {
        throw new Error('实例ID不存在');
      }

      // 创建更新数据对象，只更新指定的字段
      const updateData: { name?: string; description?: string } = {};
      updateData[field] = value;

      console.log('更新数据:', updateData);
      
      const response = await updateInstance(instanceId, updateData, region);
      console.log('更新实例返回结果:', response);

      if (response?.success) {
        toast.success({
          message: field === 'name' ? '实例名称编辑成功' : '实例描述编辑成功',
          duration: 5
        });
        
        // 重新获取实例列表
        onClickRefreshBtn();
      } else {
        toast.error({
          message: (response as any)?.message || '更新失败，请重试',
          duration: 5
        });
      }
    } catch (error) {
      console.error('更新实例出错:', error);
      toast.error({
        message: '系统错误，请重试',
        duration: 5
      });
    } finally {
      setSubmitLoading(false);
      // 关闭编辑状态
      setEditingField(null);
    }
  };

  // 添加点击事件监听器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 如果当前没有正在编辑的字段，直接返回
      if (!editingField) return;

      const target = event.target as HTMLElement;
      
      // 检查点击事件是否发生在编辑卡片内部
      // 查找所有可能的popover容器
      const popoverContainers = document.querySelectorAll('.acud-popover-content');
      let isClickInsidePopover = false;
      
      // 检查每个popover容器
      popoverContainers.forEach(container => {
        if (container.contains(target)) {
          isClickInsidePopover = true;
        }
      });

      // 检查是否点击在编辑图标上
      const editIcons = document.querySelectorAll('.edit-icon');
      let isClickOnEditIcon = false;
      editIcons.forEach(icon => {
        if (icon.contains(target)) {
          isClickOnEditIcon = true;
        }
      });

      // 检查是否点击在输入框或者其他表单元素上
      const isClickOnInput = target.closest('input, textarea, .acud-input, .acud-textarea');
      
      // 如果点击发生在编辑相关元素内部，不关闭编辑卡片
      if (isClickInsidePopover || isClickOnEditIcon || isClickOnInput) {
        return;
      }

      // 如果点击发生在编辑卡片和编辑图标之外，关闭编辑卡片
      setEditingField(null);
      setNameInput('');
      setNameError('');
      setDescInput('');
      setDescError('');
    };

    // 使用捕获阶段监听，确保在其他事件处理器之前执行
    document.addEventListener('mousedown', handleClickOutside, true);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [editingField]);

  // 如果显示创建表单，则渲染CreateInstance组件
  if (showCreateForm) {
    // 直接返回CreateInstance组件，确保它可以控制整个页面视图
    return <CreateInstance onBack={onBackToList} />;
  }

  return (
    <div className={styles['aigw-instance-list-page']}>
      <div className={styles['aigw-instance-list-page-header']}>
        {/* 通知栏 */}
        <div className={styles['notification-bar']}>
          <div className={styles['notification-icon']}></div>
          <div className={styles['notification-text']}>
            云原生网关提供企业级托管网关服务，目前正处于公测阶段。在此期间，将免费为您提供服务，诚邀您体验功能。
          </div>
        </div>

        {/* 页面头部元素 */}
        <div className={styles['page-header']}>
          <div className={styles['page-title']}>网关实例</div>
          <div className={styles['page-actions']}>
            <a
              className={`${styles['feature-intro-btn']} ${showIntroduction ? styles.active : styles.inactive}`}
              onClick={toggleIntroduction}
              role="button"
            >
              <ProductIntroIcon
                className={styles['product-intro-icon']}
                color={showIntroduction ? '#2468F2' : '#151B26'}
              />
              功能简介            </a>
            {/* <Link href="https://cloud.baidu.com/doc/aigw/index.html" target="_blank">帮助文档</Link> */}
          </div>
        </div>

        {/* 简介卡片 */}
        {showIntroduction && (
          <div className={styles.introduction}>
            <div className={styles['introduction-title-row']}>
              <div className={styles['introduction-title']}>云原生网关简介</div>
              <a
                className={styles['hide-btn']}
                onClick={toggleIntroduction}
                aria-label="隐藏网关简介"
                role="button"
              >
                隐藏
              </a>
            </div>
            <div className={styles['introduction-desc']}>
              云原生网关集流量网关、微服务网关、安全网关和 AI 网关四合一，实现碎片化网关的架构统一，提供无侵入式托管网关服务，支持推理服务代理及流量管控，是百度智能云 AI 原生应用的统一入口。
            </div>
            <div className={styles['introduction-items']}>
              <span>云原生 AI</span>
              <span>无侵入式</span>
              <span>自动免运维</span>
              <span>推理流量代理</span>
            </div>
          </div>
        )}
      </div>
      <div className={styles['aigw-instance-list-page-content']}>
        <div className="mse-custom-page-operation-container">
          <div className="mse-custom-page-operation-left">
            <Tooltip title={total >= 20 ? '最多创建 20 个网关实例' : undefined}>
              <span style={{ display: 'inline-block' }}>
                <Button
                  type="primary"
                  icon={<OutlinedPlusNew />}
                  onClick={onToCreate}
                  disabled={total >= 20}
                >
                  创建网关实例
                </Button>
              </span>
            </Tooltip>
          </div>
          <div className="mse-custom-page-operation-right">
            <Search
              allowClear
              value={inputValue}
              style={{ marginRight: 0, width: 300 }}
              placeholder="搜索实例名称"
              onSearch={doSearch}
              onChange={handleSearchChange}
              onBlur={handleSearchBlur}
            />
            <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          </div>
        </div>

        <Table
          dataSource={dataSource}
          columns={columns}
          rowKey="instanceId"
          loading={{
            loading: loading,
            size: 'small'
          }}
          pagination={false}
          scroll={{ x: 1068 }}
          onRow={(record) => ({
            onMouseEnter: () => onRowMouseEnter(record),
            onMouseLeave: onRowMouseLeave
          })}
        />

        <div className="paginationContainer" style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            showSizeChanger={true}
            showQuickJumper={true}
            current={pageNo}
            pageSize={pageSize}
            total={total}
            showTotal={(total) => `共 ${total} 条`}
            onChange={(page, newPageSize) => {
              setPageNo(page);
              if (newPageSize) {
                setPageSize(newPageSize);
              }
              run(page, newPageSize || pageSize);
            }}
            onShowSizeChange={(current, size) => {
              setPageNo(1);
              setPageSize(size);
              run(1, size);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default InstanceList;
