.routeDetailContainer {
  width: 100%;
  height: 100%;
  
  :global {
    .acud-detail-page-container {
      padding: 24px;
      background: #f5f5f5;
    }
  }
}

.routeDetailLayout {
  display: flex;
  width: 100%;
  min-height: 600px;
  background: #fff;
  border-radius: 4px;
}

.routeDetailSider {
  background-color: #fff;
  border-right: 1px solid #ebedf0;
  box-shadow: none;
  max-width: 180px;
  min-width: 180px;
  flex: 0 0 180px;
}

.routeDetailMenu {
  border-right: none;
  background-color: #fff;
  height: 100%;
  padding-top: 8px;
  
  :global {
    .acud-menu {
      height: 100%;
      box-shadow: none !important;
      padding-top: 8px;
    }
    
    .acud-menu-root {
      height: 100%;
      box-shadow: none !important;
    }
    
    .acud-menu-item {
      height: 40px !important;
      line-height: 40px !important;
      text-align: left;
      padding-left: 16px !important;
      font-size: 14px !important;
      margin: 0 !important;
      transition: none !important;
    }
    
    .acud-menu-item-selected {
      background-color: #ebf1ff;
      color: #2468f2;
      border-right: none !important;
      border-left: none !important;
      font-weight: normal !important;
      font-size: 14px !important;
      height: 40px !important;
      line-height: 40px !important;
    }
  }
}

.routeDetailContent {
  padding:24px;
  background-color: #fff;
  flex: 1;
}

.moduleWrapper {
  display: flex;
  flex-direction: column;
}

.moduleContainer {
  background: #fff;
  border-radius: 3px;
  padding: 20px;
  margin-bottom: 0;
}

.moduleTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
}

.moduleContent {
  width: 100%;
}

.infoItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.infoItem_last {
  display: flex;
  align-items: flex-start;
}


.infoLabel {
  min-width: 80px;
  color: #5C5F66;
  margin-right: 4px;
  text-align: left;
}

.fixedWidthLabel {
  width: 143px;
  min-width: 143px;
  flex-shrink: 0;
}

.infoValue {
  color: #262d40;
  display: flex;
  align-items: center;
  word-break: break-all;
}


.infoValueWithAction {
  display: flex;
  align-items: center;
  
  .infoValue {
    margin-right: 8px;
  }
  
  .editIcon {
    color: #2468F2;
    cursor: pointer;
    font-size: 16px;
    
    &:hover {
      color: #528EFF;
    }
  }
}

.authModalItem {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.authModalLabel {
  min-width: 80px;
  display: inline-block;
  color: #5C5F66;
}

.authModalTip {
  margin-bottom: 16px;
  color: #84878c;
  font-size: 12px;
  padding-left: 80px;
}

.authTransferContainer {
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  padding-left: 80px;
  width: 100%;
  max-width: 600px;
}

.pathInfo {
  display: flex;
  flex-direction: row;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
  
  > div {
    display: flex;
    align-items: center;
  }
}

.placeholder {
  padding: 80px 0;
  text-align: center;
  font-size: 14px;
  color: #999;
  background: #fafafa;
}

// HTTP方法标签样式
.methodTag {
  border: none;
  border-radius: 2px;
  line-height: 20px;

  &-GET {
    background: #E6F0FF;
    color: #2468F2;
  }

  &-POST {
    background: #ECFFE6;
    color: #30BF13;
  }

  &-PUT {
    background: #FFF4E6;
    color: #FF9326;
  }

  &-DELETE {
    background: #FFE8E6;
    color: #F33E3E;
  }

  &-PATCH {
    background: #ECE6FF;
    color: #41078C;
  }

  &-HEAD {
    background: #F7F7F9;
    color: #151B26;
  }

  &-OPTIONS {
    background: #E6F9FF;
    color: #004173;
  }
}

/* 添加一个新的全局样式规则 */
:global {
  .acud-tag.acud-routeDetail-limitedTag {
    max-width: 260px !important;
    
    > span {
      max-width: 260px !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      display: inline-block !important;
    }
  }
} 

  // 未认证标签样式
  .unAuthTag {
    background-color: #F7F7F9 !important;
    color: #84868C !important;
    font-size: 10px !important;
    border: none !important;
    padding: 0 4px !important;
    margin-left: 8px;
  }

  .transferItem {
    display: flex;
    align-items: center;
    width: 100%;
    padding-left: 12px;

    .transferItemTitle {
      max-width: 140px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  