import {Pagination} from 'acud';
import type {PaginationProps} from 'acud/lib/pagination';
import React, {memo, useEffect, useMemo} from 'react';

import {getPaginationVisible} from '@/utils/utils';

const PaginationOfAutoHide: React.FC<
  PaginationProps & {
    minPageSize?: number;
    onVisibleChange?: (val: boolean) => void;
  }
> = (props) => {
  const {minPageSize, ...otherProps} = props;
  const visible = useMemo(
    () => getPaginationVisible(otherProps.total || 0, minPageSize),
    [minPageSize, otherProps.total]
  );

  useEffect(() => {
    props.onVisibleChange && props.onVisibleChange(visible);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  if (visible) {
    return (
      <div className="common-pagination-container">
        <Pagination {...otherProps} />
      </div>
    );
  }

  return null;
};

export default memo(PaginationOfAutoHide);
