import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {getQueryParams, useRegion} from '@baidu/bce-react-toolkit';
import {Button, Link, Search, Table, Tag} from 'acud';
import {OutlinedInfoCircle, OutlinedPlus} from 'acud-icon';
import {useRequest} from 'ahooks';
import cx from 'classnames';

import {getServiceInstanceList} from '@/apis/serviceAdmin';
import BatchOperations from '@/components/BatchOperations';
import CreateServiceInstance from '@/components/CreateServiceInstance';
import DeleteServiceInstance from '@/components/DeleteServiceInstance';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';
import RefreshButton from '@/components/RefreshButton';
import urls from '@/utils/urls';

const ServiceTnstanceList: React.FC = () => {
  const {instanceId, serviceName, serviceId, namespace} = getQueryParams();
  const [dataSource, setDataSource] = useState([]);
  const [seachType, setSearchType] = useState('queryInstanceId');
  const [seacherValue, setSeacherValue] = useState('');
  const [health, setHealth] = useState('');
  const [isolate, setIsolate] = useState('');
  const [selectedRows, setSelectedRows] = useState<React.Key[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editInfo, setEditInfo] = useState({});
  const [isEdit, setIsEdit] = useState(false);

  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const {region} = useRegion();
  const isFirstRun = useRef(true);
  const navigate = useNavigate();

  const getServiceListBack = useCallback(
    async (params: any) => {
      return getServiceInstanceList({
        instanceId: instanceId,
        serviceId,
        pageNo,
        pageSize,
        serviceName,
        namespace,
        [seachType]: seacherValue,
        healthStatus: health,
        isolateStatus: isolate,
        ...params
      });
    },
    [
      pageNo,
      pageSize,
      instanceId,
      seacherValue,
      seachType,
      serviceId,
      health,
      isolate
    ]
  );

  const {
    run,
    loading,
    refresh: refreshInstanceList
  } = useRequest(getServiceListBack, {
    manual: true,
    onSuccess: (res) => {
      setSelectedRows([]);
      setSelectedRowKeys([]);
      setDataSource(res?.result?.result || []);
      setTotal(res?.result?.totalCount || 0);
    }
  });

  useEffect(() => {
    if (instanceId && serviceId) {
      run({});
    }
  }, [instanceId, serviceId]);

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    refreshInstanceList();
  }, [refreshInstanceList]);

  const handleChangeSearchType = (value: string) => {
    setSearchType(value);
  };

  const onConfirmSearch = (searchParam: any) => {
    const {type, value} = searchParam;
    setSeacherValue(value);
    setPageNo(1);
    run({
      [type]: value,
      pageNo: 1
    });
  };

  const onChange = (pag, filter) => {
    const {healthStatus, isolateEnable} = filter;
    const param: any = {
      healthStatus: '',
      isolateStatus: ''
    };
    setHealth('');
    setIsolate('');
    if (healthStatus) {
      param.healthStatus = healthStatus[0];
      setHealth(healthStatus[0]);
    }
    if (isolateEnable) {
      param.isolateStatus = isolateEnable[0];
      setIsolate(isolateEnable[0]);
    }
    run({
      ...param
    });
  };

  const columns = [
    {
      title: '实例ID',
      dataIndex: 'serviceInstanceId',
      width: 300,
      render(value) {
        return (
          <Link
            href={`#/service/instance/base/info?instanceId=${instanceId}&serviceName=${serviceName}&serviceId=${serviceId}&instanceServiceId=${value}&namespace=${namespace}`}
          >
            {value}
          </Link>
        );
      }
    },
    {
      title: '实例IP',
      dataIndex: 'host',
      width: 150,
      render(value) {
        return value || '-';
      }
    },
    {
      title: '端口',
      dataIndex: 'port',
      width: 100,
      render(value) {
        return value || '-';
      }
    },
    {
      title: '健康状态',
      dataIndex: 'healthStatus',
      width: 150,
      filters: [
        {text: '健康', value: true},
        {text: '异常', value: false}
      ],
      filterMultiple: false,
      render(value) {
        const type = value ? 'circle status-success' : 'circle status-warning';
        const text = value === true ? '健康' : value === false ? '异常' : '-';
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{paddingLeft: 0}}
            icon={<span className={type} />}
          >
            {text || '-'}
          </Tag>
        );
      }
    },
    {
      title: '隔离状态',
      dataIndex: 'isolateEnable',
      width: 150,
      filters: [
        {text: '隔离', value: true},
        {text: '不隔离', value: false}
      ],
      filterMultiple: false,
      render(value) {
        const type = value ? 'circle status-warning' : 'circle status-success';
        const text = value === true ? '隔离' : value === false ? '不隔离' : '-';
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{paddingLeft: 0}}
            icon={<span className={type} />}
          >
            {text || '-'}
          </Tag>
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      width: 150
    },
    {
      title: '最近健康检查时间',
      dataIndex: 'lastHeartbeatTime',
      width: 150
    },
    {
      title: '操作',
      className: 'action-cell',
      render: (value, row, index) => {
        return (
          <>
            <Button
              type="actiontext"
              onClick={() => {
                setIsModalOpen(true);
                setIsEdit(true);
                setEditInfo(row);
              }}
            >
              编辑
            </Button>
            <DeleteServiceInstance
              instanceId={instanceId}
              serviceId={serviceId}
              item={row}
              refresh={refreshInstanceList as any}
            />
          </>
        );
      }
    }
  ];

  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    newSelectedRows: any
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(newSelectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange
  };

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }
    navigate(urls.serviceManage);
  }, [region]);

  return (
    <div className={cx(['custom-layout'])}>
      <div className="mse-custom-header-wrap">
        <div className="mse-custom-header-left">
          <div className="custom-header-title">
            <Link href={`#/service/admin/list?instanceId=${instanceId}`}>
              服务管理
            </Link>
          </div>
          <div className="custom-header-title">
            &nbsp;<span>{` > ${serviceName}`}</span>
          </div>
        </div>
        <div className="mse-custom-header-right">
          <Link
            className="mse-custom-header-link"
            href="https://cloud.baidu.com/doc/MSE/index.html"
            target="_blank"
            icon={<OutlinedInfoCircle />}
          >
            产品文档
          </Link>
        </div>
      </div>
      <div className="mse-custom-page-content">
        <div className="mse-custom-page-content-title">服务实例列表</div>
        <div className="mse-custom-page-operation-container">
          <div className="mse-custom-page-operation-left">
            <Button
              type="primary"
              icon={<OutlinedPlus />}
              onClick={() => setIsModalOpen(true)}
            >
              创建服务实例
            </Button>
            <BatchOperations
              setSelectedRows={selectedRows}
              instanceId={instanceId}
              serviceId={serviceId}
              refresh={refreshInstanceList}
            />
          </div>
          <div className="mse-custom-page-operation-right">
            <Search
              multipleOption={[
                {
                  label: '实例ID',
                  value: 'queryInstanceId',
                  key: 'queryInstanceId'
                },
                {
                  label: '实例IP',
                  value: 'host',
                  key: 'host'
                }
              ]}
              placeholder={`请输入${seachType === 'queryInstanceId' ? '实例ID' : '实例IP'}关键字搜索`}
              onChangeMultiple={(value) => {
                handleChangeSearchType(value as string);
              }}
              multipleValue={seachType}
              style={{width: 300}}
              onSearch={onConfirmSearch}
            />
            <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          </div>
        </div>

        <Table
          dataSource={dataSource}
          columns={columns}
          rowKey="serviceInstanceId"
          loading={{
            loading: loading,
            size: 'small'
          }}
          pagination={false}
          onChange={onChange}
          rowSelection={{
            // type: selectionType,
            ...rowSelection
          }}
        />

        <div className="paginationContainer">
          <PaginationOfAutoHide
            showSizeChanger={true}
            showQuickJumper={true}
            current={pageNo}
            pageSize={pageSize}
            total={total}
            onChange={(page, pageSize) => {
              setPageNo(page);
              setPageSize(pageSize!);
              run({pageNo: page, pageSize});
            }}
          />
        </div>
        {isModalOpen && (
          <CreateServiceInstance
            isEdit={isEdit}
            isModalOpen={isModalOpen}
            instanceId={instanceId}
            serviceId={serviceId}
            serviceName={serviceName}
            namespace={namespace}
            instance={editInfo}
            refresh={refreshInstanceList as any}
            onClose={() => {
              setIsModalOpen(false);
              setIsEdit(false);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default ServiceTnstanceList;
