.tag-edit-panel {
  background: #f7f7f9;
  border-radius: 6px;
  padding: 16px;
  box-sizing: border-box;
  height: fit-content;

  .tag-form {
    :global {
      .acud-form-item-label {
        display: none;
      }
      .acud-form-item {
        margin-bottom: 16px !important;
      }
      .acud-btn-text-limited {
        padding: 0;
        min-width: auto;
      }
      .acud-select {
        background: #fff;
      }
    }
  }

  .acud-form-item-label {
    margin-right: 4px !important;
  }

  .tag-text {
    .tag-tip-warn {
      color: #f38900;
    }
  }

  .reminder {
    display: flex;
    width: fit-content;
    padding: 10px 30px 10px;
    margin-bottom: 20px;
    line-height: 20px;
    font-size: 12px;
    background-color: #f7f7f7;
    color: #666;
    width: 100%;

    .tag-tip-warn {
      color: #f38900 !important;
    }

    > label {
      display: inline-flex;
    }
  }

  .footer {
    display: flex;
    align-items: center;

    .tag-button-add {
      padding: 0 7px;
      margin: 0 9px 0 0;
      height: 20px;
      line-height: 20px;

      &-icon {
        margin-right: 4px;
        position: relative;
        bottom: 1px;
      }
    }
    a {
      color: #2468f2;
      font-size: 12px;
    }
    > * {
      margin-right: 16px;
      vertical-align: middle;
    }
  }

  .float-right {
    float: right;
    margin: 5px 0 0 0;
  }

  .inline-form {
    display: flex;
    flex-wrap: wrap;
  }

  .error-msg {
    display: block;
    margin-bottom: 10px;
    color: #f33e3e;
    margin-left: 7px;
  }

  .footer {
    .tag-button-add {
      padding: 0;
      margin-bottom: 8px;
    }
  }
}

.label-view-wrap {
  :global {
    .acud-tag {
      margin-bottom: 8px;
    }
  }
}
