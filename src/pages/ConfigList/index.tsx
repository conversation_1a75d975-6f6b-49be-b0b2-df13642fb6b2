import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState
} from 'react';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {
  Button,
  Empty,
  Form,
  Input,
  Link,
  Modal,
  Popover,
  Search,
  Table,
  toast,
  Tooltip
} from 'acud';
import {OutlinedEditingSquare} from 'acud-icon';
import {useRequest} from 'ahooks';

import {
  deleteConfigGroup,
  editConfigGroup,
  getConfigGroupList
} from '@/apis/configManage';
import EllipsisWrap from '@/components/Ellipsis';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';
import RefreshButton from '@/components/RefreshButton';
import {SmartContext} from '@/contexts/SmartContext';
import urls from '@/utils/urls';

import styles from './index.module.less';
import {CreateModal, ImportModal} from './modal';

const ConfigList: React.FC = () => {
  const {namespace} = getQueryParams();
  const [dataSource, setDataSource] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [seachType, setSearchType] = useState('name');
  const [seacherValue, setSeacherValue] = useState('');
  const {configInstance} = useContext(SmartContext);

  const getServiceListBack = useCallback(
    async (params: any) => {
      return getConfigGroupList({
        instanceId: configInstance,
        pageNo,
        pageSize,
        keywordType: seachType,
        keyword: seacherValue,
        ...params
      });
    },
    [pageNo, pageSize, configInstance, seacherValue, seachType]
  );

  const {
    run,
    loading,
    refresh: refreshInstanceList
  } = useRequest(getServiceListBack, {
    manual: true,
    onSuccess: (res) => {
      setDataSource(res?.result?.configGroups || []);
      setTotal(res?.result?.totalCount || 0);
    }
  });

  useEffect(() => {
    if (namespace) {
      setSearchType('namespace');
    }
  }, [namespace]);

  useEffect(() => {
    if (configInstance) {
      if (namespace) {
        run({
          keywordType: 'namespace',
          keyword: namespace
        });
        return;
      }
      run({});
    }
  }, [configInstance]);

  const onClickDeleteBtn = useCallback(
    (row: any) => {
      Modal.confirm({
        title: '删除配置分组',
        content: '确定要删除该配置分组吗？删除后无法恢复，请谨慎操作。',
        onOk: async () => {
          try {
            // 调用删除接口
            const res = await deleteConfigGroup({
              instanceId: configInstance,
              name: row.name,
              namespace: row.namespace
            });
            if (res?.success) {
              toast.success({
                message: `配置分组 ${row.name} 删除成功。`,
                duration: 5
              });
              onClickRefreshBtn();
            }
          } catch (err) {
            console.error(err);
            toast.error({
              message: `配置分组 ${row.name} 删除失败，请重新操作。`,
              duration: 5
            });
          }
        }
      });
    },
    [configInstance]
  );

  const columns = [
    {
      title: '配置分组',
      dataIndex: 'name',
      width: 200,
      key: 'name',
      render(value, record) {
        const {namespace, name} = record;
        return (
          <EllipsisWrap
            data={
              <Link
                href={`#${urls.configFileList}?instanceId=${configInstance}&configGroupName=${name}&namespace=${namespace}`}
              >
                {name}
              </Link>
            }
            width={175}
          />
        );
      }
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
      width: 200,
      render(value) {
        return <EllipsisWrap data={value} width={175} />;
      }
    },
    {
      title: '备注',
      dataIndex: 'comment',
      key: 'comment',
      width: 200,
      render(value, record) {
        const [popVisible, setPopVisible] = useState(false);
        const [showEdit, setShowEdit] = useState(false);
        const [form] = Form.useForm();
        const editComment = async () => {
          try {
            await form.validateFields();
            const {comment} = form.getFieldsValue();
            form.setFieldsValue({
              comment
            });
            await editConfigGroup({
              instanceId: configInstance,
              name: record.name,
              namespace: record.namespace,
              comment
            });
            toast.success({
              message: '配置分组备注编辑成功',
              duration: 5
            });
            setPopVisible(false);
            refreshInstanceList();
          } catch (error) {
            console.error(error);
          }
        };
        const onPopVisibleChange = (visible) => {
          setPopVisible(visible);
        };
        const cancelAddress = () => {
          form.setFieldsValue({
            comment: value
          });
          setPopVisible(false);
        };
        const mouseOverHandler = () => {
          setShowEdit(true);
        };
        const mouseLeaveHandler = () => {
          setShowEdit(false);
        };
        return (
          <div
            className={styles['edit-cell-wrap']}
            onMouseEnter={mouseOverHandler}
            onMouseLeave={mouseLeaveHandler}
          >
            <EllipsisWrap data={value} width={175} />
            {
              <Popover
                placement="top"
                visible={popVisible}
                trigger="click"
                onVisibleChange={onPopVisibleChange}
                content={
                  <div className={styles['table-update-comment']}>
                    <Form
                      form={form}
                      initialValues={{
                        comment: value
                      }}
                    >
                      <Form.Item
                        rules={[
                          {
                            max: 200,
                            message: '不可超过200个字符'
                          }
                        ]}
                        name="comment"
                      >
                        <Input.TextArea limitLength={200} />
                      </Form.Item>
                      <div className="action-content">
                        <Button
                          style={{marginRight: 8}}
                          type="primary"
                          onClick={editComment}
                        >
                          确定
                        </Button>
                        <Button onClick={cancelAddress}>取消</Button>
                      </div>
                    </Form>
                  </div>
                }
              >
                {(showEdit || popVisible) && (
                  <OutlinedEditingSquare className={styles['edit-cell-icon']} />
                )}
              </Popover>
            }
          </div>
        );
      }
    },
    {
      title: '配置文件数',
      dataIndex: 'fileCount',
      width: '20%',
      key: 'fileCount'
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: '20%',
      key: 'createTime'
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: '10%',
      className: 'action-cell',
      render(_, row) {
        return (
          <>
            <Tooltip
              trigger={row?.fileCount ? 'hover' : ''}
              title="当前配置分组仍有配置文件，您需要先删除配置文件才能删除该配置分组"
            >
              <Button
                type="actiontext"
                onClick={() => {
                  onClickDeleteBtn(row);
                }}
                disabled={row?.fileCount}
              >
                删除
              </Button>
            </Tooltip>
          </>
        );
      }
    }
  ];

  const handleChangeSearchType = (value: string) => {
    setSearchType(value);
  };

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    refreshInstanceList();
  }, [refreshInstanceList]);

  const onConfirmSearch = (searchParam: any) => {
    const {value} = searchParam;
    setSeacherValue(value);
    setPageNo(1);
    run({
      pageNo: 1,
      keyword: value
    });
  };

  const customEmpty = useMemo(() => {
    return (
      <Empty
        description={
          configInstance ? (
            '暂无数据'
          ) : (
            <span>
              暂无可用实例，已有实例版本较低或暂未创建实例，您可前往
              <a href="#/instance/list">实例列表</a>或
              <a
                href="https://console.bce.baidu.com/support/#/ticket/#/ticket/create"
                target="_blank"
                rel="noreferrer"
              >
                提交工单
              </a>
              升级已有实例
            </span>
          )
        }
      />
    );
  }, [configInstance]);

  return (
    <div>
      {configInstance && (
        <div className="mse-custom-page-operation-container">
          <div className="mse-custom-page-operation-left">
            <CreateModal
              instanceId={configInstance}
              refresh={refreshInstanceList}
            />
            <ImportModal
              instanceId={configInstance}
              refresh={refreshInstanceList}
            />
          </div>
          <div className="mse-custom-page-operation-right">
            <Search
              multipleOption={[
                {
                  label: '配置分组',
                  value: 'name',
                  key: 'name'
                },
                {
                  label: '命名空间',
                  value: 'namespace',
                  key: 'namespace'
                }
              ]}
              placeholder={`请输入${seachType === 'name' ? '配置分组' : '命名空间'}`}
              onChangeMultiple={(value) => {
                handleChangeSearchType(value as string);
              }}
              defaultValue={namespace}
              multipleValue={seachType}
              style={{width: 300}}
              onSearch={onConfirmSearch}
            />
            <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          </div>
        </div>
      )}

      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="index"
        loading={{
          loading: loading,
          size: 'small'
        }}
        locale={{
          emptyText: customEmpty
        }}
        pagination={false}
      />

      <div className="paginationContainer">
        <PaginationOfAutoHide
          showSizeChanger={true}
          showQuickJumper={true}
          current={pageNo}
          pageSize={pageSize}
          total={total}
          onChange={(page, pageSize) => {
            setPageNo(page);
            setPageSize(pageSize!);
            run({
              pageNo: page,
              pageSize
            });
          }}
        />
      </div>
    </div>
  );
};

export default ConfigList;
