import React, {useEffect, useMemo} from 'react';
import {HashRouter, Navigate, Route, Routes} from 'react-router-dom';
import {
  AppContextActionType,
  useAppContext,
  useDocumentTitle
} from '@baidu/bce-react-toolkit';
import {AppLayout} from '@baidu/bce-react-toolkit';
import {Loading} from 'acud';
import {useBoolean} from 'ahooks';

import {queryIamStsRole} from '@/apis/auth';
import menus, {flattenedMenuList} from '@/pages';

import Activation from './pages/Activation';
import urls from './utils/urls';

export default function App() {
  const [inited, {setTrue: setInitStatus}] = useBoolean(false);
  const routes = useMemo(() => {
    return flattenedMenuList.filter((item) => item.Component);
  }, []);

  const {appState, appDispatch} = useAppContext();

  useDocumentTitle();

  useEffect(() => {
    queryIamStsRole({roleName: 'BceServiceRole_csm'})
      .then((res) => {
        if (res?.result?.name) {
          appDispatch({
            type: AppContextActionType.ACTIVATE_PRODUCT
          });
        }
        setInitStatus();
      })
      .finally(() => {
        setInitStatus();
      });
  }, []);

  return (
    <HashRouter>
      {inited ? (
        <AppLayout menus={menus} enableBuiltInProductActivation={false}>
          <Routes>
            {appState?.isActivated ? (
              routes.map(({key, Component}) => {
                const Element = Component!;
                return (
                  <Route path={key} key={key} element={<Element />}></Route>
                );
              })
            ) : (
              <Route
                path={urls.registrationList}
                key={urls.registrationList}
                element={<Activation />}
              ></Route>
            )}
            {routes.map(({key, Component}) => {
              const Element = Component!;
              return <Route path={key} key={key} element={<Element />}></Route>;
            })}
            <Route
              path="*"
              element={<Navigate to={urls.registrationList} replace />}
            />
          </Routes>
        </AppLayout>
      ) : (
        <Loading loading={true} size="small"></Loading>
      )}
    </HashRouter>
  );
}
