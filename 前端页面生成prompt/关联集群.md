1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、路由配置、消费者管理），点击每个导航栏右侧会切换成当前导航栏下对应的页面。点击实例详情右侧包含 2 个标签页（基本信息、实例关联信息），在实例关联信息标签页中有一个关联容器集群，点击关联容器集群按钮会出现一个弹窗进行关联。

2. 需求描述：现在，我期望这个弹窗中新增一个字段：监听 K8S Ingress，右侧是一个复选框，复选框右侧文案为"开启监听"，默认不勾选。勾选之后，在下方出现 2 个字段：1. 指定 IngressClass 2. 指定命名空间，指定 IngressClass 右侧为 Radio. Button，包含 2 个选项（全部同步/自定义范围），默认选中全部同步，如果选择自定义范围，则在指定 IngressClass 和指定命名空间之间新增一个字段：IngressClass，右侧为一个输入框，用于输入IngressClass， placeholder 为“请输入名称，目前仅支持一个”；指定命名空间字段右侧为 Radio. Button，仅包含 1 个选项（全部同步），且默认选中。其余参数不要变动。点击弹窗中的确定按钮执行关联集群操作。确定后调用关联集群这个接口，接口需要的请求参数和接口文档完全一致，你需要在遵循相关规范的前提下修改这个弹窗中涉及模块相关的参数和字段。你需要按照接口文档中的对应字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。注意：不要修改我已有的交互逻辑和样式！


