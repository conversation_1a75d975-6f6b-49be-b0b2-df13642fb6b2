# 根据集群ID和命名空间查询服务列表接口（查询集群中指定命名空间下服务列表）

## 接口基本信息

- **接口说明**：根据集群ID和命名空间查询集群中所有服务
- **接口地址**：`/api/aigw/v1/aigateway/cluster/service`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| clusterId | String | 是 | 集群ID | cce-qhjz40ds |
| namespace | String | 是 | 命名空间名称 | default |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": [
        "inference-service-1",
        "inference-service-2",
        "model-server",
        "feature-extractor"
    ]
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Array | 服务名称列表 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查集群ID或命名空间格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询服务的权限 |
| 404 | 集群或命名空间不存在 | 确认集群ID和命名空间是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/cluster/service?clusterId=cce-qhjz40ds&namespace=default
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": [
        "inference-service-1",
        "inference-service-2",
        "model-server",
        "feature-extractor",
        "api-gateway",
        "monitoring-service"
    ]
}
```

## 注意事项

1. 集群ID必须符合规范格式，通常以特定前缀（如`cce-`）开头
2. 命名空间名称区分大小写，需要精确匹配
3. 接口仅返回服务名称列表，不包含服务的详细信息
4. 返回结果中的服务列表按字母顺序排序
5. 如果指定命名空间中没有服务，将返回空数组`[]`
6. 用户需要具有对应集群和命名空间的查询权限才能获取服务列表 