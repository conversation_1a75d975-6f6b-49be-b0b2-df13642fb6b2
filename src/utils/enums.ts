// 实例列表状态
export const RegistractionStatusDict = new Map([
  [0, {text: '初始化', iconClass: 'circle status-active'}],
  [1, {text: '创建中', iconClass: 'circle status-processing'}],
  [2, {text: '运行中', iconClass: 'circle status-success'}],
  [3, {text: '调整中', iconClass: 'circle status-active'}],
  [4, {text: '释放中', iconClass: 'circle status-active'}],
  [5, {text: '运行异常', iconClass: 'circle status-warning'}],
  [6, {text: '创建失败', iconClass: 'circle status-error'}],
  [7, {text: '待就绪', iconClass: 'circle status-processing'}],
]); // 实例状态

// 配置文件状态
export const ConfigFileStatusDict = new Map([
  ['to-be-released', {text: '待发布', iconClass: 'circle status-inactive'}],
  ['normal', {text: '已发布', iconClass: 'circle status-success'}],
  ['failure', {text: '发布失败', iconClass: 'circle status-error'}]
]);

export const FormatMapping = {
  text: 'plaintext',
  json: 'json',
  xml: 'xml',
  yaml: 'yaml',
  html: 'html',
  properties: 'plaintext',
  toml: 'yaml'
};

export const FormatOptions = [
  {
    label: 'TEXT',
    value: 'text'
  },
  {
    label: 'JSON',
    value: 'json'
  },
  {
    label: 'XML',
    value: 'xml'
  },
  {
    label: 'YAML',
    value: 'yaml'
  },
  {
    label: 'HTML',
    value: 'html'
  },
  {
    label: 'Properties',
    value: 'properties'
  },
  {
    label: 'TOML',
    value: 'toml'
  }
];

export const ReleaseHistoryType = {
  normal: '发布',
  rollback: '回滚',
  delete: '删除'
};
