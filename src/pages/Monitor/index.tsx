import React, {useCallback, useEffect, useState} from 'react';
import {Table, Tag, Search, Button} from 'acud';
import {useRequest} from 'ahooks';

import {getPrometheusList} from '@/apis/instance';
import RefreshButton from '@/components/RefreshButton';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';

import {CreateModal, deleteConfirm, EditModal} from './modals';

const Monitor = (props) => {
  const {detail} = props;
  const [dataSource, setDataSource] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [searchKey, setSearchKey] = useState('');
  const [instanceId, setInstanceId] = useState('');

  useEffect(() => {
    if (detail?.id) {
      setInstanceId(detail.id);
    }
  }, [detail]);

  const getNameSpacelist = useCallback(
    async (params: any) => {
      return getPrometheusList({
        pageNo,
        pageSize,
        ...params
      });
    },
    [pageNo, pageSize]
  );

  const {run, loading} = useRequest(getNameSpacelist, {
    manual: true,
    ready: detail?.monitorInstanceId,
    onSuccess: (res) => {
      if (res?.result?.items?.length) {
        const dataSource = res?.result?.items.filter((element: any) => {
          const instanceID = element.spec?.instanceID;
          return detail?.monitorInstanceId === instanceID;
        });
        setDataSource(dataSource);
        setTotal(res?.result?.totalCount || 0);
      }
    }
  });

  // 刷新列表
  const refreshInstanceList = useCallback(() => {
    run({});
  }, [run]);

  // 点击搜索
  const onConfirmSearch = useCallback((value) => {
    setSearchKey(value);
    run({
      searchKey: value
    });
  }, [run]);

  // 点击刷新
  const onClickRefreshBtn = useCallback(() => {
    refreshInstanceList();
  }, [refreshInstanceList]);

  useEffect(() => {
    run({});
  }, [detail, run]);

  const InstanceStatus: any = {
    Pending: {
      text: '等待中',
      value: 'pending',
      iconClass: 'circle status-active'
    },
    Creating: {
      text: '创建中',
      value: 'pending',
      iconClass: 'circle status-active'
    },
    Terminating: {
      text: '删除中',
      value: 'pending',
      iconClass: 'circle status-active'
    },
    Failed: {text: '失败', value: 'error', iconClass: 'circle status-error'},
    Running: {
      text: '运行中',
      value: 'success',
      iconClass: 'circle status-success'
    },
    Upgrading: {
      text: '升级中',
      value: 'pending',
      iconClass: 'circle status-active'
    },
    Unknown: {text: '未知', value: 'error', iconClass: 'circle status-error'}
  };

  const cpromInstanceTemplateMap = new Map([['advance-v2', '基础版']]);

  const columns = [
    {
      title: '实例名称',
      dataIndex: 'instanceName',
      key: 'instanceName',
      render(value, record) {
        const {instanceName} = record.spec ?? {};
        return instanceName;
      }
    },
    {
      title: '实例ID',
      dataIndex: 'instanceID',
      key: 'instanceID',
      render(value, record) {
        const {instanceID} = record.spec ?? {};
        return instanceID;
      }
    },
    {
      title: '实例状态',
      dataIndex: 'status',
      key: 'status',
      render: (value: {phase: string}) => {
        const {phase} = value ?? {};
        const statusItem = InstanceStatus[phase];
        return statusItem ? (
          <Tag
            color="transparent"
            className="table-status"
            style={{paddingLeft: 0}}
            icon={<span className={statusItem.iconClass} />}
          >
            {statusItem.text || '-'}
          </Tag>
        ) : null;
      }
    },
    {
      title: '实例规格',
      dataIndex: 'metadata',
      key: 'metadata',
      render(value) {
        const cpromInstanceTemplate =
          value?.labels['cprom-instance-template'] ?? '';
        return cpromInstanceTemplateMap.get(cpromInstanceTemplate);
      }
    },
    {
      title: '存储时长',
      dataIndex: 'retentionPeriod',
      key: 'retentionPeriod',
      render(value, record) {
        const {
          vmClusterConfig: {retentionPeriod}
        } = record.spec ?? {};
        return `${retentionPeriod}`;
      }
    },
    {
      title: 'Grafana 服务',
      dataIndex: 'monitorGrafanaName',
      key: 'monitorGrafanaName',

      render(value, record) {
        const {monitorGrafanaId} = record;
        return (
          <a
            href={`https://console.bce.baidu.com/cprom/#/grafana/detail?monitorGrafanaId=${monitorGrafanaId}`}
            target="_blank"
            rel="noreferrer"
          >
            {value}
          </a>
        );
      }
    }
  ];

  return (
    <div className="aigw-detail-content-wrap">
      <div className="aigw-detail-content-title">路由列表</div>
      <div className="aigw-detail-operation-container">
        <div className="aigw-detail-operation-left">
          <CreateModal refresh={refreshInstanceList} instanceId={instanceId} />
        </div>
        <div className="aigw-detail-operation-right">
          <Search
            placeholder="请输入路由名称搜索"
            style={{width: 300}}
            onSearch={onConfirmSearch}
          />
          <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
        </div>
      </div>
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="id"
        loading={{
          loading: loading,
          size: 'small'
        }}
        pagination={false}
      />

      <div className="paginationContainer">
        <PaginationOfAutoHide
          showSizeChanger={true}
          showQuickJumper={true}
          current={pageNo}
          pageSize={pageSize}
          total={total}
          onChange={(page, pageSize) => {
            setPageNo(page);
            setPageSize(pageSize!);
            run({pageNo: page, pageSize});
          }}
        />
      </div>
    </div>
  );
};

export default Monitor;
