import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

import {BASE_URL} from './index';

/**
 * 获取配置分组列表
 */
export function getConfigGroupList(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configGroups`,
    params: data
  });
}

/**
 * 创建配置分组
 */
export function createConfigGroup(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configGroups`,
    data,
    method: 'POST'
  });
}

/**
 * 编辑配置分组
 */
export function editConfigGroup(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configGroups`,
    data,
    method: 'PUT'
  });
}

/**
 * 删除配置分组
 */
export function deleteConfigGroup(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configGroups`,
    method: 'DELETE',
    params: data
  });
}

/**
 * 导入配置分组
 */
export function importConfigGroup(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configFilesArchive`,
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 获取配置文件列表
 */
export function getConfigFileList(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configFiles`,
    params: data
  });
}

/**
 * 获取配置文件详情
 */
export function getConfigFileDetail(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configFile`,
    params: data
  });
}

/**
 * 新建配置文件
 */
export function createConfigFile(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configFiles`,
    method: 'POST',
    data
  });
}

/**
 * 编辑配置文件
 */
export function editConfigFile(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configFiles`,
    method: 'PUT',
    data
  });
}

/**
 * 删除配置文件
 */
export function deleteConfigFile(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configFiles/batch?action=delete`,
    method: 'POST',
    data
  });
}

/**
 * 发布配置文件
 */
export function releaseConfigFile(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configReleases`,
    method: 'POST',
    data
  });
}

/**
 * 获取配置文件发布列表
 */
export function getConfigReleseList(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configReleases`,
    params: data
  });
}

/**
 * 获取配置文件发布详情
 */
export function getConfigReleseDetail(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configRelease`,
    params: data
  });
}

/**
 * 删除发布版本
 */
export function deleteConfigRelese(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configReleases`,
    method: 'DELETE',
    params: data
  });
}

/**
 * 回滚到发布版本
 */
export function backConfigRelese(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configReleases`,
    method: 'PUT',
    data
  });
}

/**
 * 获取发布历史列表
 */
export function getReleseHistoryList(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/configReleaseRecords`,
    params: data
  });
}
