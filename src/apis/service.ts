import { BaseResponseType, request } from '@baidu/bce-react-toolkit';

import { BASE_URL } from './index';

/**
 * 查询实例中服务列表
 * 
 * 请求参数：
 * - instanceId: 网关实例ID
 * - keyword: 服务名称关键字，用于模糊搜索（可选）
 * - pageNo: 页码，默认为1（可选）
 * - pageSize: 每页数量，默认为10（可选）
 * - orderBy: 排序字段，默认为createTime（可选）
 * - order: 排序方式，可选值：desc（降序）、asc（升序），默认为desc（可选）
 * 
 * 响应字段：
 * - page: 分页信息对象
 * - page.orderBy: 排序字段
 * - page.order: 排序方式
 * - page.pageNo: 当前页码
 * - page.pageSize: 每页数量
 * - page.totalCount: 总记录数
 * - page.result: 服务列表
 * - page.result[].serviceName: 服务名称
 * - page.result[].serviceSource: 服务来源
 * - page.result[].routeCount: 关联路由数量
 * - page.result[].createTime: 创建时间
 * - page.result[].serviceStatus: 服务状态
 */
export function getInstanceServices(
  instanceId: string,
  params?: {
    keyword?: string;
    pageNo?: number;
    pageSize?: number;
    orderBy?: string;
    order?: string;
  },
  region?: string
): BaseResponseType<{
  page: {
    orderBy: string;
    order: string;
    pageNo: number;
    pageSize: number;
    totalCount: number;
    result: Array<{
      serviceName: string;
      serviceSource: string;
      routeCount: number;
      createTime: string;
      serviceStatus: string;
      namespace: string;
    }>;
  };
}> {
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/service/list`,
    params,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 查询服务详情
 * 
 * 请求参数：
 * - instanceId: 网关实例ID
 * - serviceName: 服务名称
 * 
 * 响应字段：
 * - result: 服务详情对象
 * - result.clusterId: 集群ID
 * - result.namespace: 命名空间名称
 * - result.routeCount: 已关联路由数量
 * - result.serviceSource: 服务来源
 */
export function getServiceDetail(
  instanceId: string,
  serviceName: string
): BaseResponseType<any> {
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/${serviceName}/service`
  });
}

/**
 * 查询集群中命名空间列表
 * 
 * 请求参数：
 * - clusterId: 集群ID
 * 
 * 响应字段：
 * - result: 命名空间名称列表
 */
export function getClusterNamespaces(
  clusterId: string
): BaseResponseType<any> {
  return request({
    url: `/api/aigw/v1/aigateway/cluster/namespace`,
    params: { clusterId }
  });
}

/**
 * 查询集群中指定命名空间下服务列表
 * 
 * 请求参数：
 * - clusterId: 集群ID
 * - namespace: 命名空间名称
 * 
 * 响应字段：
 * - result: 服务名称列表
 */
export function getNamespaceServices(
  clusterId: string,
  namespace: string
): BaseResponseType<any> {
  return request({
    url: `/api/aigw/v1/aigateway/cluster/service`,
    params: { clusterId, namespace }
  });
}

/**
 * 添加服务
 * 
 * 请求参数：
 * - instanceId: 网关实例ID
 * - clusterId: 集群ID
 * - serviceSource: 服务来源
 * - namespace: 服务所在的命名空间
 * - serviceList: 要添加的服务列表
 * 
 * 响应字段：
 * - success: 请求是否成功
 * - status: 状态码
 * - result: 结果对象
 * - result.addedCount: 成功添加的服务数量
 */
export function addServices(
  instanceId: string,
  data: {
    clusterId: string;
    serviceSource: string;
    namespace: string;
    serviceList: string[];
  }
): BaseResponseType<{addedCount: number}> {
  return request({
    url: `/api/aigw/v1/aigateway/cluster/${instanceId}/serviceList`,
    method: 'POST',
    data
  });
}

/**
 * 移除服务
 * 
 * 请求参数：
 * - instanceId: 网关实例ID
 * - serviceName: 服务名称
 * - namespace: 命名空间
 * 
 * 响应字段：
 * - result: null (移除服务成功后返回null)
 */
export function removeService(
  instanceId: string,
  serviceName: string,
  namespace: string
): BaseResponseType<any> {
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/${serviceName}/${namespace}/service`,
    method: 'DELETE'
  });
}

/**
 * 查询服务端口信息
 * 
 * 请求参数：
 * - clusterId: 集群ID
 * - serviceName: 服务名称
 * - namespace: 命名空间名称
 * 
 * 响应字段：
 * - result: 服务端口信息列表，格式为"端口号 协议类型"
 */
export function getServicePorts(
  clusterId: string,
  serviceName: string,
  namespace: string,
  region?: string
): BaseResponseType<string[]> {
  return request({
    url: `/api/aigw/v1/aigateway/${clusterId}/${serviceName}/${namespace}/port`,
    method: 'GET',
    headers: {
      'X-Region': region || ''
    }
  });
} 