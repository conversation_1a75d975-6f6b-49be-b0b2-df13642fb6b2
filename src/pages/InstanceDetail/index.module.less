//设置全局的modal样式 ,使用global会全局生效,即这个项目中modalHeight会全局生效
:global {

  .modalHeight {
    max-height: 80vh !important;
    overflow-y: auto !important;

    .acud-modal-content {
      max-height: inherit !important;

      .acud-modal-body {
        margin: 24px !important;
      }
    }
  }

  // 非confirm弹窗的body的margin，上下左右均为24px 
  .acud-modal {
    .acud-modal-body {
      margin: 24px !important; //因为是放在global里面，因此全局会生效
    }
  }

  .acud-modal-dialogbox-confirm {
    .acud-modal-body {
      margin: 8px 24px !important; //确认弹窗的body的margin,注意确认弹窗的body上margin是8px
    }
  }

}

.instance-detail-container {
  width: 100%;
  height: 100%;
}

.instance-detail-layout {
  background-color: #fff;
  min-height: calc(100vh - 132px);
  display: flex;
}

.instance-detail-sider {
  background-color: #fff;
  border-right: 1px solid #ebedf0;
  box-shadow: none;
  max-width: 160px;
  min-width: 160px;
  flex: 0 0 160px;
}

.instance-detail-menu {
  border-right: none;
  background-color: #fff;
  height: 100%;
  padding-top: 16px;
}

.instance-detail-menu :global(.acud-menu) {
  height: 100%;
  box-shadow: none !important;
  padding-top: 8px;
}

.instance-detail-menu :global(.acud-menu-root) {
  height: 100%;
  box-shadow: none !important;
}

/* 移除所有可能的阴影 */
.instance-detail-menu :global(*) {
  box-shadow: none !important;
}

.instance-detail-menu :global(.acud-menu-item) {
  height: 40px !important;
  line-height: 40px !important;
  text-align: left;
  padding-left: 16px !important;
  font-size: 14px !important;
  margin: 0 !important;
  transition: none !important;
}

.instance-detail-menu :global(.acud-menu-item-selected) {
  background-color: #ebf1ff;
  color: #2468f2;
  border-right: none !important;
  border-left: none !important;
  font-weight: normal !important;
  font-size: 14px !important;
  height: 40px !important;
  line-height: 40px !important;
}

.instance-detail-content {
  padding: 8px 24px;
  background-color: #fff;
  flex: 1;
}

.related-info-container {
  // padding: 16px;
  background-color: #fff;
  border-radius: 4px;
}

.cluster-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

:global(.status-badge) {
  display: inline-flex;
  align-items: center;

  &:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 6px;
  }

  &.status-normal:before {
    background-color: #30bf13;
  }

  &.status-error:before {
    background-color: #f33e3e;
  }

  &.status-warning:before {
    background-color: #ff9326;
  }
}

//抽屉的样式
:global(.acud-drawer-content) {
  :global(.acud-form-item-label > label) {
    font-weight: 400;
    width: 100%;
    font-size: 12px;
    color: #5C5F66;
    line-height: 20px;
    padding-left: 0 !important;
  }

  :global(.acud-form-item) {
    margin-bottom: 16px;
  }

  :global(.acud-form-item-label) {
    text-align: left;
    padding-left: 0 !important;
  }



  :global(.acud-col-4) {
    flex: 0 0 100px !important;
    max-width: 100px !important;
  }

  :global(.acud-form-item-control) {
    flex: 1;
    max-width: none !important;
  }

  :global(.acud-col.acud-form-item-control) {
    max-width: none !important;
    width: auto !important;
    flex: 1 1 auto !important;
  }

  :global(.acud-form-item-control-input) {
    max-width: none !important;
    width: 100% !important;
  }

  :global(.acud-table-row) {
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  :global(.acud-table-row-selected) {
    background-color: #ebf1ff;

    &:hover {
      background-color: #ebf1ff;
    }
  }
}

.readonly-field {
  min-height: 32px;
  line-height: 32px;
  color: #333;
  display: flex;
  align-items: center;
  width: 100%;
}

.drawer-alert-container {
  margin: -24px -24px 24px -24px;
}

.alert-message {
  margin-bottom: 16px;
  border-radius: 4px;
  border-left: none;
  border-right: none;
}






:global(.cluster-table-fixed-width) {
  width: 100% !important;
  // min-width: 680px !important;
  // max-width: 680px !important;
  border: 1px solid #E8E9EB;
  border-radius: 6px;
  overflow: hidden; // 确保圆角边框效果

  // 移除内边框
  .acud-table-thead>tr>th {
    border-right: none !important;
  }

  .acud-table-tbody>tr>td {
    border-right: none !important;
  }
}

.custom-radio-btn {
  width: 260px !important;
  height: 70px !important;
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  padding: 0 12px;
  box-sizing: border-box;
  text-align: left;
  background: #fff !important;
  border: 1px solid #2468F2 !important;
  border-radius: 6px !important;

  // 选中时的样式可根据acud的Radio.Button实际情况调整
  .radio-title {
    font-size: 14px;
    color: #2468F2;
    line-height: 22px;
    font-weight: 500;
  }

  .radio-desc {
    font-size: 12px;
    color: #5C5F66;
    line-height: 20px;
    margin-top: 2px;
  }

  // 去除默认Radio.Button的多余样式
  .acud-radio-button-inner {
    display: none;
  }
}