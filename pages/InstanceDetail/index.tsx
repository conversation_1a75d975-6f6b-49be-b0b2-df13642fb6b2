const handleMenuClick = (key) => {
  setActiveMenuItem(key);
  // 当点击实例详情菜单项时，将标签重置为基本信息
  if (key === 'instance-detail') {
    setActiveTab('basic-info');
  }
  
  // 更新URL，添加或更新activeMenu参数
  const currentUrl = new URL(window.location.href);
  const params = new URLSearchParams(currentUrl.hash.split('?')[1] || '');
  params.set('activeMenu', key);
  params.set('instanceId', instanceId); // 确保instanceId参数存在
  
  // 构建新的URL
  const hashPart = currentUrl.hash.split('?')[0];
  const newHash = `${hashPart}?${params.toString()}`;
  
  // 更新URL，但不触发页面重新加载
  window.history.replaceState(null, '', `#${newHash.substring(1)}`);
  
  console.log('菜单点击，更新URL:', newHash);
}; 