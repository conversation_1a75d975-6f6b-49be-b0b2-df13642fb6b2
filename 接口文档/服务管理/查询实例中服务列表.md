# 根据实例ID查询服务列表接口（查询实例中服务列表）

## 接口基本信息

- **接口说明**：根据实例ID查询网关实例关联的所有服务列表，支持服务名称模糊搜索
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/service/list`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| keyword | String | 否 | 服务名称关键字，用于模糊搜索 | inference |
| pageNo | Integer | 否 | 页码，默认为1 | 1 |
| pageSize | Integer | 否 | 每页数量，默认为10 | 10 |
| orderBy | String | 否 | 排序字段，默认为createTime | createTime |
| order | String | 否 | 排序方式，可选值：desc（降序）、asc（升序），默认为desc | desc |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 2,
        "result": [
            {
                "serviceName": "mnist-inference",
                "serviceSource": "CCE",
                "routeCount": 3,
                "createTime": "2025-04-15 10:30:45",
                "serviceStatus": "running",
                "namespace": "kube-system"
            },
            {
                "serviceName": "image-recognition",
                "serviceSource": "CCE",
                "routeCount": 1,
                "createTime": "2025-04-14 16:20:15",
                "serviceStatus": "running",
                "namespace": "kube-system"
            }
        ]
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| page | Object | 分页信息对象 |
| page.orderBy | String | 排序字段 |
| page.order | String | 排序方式，desc或asc |
| page.pageNo | Integer | 当前页码 |
| page.pageSize | Integer | 每页数量 |
| page.totalCount | Integer | 总记录数 |
| page.result | Array | 服务列表 |
| page.result[].serviceName | String | 服务名称 |
| page.result[].namespace | String | 服务所在命名空间 |
| page.result[].serviceSource | String | 服务来源，如"CCE"（容器引擎） |
| page.result[].routeCount | Integer | 关联路由数量 |
| page.result[].createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| page.result[].serviceStatus | String | 服务状态，可能的值：<br>- running：运行中<br>- deleted：已删除<br>- error：错误<br>- unknown：未知 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询服务列表的权限 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/gw-ist9vvin/service/list?keyword=inference&pageNo=1&pageSize=10&orderBy=createTime&order=desc
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 2,
        "result": [
            {
                "serviceName": "text-embedding-inference",
                "serviceSource": "CCE",
                "routeCount": 5,
                "createTime": "2025-04-16 14:25:30",
                "serviceStatus": "running",
                "namespace": "kube-system"
            },
            {
                "serviceName": "mnist-inference",
                "serviceSource": "CCE",
                "routeCount": 3,
                "createTime": "2025-04-15 10:30:45",
                "serviceStatus": "running",
                "namespace": "kube-system"
            }
        ]
    }
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 查询参数`keyword`支持对服务名称进行模糊搜索，不区分大小写
3. 查询参数支持分页和排序功能，可根据需要进行自定义
4. 服务来源目前仅支持"CCE"（容器引擎）
5. 服务状态表示服务当前的运行状态，可根据状态判断服务是否可用
6. 如果实例下没有服务，或没有匹配关键字的服务，将返回空数组`[]`
7. 用户需要具有对应实例的查询权限才能获取服务列表 