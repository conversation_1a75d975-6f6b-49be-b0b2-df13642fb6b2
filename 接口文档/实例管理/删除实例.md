# 删除实例接口

## 接口基本信息

- **接口说明**：删除指定的AI网关实例
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}`
- **请求方式**：DELETE
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |


## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": null
}
```

### 错误响应体结构

```json
{
    "success": false,
    "status": 403,
    "message": "实例已开启删除保护，无法被删除"
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Null | 删除实例成功后返回null |
| message | String | 错误信息，仅在失败时返回 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有删除实例的权限 |
| 403 | 禁止操作 | 可能原因如下：<br>1. 实例已开启删除保护<br>2. 实例中仍有服务<br>3. 实例中仍有路由 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 409 | 资源冲突 | 实例可能正在被其他操作修改，请稍后重试 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
DELETE /api/aigw/v1/aigateway/gw-ist9vvin
X-Region: gz
```

## 响应示例

### 成功响应示例

```json
{
    "success": true,
    "status": 200,
    "result": null
}
```

### 禁止操作响应示例

```json
{
    "success": false,
    "status": 403,
    "message": "实例已开启删除保护，无法被删除"
}
```

```json
{
    "success": false,
    "status": 403,
    "message": "实例中仍有服务，无法被删除"
}
```

```json
{
    "success": false,
    "status": 403,
    "message": "实例中仍有路由，无法被删除"
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 删除实例操作不可逆，请谨慎操作
3. 系统会先进行以下检查，任一检查失败都会导致删除操作被拒绝：
   - 检查实例是否开启删除保护（deleteProtection=true）
   - 检查实例是否存在关联的服务
   - 检查实例是否存在关联的路由
4. 如果需要删除已开启删除保护的实例，需要先通过更新实例接口关闭删除保护
5. 如果需要删除仍有服务或路由的实例，需要先删除相关服务或路由
6. 用户需要具有对应实例的管理权限才能删除实例
7. 如果实例正在创建、调整或其他操作中，可能无法立即删除 