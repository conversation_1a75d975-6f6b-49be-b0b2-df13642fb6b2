import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState
} from 'react';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {Alert, Empty, Search, Table, Tag} from 'acud';
import {useRequest} from 'ahooks';

import {getReleseHistoryList} from '@/apis/configManage';
import EllipsisWrap from '@/components/Ellipsis';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';
import RefreshButton from '@/components/RefreshButton';
import {SmartContext} from '@/contexts/SmartContext';
import {ReleaseHistoryType} from '@/utils/enums';

import {ViewModal} from './modal';

const HistoryList: React.FC = () => {
  const {namespaceName} = getQueryParams();
  const [dataSource, setDataSource] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [seachType, setSearchType] = useState('namespace');
  const [seacherValue, setSeacherValue] = useState('');
  const {configInstance} = useContext(SmartContext);

  const getServiceListBack = useCallback(
    async (params: any) => {
      return getReleseHistoryList({
        instanceId: configInstance,
        pageNo,
        pageSize,
        keywordType: seachType,
        keyword: seacherValue,
        ...params
      });
    },
    [pageNo, pageSize, configInstance, seacherValue, seachType]
  );

  const {
    run,
    loading,
    refresh: refreshInstanceList
  } = useRequest(getServiceListBack, {
    manual: true,
    onSuccess: (res) => {
      setDataSource(res?.result?.configReleaseRecords || []);
      setTotal(res?.result?.totalCount || 0);
    }
  });

  useEffect(() => {
    if (configInstance) {
      run({});
    }
  }, [configInstance]);

  const columns = [
    {
      title: '配置文件名',
      dataIndex: 'file',
      width: 100,
      key: 'file',
      render: (value: string) => {
        return <EllipsisWrap data={value} width={150} />;
      }
    },
    {
      title: '版本号',
      dataIndex: 'release',
      key: 'release',
      width: 100,
      render: (value: string) => {
        return <EllipsisWrap data={value} width={150} />;
      }
    },
    {
      title: '发布类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
      render: (value: string) => {
        return ReleaseHistoryType[value] || '-';
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (value: string) => {
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{paddingLeft: 0}}
            icon={
              <span
                className={
                  value === 'success'
                    ? 'circle status-success'
                    : 'circle status-error'
                }
              />
            }
          >
            {value === 'success' ? '成功' : '失败'}
          </Tag>
        );
      }
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
      width: 100,
      render: (value: string) => {
        return <EllipsisWrap data={value} width={100} />;
      }
    },
    {
      title: '配置分组',
      dataIndex: 'group',
      key: 'group',
      width: 100,
      render: (value: string) => {
        return <EllipsisWrap data={value} width={100} />;
      }
    },
    {
      title: '操作人',
      dataIndex: 'createBy',
      key: 'createBy',
      width: 100,
      render: (value: string) => {
        return <EllipsisWrap data={value} width={100} />;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 100
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      className: 'action-cell',
      width: 160,
      render(_, row) {
        return <ViewModal initialValues={row} />;
      }
    }
  ];

  const handleChangeSearchType = (value: string) => {
    setSearchType(value);
  };

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    refreshInstanceList();
  }, [refreshInstanceList]);

  const onConfirmSearch = (searchParam: any) => {
    const {value} = searchParam;
    setSeacherValue(value);
    setPageNo(1);
    run({
      keyword: value,
      pageNo: 1
    });
  };

  const customEmpty = useMemo(() => {
    return (
      <Empty
        description={
          configInstance ? (
            '暂无数据'
          ) : (
            <span>
              暂无可用实例，已有实例版本较低或暂未创建实例，您可前往
              <a href="#/instance/list">实例列表</a>或
              <a
                href="https://console.bce.baidu.com/support/#/ticket/#/ticket/create"
                target="_blank"
                rel="noreferrer"
              >
                提交工单
              </a>
              升级已有实例
            </span>
          )
        }
      />
    );
  }, [configInstance]);

  return (
    <div>
      <Alert
        message="发布历史列表记录当前实例中向后端服务/应用提交的所有配置更新请求，包括配置文件的发布、历史版本的回滚、历史版本的删除。对配置文件进行的非发布操作由于并未实际推送到后端服务/应用，因此不会记录在当前列表中。"
        type="info"
        showIcon
        className="mse-list-alert"
      />
      {configInstance && (
        <div className="mse-custom-page-operation-container">
          <div className="mse-custom-page-operation-left">
            <div className="mse-detail-operation-total">
              共有{total}条发布历史记录
            </div>
          </div>
          <div className="mse-custom-page-operation-right">
            <Search
              multipleOption={[
                {
                  label: '命名空间',
                  value: 'namespace',
                  key: 'namespace'
                },
                {
                  label: '配置分组',
                  value: 'group',
                  key: 'group'
                },
                {
                  label: '配置文件',
                  value: 'file',
                  key: 'file'
                }
              ]}
              placeholder={`请输入${seachType === 'namespace' ? '命名空间' : seachType === 'group' ? '配置分组' : '配置文件'}`}
              onChangeMultiple={(value) => {
                handleChangeSearchType(value as string);
              }}
              defaultValue={namespaceName}
              multipleValue={seachType}
              style={{width: 300}}
              onSearch={onConfirmSearch}
            />
            <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          </div>
        </div>
      )}

      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="id"
        loading={{
          loading: loading,
          size: 'small'
        }}
        locale={{
          emptyText: customEmpty
        }}
        pagination={false}
      />

      <div className="paginationContainer">
        <PaginationOfAutoHide
          showSizeChanger={true}
          showQuickJumper={true}
          current={pageNo}
          pageSize={pageSize}
          total={total}
          onChange={(page, pageSize) => {
            setPageNo(page);
            setPageSize(pageSize!);
            run({
              pageNo: page,
              pageSize
            });
          }}
        />
      </div>
    </div>
  );
};

export default HistoryList;
