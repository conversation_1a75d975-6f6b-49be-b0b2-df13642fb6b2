# 查询同VPC中的CCE集群接口

## 接口基本信息

- **接口说明**：根据实例的VPC和所处地域查询当前VPC下的所有有权限（admin）的CCE集群
- **接口地址**：`/api/aigw/v1/aigateway/cluster/clusterList`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| vpcId | String | 是 | VPC的ID | vpc-55dfhgtw0x |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码，默认请求头中会包含 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": [
        {
            "clusterId": "cce-g74gwis23",
            "clusterName": "cce-development",
            "status": "RUNNING",
            "vpcCidr": "192.168.0.0/16",
            "region": "gz"
        }
    ]
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Array | 集群列表 |

### 结果列表字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| clusterId | String | 集群ID，唯一标识符 |
| clusterName | String | 集群名称 |
| status | String | 集群运行状态，可能的值：RUNNING（运行中）、CREATING（创建中）、ERROR（异常）、DELETING（删除中） |
| vpcCidr | String | VPC网段，如192.168.0.0/16 |
| region | String | 集群所属地域，如gz（广州）、bj（北京）等 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 确认用户是否有权限访问该VPC下的集群 |
| 404 | 未找到相关集群 | 确认VPC ID是否正确，或该VPC下是否有可用集群 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/cluster/clusterList?vpcId=vpc-55dfhgtw0x
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": [
        {
            "clusterId": "cce-g74gwis23",
            "clusterName": "cce-development",
            "status": "RUNNING",
            "vpcCidr": "192.168.0.0/16",
            "region": "gz"
        },
        {
            "clusterId": "cce-j98lmn056",
            "clusterName": "cce-production",
            "status": "RUNNING",
            "vpcCidr": "172.16.0.0/16",
            "region": "gz"
        }
    ]
}
```

## 注意事项

1. 该接口仅返回当前用户在指定VPC下有admin权限的CCE集群
2. 地域信息(X-Region)会从默认请求头中获取，无需额外传递
3. 查询结果按照集群创建时间降序排列 