.create-instance-modal-tips {
  color: #84868c;
  position: relative;
  padding-left: 10px;
  top: -20px;
}
.label-wrap {
  background: #f7f7f9;
  border-radius: 6px;
  padding: 16px;

  .label {
    width: 400px;

    .label-item {
      .label-item-content {
        width: 100%;
        display: flex;
        margin-bottom: 10px;
        justify-content: space-between;
        position: relative;

        & > div {
          display: flex;
          line-height: 30px;

          & > span {
            white-space: nowrap;
          }

          &:nth-child(2) {
            margin-left: 10px;
            flex-grow: 0;
          }

          & > button {
            position: absolute;
            right: 0;
          }
        }
      }

      .error-msg {
        margin-bottom: 10px;
        margin-left: 50px;
        color: #e71515;
      }
    }
    :global {
      .acud-btn-has-icon {
        padding-left: 0;
        justify-content: flex-start;
      }
    }
  }
}

.label-view-wrap {
  :global {
    .acud-tag {
      margin-bottom: 8px;
    }
  }
}

.create-instance-modal {
  :global {
    .acud-form-item-label.acud-form-item-label-left {
      width: 100px;
      margin-right: 8px;
    }
  }
  .ttl-wrap {
    display: flex;
    padding-left: 136px;
    .ttl-wrap-input {
      display: flex;
      align-items: center;
    }
  }
}
