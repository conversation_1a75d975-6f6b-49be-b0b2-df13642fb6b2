@import (reference) '../../../styles/global.less';

.create-instance-wrap {
  background-color: #f5f5f5;
  
  .create-instance-header {
    height: 50px;
    display: flex;
    align-items: center;
    background-color: #fff;
  }
  .create-instance-content-wrap {
    display: flex;
    justify-content: center;
    background-color: #f5f5f5;
    min-height: calc(~'100vh - 50px');
  }
  .create-instance-content {
    min-height: calc(~'100vh - 200px');
    max-height: none;
    height: auto;
    overflow: auto;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 6px;
    margin: 16px;
    padding: 32px 48px;
    width: 1180px;
    
    // 模块容器样式
    .module-container {
      margin-bottom: 40px;
      background-color: #fff;
      
      &:last-child {
        border-bottom: none;
        padding-bottom: 60px; // 为底部按钮留出空间
      }
      
      .module-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 24px;
        color: #151a26;
        background-color: #fff;
        position: relative;
        
        &::before {
          content: "";
          position: absolute;
          left: -8px;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background-color: #2468f2;
          border-radius: 2px;
        }
      }
      
      .module-content {
        padding-left: 8px;
        background-color: #fff;
        
        :global {
          .acud-form-item {
            margin: 0;
            
            .acud-form-item-label {
              font-weight: 400;
              color: #5c5f66;
            }
            
            .acud-form-item-control-input {
              min-height: 32px;
            }
            
            .acud-form-item-required::before {
              color: #ff4d4f;
            }
            
            .acud-form-item-extra {
              color: #84878c;
              font-size: 12px;
              line-height: 20px;
              margin-top: 4px;
            }
          }
        }
      }
    }
    
    // 基本信息模块样式
    .basic-info-module {
      .associate-tip {
        color: #84878c;
        margin-left: 8px;
      }
    }
    
    // 路由规则模块样式
    .route-rules-module {
      .param-row {
        display: flex;
        align-items: center;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .param-item {
          margin-right: 16px;
          
          &:last-child {
            margin-right: 0;
          }
        }
        
        .delete-icon {
          margin-left: 8px;
          cursor: pointer;
          color: #84878c;
          
          &:hover {
            color: #ff4d4f;
          }
        }
      }
      
      .add-param-button {
        margin-top: 8px;
      }
    }
    
    // 目标服务模块样式
    .target-service-module {
      .service-selection {
        margin-bottom: 16px;
      }
    }
    
    // 认证授权模块样式
    .auth-module {
      .auth-switch {
        display: flex;
        align-items: center;
        
        .switch-label {
          margin-left: 8px;
          color: #5c5f66;
        }
      }
      
      .consumer-transfer {
        margin-top: 16px;
      }
    }
  }
  
  .create-instance-footer {
    background-color: #fff;
    height: 80px;
    bottom: 0;
    align-items: center;
    box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    display: flex;
    left: 0;
    padding: 16px;
    position: fixed;
    left: 0;
    width: 100%;
    z-index: 1000000;
    
    .create-instance-footer-content {
      display: flex;
      justify-content: flex-start;
      margin: 0 auto;
      max-width: 1180px;
      width: 100%;
      
      button{
        margin-right: 16px;
        width: 88px;
        height: 40px;
        font-size: 14px;
        line-height: 22px;
        font-weight: 400;
      }
    }
  }
}

// 认证转移容器
.authTransferContainer {
  display: flex;
  flex-direction: column;
  margin-top: 9px;
}

// 路径选择器样式
.pathSelector {
  width: 88px !important;
  background-color: #F7F7F9 !important;
  border-radius: 3px 0 0 3px !important;
  border-right: none !important;

  
  :global {
    .acud-select-selector {
      background-color: #F7F7F9 !important;
      border-radius: 3px 0 0 3px !important;
      border-right: none !important;
      padding: 0 12px !important;
    }
  }
}

.pathInput {
  width: 360px !important;
} 


  // 未认证标签样式
.unAuthTag {
  background-color: #F7F7F9 !important;
  color: #84868C !important;
  font-size: 10px !important;
  border: none !important;
  padding: 0 4px !important;
  margin-left: 8px;
}

.transferItem {
  display: flex;
  align-items: center;
  width: 100%;
  padding-left: 12px;

  .transferItemTitle {
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.deleteButton{
  padding-left: 0 !important;
  min-width: fit-content !important;
  color: #2468F2 !important;
}