
.consumerListContainer {
  // padding: 20px;
  width: 100%;

  :global {
    .acud-tabs-bar {
      margin-bottom: 16px;
    }
  }

  .operationBar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .refreshButton {
    margin-left: auto;
  }
}

/* 创建消费者抽屉样式 - 修改为与添加服务抽屉一致的样式 */
.formSection {
  margin-bottom: 24px;
  padding-bottom: 8px;
}

.formSectionLast {
  margin-bottom: 0;
  padding-bottom: 0;
}

.sectionTitle {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.formContent {
  padding: 0;
  font-size: 12px;
  color: #5C5F66;
  line-height: 20px;
  font-weight: 400;

  .acud-form-item-label{
   width: 90px !important;
  }
}

.readonlyField {
  margin-left: 8px;
  font-size: 12px;
  color: #151B26;
  line-height: 20px;
  font-weight: 400;
}

/* 穿梭框样式调整 */
.transferContainer {
  :global {
    .acud-transfer-list {
      .acud-transfer-list-content {
        .acud-transfer-list-content-item {
          display: flex;
          align-items: center;

          .acud-checkbox-wrapper {
            margin-right: 8px;
          }
        }
      }
    }
  }
}

/* 覆盖抽屉中Form.Item控制列的flex样式 */
.fixFlexControl {
  :global {
    .acud-col.acud-form-item-control {
      flex: 1 1 0 !important;
    }
  }
}

// 未开启消费者认证标签样式
.unAuthTag {
  background-color: #F7F7F9 !important;
  color: #84868C !important;
  font-size: 10px !important;
  border: none !important;
  padding: 0 4px !important;
  margin-left: 8px;
}

.transferItem {
  display: flex;
  align-items: center;
  width: 100%;
  padding-left: 12px;

  .transferItemTitle {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}