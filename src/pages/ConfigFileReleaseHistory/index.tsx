import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {Button, Search, Table, Tag, Tooltip} from 'acud';
import {useRequest} from 'ahooks';

import {getConfigReleseList} from '@/apis/configManage';
import EllipsisWrap from '@/components/Ellipsis';
import PaginationOfAutoHide from '@/components/PaginationOfAutoHide';
import RefreshButton from '@/components/RefreshButton';

import {backConfirm, deleteConfirm, DiffModal, ViewModal} from './modal';

const ConfigFileReleaseList = () => {
  const {instanceId, configGroupName, namespace} = getQueryParams();
  const [dataSource, setDataSource] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [seacherValue, setSeacherValue] = useState('');
  const [status, setStatus] = useState('');

  const _getConfigFileList = useCallback(
    async (params: any) => {
      return getConfigReleseList({
        instanceId: instanceId,
        pageNo,
        pageSize,
        group: configGroupName,
        namespace,
        keywordType: 'file',
        keyword: seacherValue,
        active: status,
        ...params
      });
    },
    [
      pageNo,
      pageSize,
      seacherValue,
      instanceId,
      configGroupName,
      namespace,
      status
    ]
  );

  const {
    run,
    loading,
    refresh: refreshInstanceList
  } = useRequest(_getConfigFileList, {
    manual: true,
    onSuccess: (res) => {
      setDataSource(res?.result?.configReleases || []);
      setTotal(res?.result?.totalCount || 0);
    }
  });

  useEffect(() => {
    if (configGroupName && namespace) {
      run({});
    }
  }, [configGroupName, namespace]);

  const onConfirmSearch = (value: any) => {
    setSeacherValue(value);
    setPageNo(1);
    run({
      keyword: value,
      pageNo: 1
    });
  };

  /** 监听点击刷新按钮 */
  const onClickRefreshBtn = useCallback(() => {
    refreshInstanceList();
  }, [refreshInstanceList]);

  const columns = useMemo(() => {
    return [
      {
        title: '配置文件名',
        dataIndex: 'file',
        width: 200,
        render: (value: string) => {
          return <EllipsisWrap data={value} width={200} />;
        }
      },
      {
        title: '版本号',
        dataIndex: 'name',
        width: 200,
        className: 'action-cell',
        render: (val, record) => {
          return (
            <ViewModal
              initialValues={record}
              instanceId={instanceId}
              width={200}
            />
          );
        }
      },
      {
        title: '状态',
        dataIndex: 'active',
        key: 'active',
        width: '10%',
        filters: [
          {text: '使用中', value: true},
          {text: '未使用', value: false}
        ],
        filterMultiple: false,
        render: (value: string) => {
          return (
            <Tag
              color="transparent"
              className="table-status"
              style={{paddingLeft: 0}}
              icon={
                <span
                  className={
                    value ? 'circle status-success' : 'circle status-inactive'
                  }
                />
              }
            >
              {value ? '使用中' : '未使用'}
            </Tag>
          );
        }
      },
      {
        title: '发布时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: '20%'
        // width: 140
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '20%',
        className: 'action-cell',
        render(name, record) {
          return (
            <React.Fragment>
              <Button
                type="actiontext"
                disabled={record.active}
                onClick={backConfirm(instanceId, refreshInstanceList, record)}
              >
                回滚
              </Button>
              <DiffModal curVersion={record} instanceId={instanceId} />
              <Button
                type="actiontext"
                onClick={deleteConfirm(instanceId, refreshInstanceList, record)}
              >
                删除
              </Button>
            </React.Fragment>
          );
        }
      }
    ];
  }, [instanceId, refreshInstanceList, dataSource]);

  const onChange = (pag, filter) => {
    const {active} = filter;
    setStatus(active?.[0]);
    run({
      active: active?.[0]
    });
  };

  return (
    <div className="mse-detail-content-wrap">
      <div className="mse-detail-content-title">发布历史版本</div>
      <div className="mse-detail-operation-container">
        <div className="mse-detail-operation-left">
          <div className="mse-detail-operation-total">
            共有{total}条配置发布历史版本
          </div>
        </div>
        <div className="mse-detail-operation-right">
          <Search
            placeholder="请输入配置文件名搜索"
            style={{width: 300}}
            onSearch={onConfirmSearch}
          />
          <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
        </div>
      </div>
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="index"
        onChange={onChange}
        loading={{
          loading: loading,
          size: 'small'
        }}
        pagination={false}
      />

      <div className="paginationContainer">
        <PaginationOfAutoHide
          showSizeChanger={true}
          showQuickJumper={true}
          current={pageNo}
          pageSize={pageSize}
          total={total}
          onChange={(page, pageSize) => {
            setPageNo(page);
            setPageSize(pageSize!);
            run({pageNo: page, pageSize});
          }}
        />
      </div>
    </div>
  );
};

export default ConfigFileReleaseList;
