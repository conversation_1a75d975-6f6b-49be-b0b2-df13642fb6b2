# 编辑IP黑白名单接口

## 接口基本信息

- **接口说明**：编辑指定的IP黑白名单规则
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/ipRestriction/{id}`
- **请求方式**：PUT
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| id | String | 是 | IP黑白名单规则ID | ip-restriction-001 |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 请求体

```json
{
    "name": "internal-whitelist",
    "enabled": false,
    "description": "更新后的内网IP白名单规则",
    "type": "whitelist",
    "scope": "global",
    "ipAddresses": [
        "***********/24",
        "10.0.0.0/8",
        "**********/12"
    ]
}
```

### 请求体字段说明

| 字段名 | 类型 | 是否必填 | 描述 | 示例值 | 限制 |
| --- | --- | --- | --- | --- | --- |
| name | String | 是 | 名称 | internal-whitelist | 长度为2-64个字符，可包含字母、数字、下划线(_)和连字符(-) |
| enabled | Boolean | 是 | 是否开启 | false | true: 开启, false: 关闭 |
| description | String | 否 | 备注 | 更新后的内网IP白名单规则 | 最大长度200个字符 |
| type | String | 是 | 类型 | whitelist | 可选值：whitelist(白名单)、blacklist(黑名单) |
| scope | String | 否 | 生效粒度 | global | 默认值为global（全局生效） |
| ipAddresses | Array[String] | 是 | IP地址/地址段 | ["***********/24", "10.0.0.0/8"] | 支持单个IP地址或CIDR格式的网段，数组长度不超过100 |

**注意：** 名称(name)字段不可编辑，创建后无法修改。

## 响应参数

### 成功响应

```json
{
    "success": true,
    "status": 200,
    "data": {
        "id": "ip-restriction-001",
        "enabled": false,
        "name": "internal-whitelist",
        "description": "更新后的内网IP白名单规则",
        "type": "whitelist",
        "scope": "global",
        "ipAddresses": [
            "***********/24",
            "10.0.0.0/8",
            "**********/12"
        ],
        "createTime": "2023-06-15 10:30:00",
        "updateTime": "2023-06-15 16:45:20"
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| data | Object | 更新后的IP黑白名单信息 |
| data.id | String | IP黑白名单规则ID |
| data.enabled | Boolean | 是否开启 |
| data.name | String | 名称（不可编辑） |
| data.description | String | 备注 |
| data.type | String | 类型，whitelist表示白名单，blacklist表示黑名单 |
| data.scope | String | 生效粒度 |
| data.ipAddresses | Array[String] | IP地址/地址段列表 |
| data.createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| data.updateTime | String | 更新时间，格式：YYYY-MM-DD HH:mm:ss |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | IP黑白名单不存在 | 确认规则ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
PUT /api/aigw/v1/aigateway/gw-ist9vvin/ipRestriction/ip-restriction-001
Content-Type: application/json
X-Region: gz
```

```json
{
    "enabled": false,
    "description": "更新后的内网IP白名单规则",
    "type": "whitelist",
    "scope": "global",
    "ipAddresses": [
        "***********/24",
        "10.0.0.0/8",
        "**********/12"
    ]
}
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "data": {
        "id": "ip-restriction-001",
        "enabled": false,
        "name": "internal-whitelist",
        "description": "更新后的内网IP白名单规则",
        "type": "whitelist",
        "scope": "global",
        "ipAddresses": [
            "***********/24",
            "10.0.0.0/8",
            "**********/12"
        ],
        "createTime": "2023-06-15 10:30:00",
        "updateTime": "2023-06-15 16:45:20"
    }
}
``` 