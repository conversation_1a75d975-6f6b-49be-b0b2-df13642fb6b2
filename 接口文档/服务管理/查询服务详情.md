# 根据实例ID和服务名称查询服务详情（查询服务详情）

## 接口基本信息

- **接口说明**：根据实例ID和服务名称查询服务详情
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/{serviceName}/service`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| serviceName | String | 是 | 服务名称 | mnist-inference |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": {
        "clusterId": "cce-qhjz40ds",
        "namespace": "default",
        "routeCount": 3,
        "serviceSource": "CCE"
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 服务详情对象 |
| result.clusterId | String | 集群ID |
| result.namespace | String | 命名空间名称 |
| result.routeCount | Integer | 已关联路由数量 |
| result.serviceSource | String | 服务来源，如"CCE"（容器引擎） |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有查询服务详情的权限 |
| 404 | 实例或服务不存在 | 确认实例ID和服务名称是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/gw-ist9vvin/mnist-inference/service
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "clusterId": "cce-qhjz40ds",
        "namespace": "ai-services",
        "routeCount": 3,
        "serviceSource": "CCE"
    }
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 服务名称区分大小写，需要精确匹配
3. 当前仅支持容器引擎（CCE）类型的服务来源详情
4. 如果指定的服务不存在，将返回404错误
5. 服务来源字段表示服务的来源类型，目前仅支持"CCE"（容器引擎）
6. 用户需要具有对应实例和服务的查询权限才能获取服务详情 