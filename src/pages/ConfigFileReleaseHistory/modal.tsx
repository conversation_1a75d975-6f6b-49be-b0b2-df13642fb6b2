import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  Button,
  Drawer,
  Form,
  Loading,
  Modal,
  Radio,
  Select,
  Tag,
  toast,
  Tooltip
} from 'acud';

import {
  backConfigRelese,
  deleteConfigRelese,
  getConfigReleseDetail,
  getConfigReleseList
} from '@/apis/configManage';
import MSETagEditPanel from '@/components/ConfigTag';
import {
  MonacoEditorComponent,
  MonacoEditorDiffComponent
} from '@/components/MonacoEditorComponent';
import {FormatOptions} from '@/utils/enums';

import styles from './index.module.less';

export const deleteConfirm =
  (instanceId: string, refresh: () => void, record: any) => () => {
    const {name, file, group, namespace} = record;
    const onOk = () => {
      return deleteConfigRelese({
        instanceId: instanceId,
        name,
        file,
        group,
        namespace
      })
        .then((res) => {
          if (res?.success) {
            toast.success({
              message: '历史版本删除成功',
              duration: 5
            });
            refresh();
          } else {
            toast.error({
              message: '删除失败，请稍后重试',
              duration: 5
            });
          }
        })
        .catch(() =>
          toast.error({
            message: '删除失败，请稍后重试',
            duration: 5
          })
        );
    };

    Modal.confirm({
      title: '删除此历史版本',
      content: (
        <>
          确定删除
          <span className="mse-font-weight">
            配置文件&nbsp;
            {record.file}&nbsp;的历史版本（版本号：{record.name}）
          </span>
          吗？删除后此历史版本将被永久删除，无法恢复和访问。若当前历史版本正在使用中，可能导致服务出现故障，请谨慎操作
        </>
      ),
      onOk
    });
  };

export const backConfirm =
  (instanceId: string, refresh: () => void, record: any) => () => {
    const {name, file, group, namespace} = record;
    const onOk = () => {
      return backConfigRelese({
        instanceId: instanceId,
        name,
        file,
        group,
        namespace
      })
        .then((res) => {
          if (res?.success) {
            toast.success({
              message: '配置回滚成功',
              duration: 5
            });
            refresh();
          } else {
            toast.error({
              message: '回滚失败，请稍后重试',
              duration: 5
            });
          }
        })
        .catch(() =>
          toast.error({
            message: '回滚失败，请稍后重试',
            duration: 5
          })
        );
    };

    Modal.confirm({
      title: '配置回滚',
      content: (
        <>
          确定将&nbsp;{' '}
          <span className="mse-font-weight">配置文件&nbsp;{record.file}</span>
          &nbsp; 回滚至 &nbsp;
          <span className="mse-font-weight">版本号为&nbsp;{record.name}</span>
          &nbsp;
          的历史版本吗？回滚后此版本将成为使用中版本，其他历史版本仍将保留，若您需要删除历史版本可点击删除进行永久删除。
        </>
      ),
      onOk
    });
  };

interface ViewModalProps {
  initialValues: any;
  instanceId: string;
  width?: number;
}

export const ViewModal: FC<ViewModalProps> = ({
  initialValues,
  instanceId,
  width
}) => {
  const editorRef = useRef<any>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [formDataInstante] = Form.useForm();
  const [curValue, setCurValue] = useState('');
  const [curVersionDetail, setCurVersionDetail] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const divRef = useRef<any>(null);

  // 获取版本详情
  const getVersionDetail = useCallback(
    (name, set) => {
      return getConfigReleseDetail({
        instanceId: instanceId,
        group: initialValues.group,
        namespace: initialValues.namespace,
        file: initialValues.file,
        name: initialValues.name
      })
        .then((res: any) => {
          const detail = res?.result;
          set(detail);
          setCurValue(detail?.content);
          formDataInstante.setFieldsValue({
            ...detail,
            tags: (detail?.tags || []).map((d) => {
              return {
                label: d.key,
                value: d.value
              };
            })
          });
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [initialValues]
  );

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    setLoading(true);

    getVersionDetail(initialValues?.name, setCurVersionDetail);
  }, [initialValues]);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
    formDataInstante.resetFields();
    setCurValue('');
  }, [formDataInstante]);

  const creatRules = useMemo(() => {
    return {
      name: [
        {
          required: true,
          message: '请输入配置文件名'
        },
        {
          max: 128,
          message: '不可超过128个字符'
        },
        {
          validator: (rule: any, value: string) => {
            if (value && !/^[a-zA-Z0-9._-]+$/.test(value)) {
              return Promise.reject(new Error('配置文件名称不符合规则'));
            }
            return Promise.resolve();
          }
        }
      ],
      comment: [
        {
          max: 200,
          message: '不可超过200个字符'
        }
      ]
    };
  }, []);

  const showTip = () => {
    return divRef?.current?.scrollWidth > divRef?.current?.clientWidth;
  };

  return (
    <>
      <Tooltip title={showTip() ? initialValues?.name : ''}>
        <a
          ref={divRef}
          style={{
            width: width
          }}
          onClick={showModal}
          className={styles['eddipsis-wrap']}
        >
          {initialValues?.name || '-'}
        </a>
      </Tooltip>

      <Drawer
        title="历史版本详情"
        onClose={handleCancel}
        visible={isModalVisible}
        width={800}
        className="mse-detail-drawer"
      >
        <Loading size="small" loading={loading}>
          <Form
            labelAlign="left"
            form={formDataInstante}
            labelWidth="84px"
            initialValues={curVersionDetail}
          >
            <Form.Item label="命名空间">
              <span>{curVersionDetail.namespace}</span>
            </Form.Item>
            <Form.Item label="配置分组">
              <span>{curVersionDetail.group}</span>
            </Form.Item>
            <Form.Item label="配置文件名">
              <span>{curVersionDetail.file}</span>
            </Form.Item>
            <Form.Item label="版本号">
              <span>{curVersionDetail.name}</span>
            </Form.Item>
            <Form.Item label="版本状态">
              <Tag
                color="transparent"
                className="table-status"
                style={{paddingLeft: 0}}
                icon={
                  <span
                    className={
                      curVersionDetail?.active
                        ? 'circle status-success'
                        : 'circle status-inactive'
                    }
                  />
                }
              >
                {curVersionDetail?.active ? '使用中' : '未使用'}
              </Tag>
            </Form.Item>
            <Form.Item name="format" label="配置格式" inputMaxWidth={800}>
              <Radio.Group
                options={FormatOptions}
                disabled={true}
              ></Radio.Group>
            </Form.Item>
            <Form.Item name="content" label="配置内容" inputMaxWidth={800}>
              <MonacoEditorComponent
                language={curVersionDetail.format}
                width="560px"
                height="250px"
                value={curValue}
                onMount={handleEditorDidMount}
                options={{
                  scrollBeyondLastLine: false,
                  readOnly: true,
                  wordWrap: 'on',
                  domReadOnly: true,
                  lineNumbers: 'on',
                  contextmenu: false
                }}
              />
            </Form.Item>
            <Form.Item label="配置文件标签" name="tags">
              <MSETagEditPanel
                isView={true}
                defaultTags={curVersionDetail?.tags}
              />
            </Form.Item>
            <Form.Item rules={creatRules.comment} name="comment" label="备注">
              <span>{curVersionDetail.comment || '-'}</span>
            </Form.Item>
            <Form.Item label="发布人">
              <span>{curVersionDetail.createBy}</span>
            </Form.Item>
            <Form.Item label="发布时间">
              <span>{curVersionDetail.createTime}</span>
            </Form.Item>
          </Form>
        </Loading>
      </Drawer>
    </>
  );
};

interface DiffModalProps {
  curVersion: any;
  instanceId: string;
}

export const DiffModal: FC<DiffModalProps> = ({curVersion, instanceId}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [diffVersion, setDiffVersion] = useState('');
  const [diffVersionDetail, setDiffVersionDetail] = useState<any>({});
  const [versionList, setVersionList] = useState<any>([]);
  const [curVersionDetail, setCurVersionDetail] = useState<any>({});
  const [loading, setLoading] = useState(false);

  // 获取版本列表
  const getList = useCallback(() => {
    return getConfigReleseList({
      instanceId: instanceId,
      pageNo: 1,
      pageSize: 1000,
      group: curVersion.group,
      namespace: curVersion.namespace,
      file: curVersion.file
    }).then((res: any) => {
      setVersionList(res?.result?.configReleases || []);
    });
  }, [curVersion]);

  // 获取版本详情
  const getVersionDetail = useCallback(
    (name, set, closeLoading) => {
      return getConfigReleseDetail({
        instanceId: instanceId,
        group: curVersion.group,
        namespace: curVersion.namespace,
        file: curVersion.file,
        name
      })
        .then((res: any) => {
          set(res?.result);
        })
        .finally(() => {
          if (closeLoading) {
            setLoading(false);
          }
        });
    },
    [curVersion]
  );

  useEffect(() => {
    if (curVersion?.name && isModalVisible) {
      getVersionDetail(curVersion?.name, setCurVersionDetail, false);
    }
  }, [curVersion, isModalVisible]);

  useEffect(() => {
    if (diffVersion && isModalVisible) {
      setLoading(true);
      getVersionDetail(diffVersion, setDiffVersionDetail, true);
    }
  }, [diffVersion, isModalVisible]);

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    setLoading(true);
    getList();
  }, [getList]);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
    setVersionList([]);
    setDiffVersion('');
    setDiffVersionDetail({});
  }, []);

  const options = useMemo(() => {
    const list = versionList.filter((d) => d.file === curVersion.file);
    return list.map((d) => {
      if (d.active) {
        return {
          label: `${d.name}（当前使用版本）`,
          value: d.name
        };
      }
      return {
        label: d.name,
        value: d.name
      };
    });
  }, [curVersion, versionList]);

  const changeVersion = useCallback(
    (val) => {
      setDiffVersion(val);
    },
    [versionList, curVersion]
  );

  useEffect(() => {
    const list = versionList.filter((d) => d.file === curVersion.file);
    const diffv = list.find((d) => d.active);
    if (diffv) {
      setDiffVersion(diffv.name);
    } else {
      setDiffVersion(versionList[0]?.name);
    }
  }, [versionList, curVersion]);

  return (
    <>
      <Button type="actiontext" onClick={showModal}>
        版本对比
      </Button>

      <Modal
        title="历史版本对比"
        maskClosable={false}
        onOk={handleCancel}
        onCancel={handleCancel}
        visible={isModalVisible}
        width={1000}
        className={styles['mse-create-config-diff-modal']}
      >
        <div className={styles['mse-create-config-diff-modal-des']}>
          当前配置文件共有{options.length}个历史发布版本
        </div>
        <Loading size="small" loading={loading}>
          <div className={styles['mse-release-diff-editor-wrap']}>
            <div className={styles['diff-top-wrap']}>
              <div className={styles['diff-top-item']}>
                当前选中版本（版本号：{curVersion.name}）
              </div>
              <div className={styles['diff-top-item']}>
                对比版本：
                <Select
                  options={options}
                  value={diffVersion}
                  onChange={changeVersion}
                  style={{maxWidth: 300}}
                />
              </div>
            </div>
            <MonacoEditorDiffComponent
              language={curVersionDetail?.format || 'plaintext'}
              width="100%"
              height="280px"
              oriValue={curVersionDetail?.content}
              value={diffVersionDetail?.content}
              options={{
                scrollBeyondLastLine: false,
                readOnly: true,
                wordWrap: 'on',
                domReadOnly: true,
                lineNumbers: 'on',
                contextmenu: false,
                minimap: {
                  enabled: false
                }
              }}
            />
          </div>
        </Loading>
      </Modal>
    </>
  );
};
