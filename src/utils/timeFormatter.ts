/**
 * 时间格式化工具函数
 * 用于图表X轴时间标签的智能格式化
 */

/**
 * 判断两个时间戳是否在同一天
 * @param startTime 开始时间戳（毫秒）
 * @param endTime 结束时间戳（毫秒）
 * @returns 是否在同一天
 */
export const isSameDay = (startTime: number, endTime: number): boolean => {
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);
  
  return (
    startDate.getFullYear() === endDate.getFullYear() &&
    startDate.getMonth() === endDate.getMonth() &&
    startDate.getDate() === endDate.getDate()
  );
};

/**
 * 智能格式化X轴时间标签
 * @param value 时间戳值（毫秒）
 * @param timeRange 时间范围对象
 * @returns 格式化后的时间字符串
 */
export const formatXAxisTime = (value: number, timeRange: { start: number; end: number }): string => {
  const date = new Date(value);
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');

  // 计算时间范围（小时）
  const timeRangeDuration = timeRange.end - timeRange.start;
  const hours = timeRangeDuration / (1000 * 60 * 60);

  // 判断是否在同一天内
  const sameDay = isSameDay(timeRange.start, timeRange.end);

  // 如果时间范围在同一天内，只显示时间部分
  if (sameDay) {
    return `${hour}:${minute}`;
  }

  // 如果跨越多天，根据时间范围长度决定显示格式
  if (hours <= 1) {
    return `${hour}:${minute}`;
  } else if (hours <= 24) {
    return `${month}-${day} ${hour}:${minute}`;
  } else {
    return `${month}-${day}`;
  }
};

/**
 * 计算固定的时间刻度点
 * @param timeRange 时间范围对象
 * @returns 固定的时间刻度点数组
 */
export const generateFixedTimeTicksArray = (timeRange: { start: number; end: number }): number[] => {
  const timeRangeDuration = timeRange.end - timeRange.start;
  const hours = timeRangeDuration / (1000 * 60 * 60);
  const sameDay = isSameDay(timeRange.start, timeRange.end);

  let interval: number;
  let tickCount: number;

  // 根据时间范围确定刻度间隔和数量
  if (sameDay) {
    if (hours <= 1) {
      interval = 10 * 60 * 1000; // 10分钟
      tickCount = 7; // 包含起始和结束点
    } else if (hours <= 6) {
      interval = 60 * 60 * 1000; // 1小时
      tickCount = 7;
    } else if (hours <= 12) {
      interval = 2 * 60 * 60 * 1000; // 2小时
      tickCount = 7;
    } else {
      interval = 4 * 60 * 60 * 1000; // 4小时
      tickCount = 7;
    }
  } else {
    if (hours <= 1) {
      interval = 10 * 60 * 1000; // 10分钟
      tickCount = 7;
    } else if (hours <= 6) {
      interval = 60 * 60 * 1000; // 1小时
      tickCount = 7;
    } else if (hours <= 24) {
      interval = 4 * 60 * 60 * 1000; // 4小时
      tickCount = 7;
    } else if (hours <= 168) { // 7天
      interval = 24 * 60 * 60 * 1000; // 1天
      tickCount = Math.min(Math.ceil(hours / 24) + 1, 8);
    } else {
      interval = 7 * 24 * 60 * 60 * 1000; // 1周
      tickCount = Math.min(Math.ceil(hours / (7 * 24)) + 1, 8);
    }
  }

  // 生成固定的刻度点数组
  const ticks: number[] = [];

  // 计算起始刻度点（对齐到间隔边界）
  const startTick = Math.floor(timeRange.start / interval) * interval;

  // 生成刻度点
  for (let i = 0; i < tickCount; i++) {
    const tick = startTick + (i * interval);
    if (tick >= timeRange.start && tick <= timeRange.end) {
      ticks.push(tick);
    }
  }

  // 确保包含起始和结束时间点
  if (ticks.length === 0 || ticks[0] > timeRange.start) {
    ticks.unshift(timeRange.start);
  }
  if (ticks[ticks.length - 1] < timeRange.end) {
    ticks.push(timeRange.end);
  }

  // 限制最大刻度数量
  if (ticks.length > 8) {
    const step = Math.ceil(ticks.length / 8);
    const filteredTicks = ticks.filter((_, index) => index % step === 0);
    // 确保包含最后一个刻度
    if (filteredTicks[filteredTicks.length - 1] !== ticks[ticks.length - 1]) {
      filteredTicks.push(ticks[ticks.length - 1]);
    }
    return filteredTicks;
  }

  return ticks;
};

/**
 * 获取X轴刻度配置（新版本 - 提供固定刻度）
 * @param timeRange 时间范围对象
 * @returns 刻度配置对象
 */
export const getXAxisTickConfig = (timeRange: { start: number; end: number }) => {
  const timeRangeDuration = timeRange.end - timeRange.start;
  const hours = timeRangeDuration / (1000 * 60 * 60);
  const sameDay = isSameDay(timeRange.start, timeRange.end);

  // 生成固定的刻度点
  const fixedTicks = generateFixedTimeTicksArray(timeRange);

  let interval: number;

  // 根据时间范围确定刻度间隔
  if (sameDay) {
    if (hours <= 1) {
      interval = 10 * 60 * 1000; // 10分钟
    } else if (hours <= 6) {
      interval = 60 * 60 * 1000; // 1小时
    } else if (hours <= 12) {
      interval = 2 * 60 * 60 * 1000; // 2小时
    } else {
      interval = 4 * 60 * 60 * 1000; // 4小时
    }
  } else {
    if (hours <= 1) {
      interval = 10 * 60 * 1000; // 10分钟
    } else if (hours <= 6) {
      interval = 60 * 60 * 1000; // 1小时
    } else if (hours <= 24) {
      interval = 4 * 60 * 60 * 1000; // 4小时
    } else if (hours <= 168) { // 7天
      interval = 24 * 60 * 60 * 1000; // 1天
    } else {
      interval = 7 * 24 * 60 * 60 * 1000; // 1周
    }
  }

  return {
    fixedTicks, // 固定的刻度点数组
    interval, // 刻度间隔
    splitNumber: fixedTicks.length - 1, // 分割数量
    minInterval: interval,
    maxInterval: interval
  };
};

/**
 * 验证和清理原始数据（保持数据点的原始时间戳）
 * @param originalData 原始数据数组 [[timestamp, value], ...]
 * @param timeRange 时间范围对象
 * @returns 清理后的数据数组（保持原始时间戳）
 */
export const validateAndCleanData = (
  originalData: [number, number | null][],
  timeRange: { start: number; end: number }
): [number, number | null][] => {
  if (!originalData || originalData.length === 0) {
    return [];
  }

  // 只过滤掉时间范围外的数据点，保持原始时间戳
  return originalData
    .filter(([timestamp]) => timestamp >= timeRange.start && timestamp <= timeRange.end)
    .sort(([a], [b]) => a - b); // 按时间戳排序
};

/**
 * 为多系列数据验证和清理数据
 * @param seriesDataArray 多系列数据数组
 * @param timeRange 时间范围对象
 * @returns 清理后的多系列数据数组
 */
export const validateAndCleanMultiSeriesData = (
  seriesDataArray: Array<{ name: string; data: [number, number | null][]; color?: string }>,
  timeRange: { start: number; end: number }
): Array<{ name: string; data: [number, number | null][]; color?: string }> => {
  return seriesDataArray.map(series => ({
    ...series,
    data: validateAndCleanData(series.data, timeRange)
  }));
};

/**
 * 格式化tooltip中的时间显示（基于数据点的实际时间戳）
 * @param timestamp 数据点的实际时间戳（毫秒）
 * @param timeRange 时间范围对象（可选，用于优化显示格式）
 * @returns 格式化后的时间字符串
 */
export const formatTooltipTime = (timestamp: number, timeRange?: { start: number; end: number }): string => {
  const date = new Date(timestamp);

  // 如果提供了时间范围，根据是否同一天优化显示格式
  if (timeRange) {
    const sameDay = isSameDay(timeRange.start, timeRange.end);

    if (sameDay) {
      // 同一天内，显示：2024年7月29日 09:30:15
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }

  // 默认显示完整时间
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};
