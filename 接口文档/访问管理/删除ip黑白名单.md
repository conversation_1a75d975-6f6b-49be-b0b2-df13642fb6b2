# 删除IP黑白名单接口

## 接口基本信息

- **接口说明**：删除指定的IP黑白名单规则
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/ipRestriction/{id}`
- **请求方式**：DELETE
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| id | String | 是 | IP黑白名单规则ID | ip-restriction-001 |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 成功响应

```json
{
    "success": true,
    "status": 200,
    "message": "IP黑白名单规则删除成功"
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| message | String | 删除结果消息 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | IP黑白名单不存在 | 确认规则ID是否正确 |
| 409 | 规则正在使用中，无法删除 | 请先禁用规则或解除关联后再删除 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
DELETE /api/aigw/v1/aigateway/gw-ist9vvin/ipRestriction/ip-restriction-001
X-Region: gz
```

## 响应示例

### 成功响应

```json
{
    "success": true,
    "status": 200,
    "message": "IP黑白名单规则删除成功"
}
```

### 错误响应示例

```json
{
    "success": false,
    "status": 409,
    "message": "IP黑白名单规则正在使用中，无法删除"
}
```

## 注意事项

1. 删除操作不可恢复，请确认后再执行删除操作
2. 如果规则正在被路由或其他配置使用，需要先解除关联才能删除
3. 建议在删除前先禁用规则，确认业务无影响后再执行删除 