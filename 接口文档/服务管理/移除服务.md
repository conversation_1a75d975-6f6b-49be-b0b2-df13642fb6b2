# 移除服务接口

## 接口基本信息

- **接口说明**：根据实例ID和服务名称移除服务
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}/{serviceName}/{namespace}/service`
- **请求方式**：DELETE
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| serviceName | String | 是 | 服务名称 | mnist-inference |
| namespace | String | 是 | 命名空间 | default |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": null
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Null | 移除服务成功后返回null |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有移除服务的权限 |
| 403 | 禁止操作 | 服务可能正在被使用或有其他限制条件 |
| 404 | 实例或服务不存在 | 确认实例ID和服务名称是否正确 |
| 409 | 资源冲突 | 服务可能正在被其他操作修改，请稍后重试 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
DELETE /api/aigw/v1/aigateway/gw-ist9vvin/mnist-inference/default/service
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": null
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 服务名称区分大小写，需要精确匹配
3. 移除服务操作不可逆，请谨慎操作
4. 如果服务关联了路由，移除服务会同时删除相关的路由配置
5. 服务正在被使用时（如有活跃的API调用）可能无法立即移除
6. 移除服务不会删除在原始集群中的服务实例，只会解除与网关的关联关系
7. 用户需要具有对应实例的管理权限才能移除服务 