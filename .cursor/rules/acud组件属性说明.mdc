---
description: 
globs: 
alwaysApply: true
---


## Button 属性说明

通过设置 Button 的属性来产生不同的按钮样式，推荐顺序为：`type` -> `size` -> `loading` -> `disabled`。
按钮的属性说明如下：
支持原生 button 的其他所有属性。

|属性|说明|类型|默认值|版本|
|---|---|---|---|---|
|block|将按钮宽度调整为其父宽度的选项|boolean|false||
|disabled|按钮失效状态|boolean|false||
|href|点击跳转的地址，指定此属性 button 的行为和 a 链接一致|string|-||
|htmlType|设置 `button` 原生的 `type` 值，可选值请参考 [HTML 标准](mdc:https:/developer.mozilla.org/en-US/docs/Web/HTML/Element/button#attr-type)|string|`button`||
|icon|设置按钮的图标组件|ReactNode|-||
|loading|设置按钮载入状态|boolean \| { delay: number }|false||
|size|设置按钮大小|`large` \| `middle` \| `small`|`middle`||
|target|相当于 a 链接的 target 属性，href 存在时生效|string|-||
|type|设置按钮类型|`primary` \| `text` \| `actiontext` \| `highlight` \| `enhance` \| `default`|`default`||
|onClick|点击按钮时的回调|(event) => void|-||

## 全局化配置 ConfigProvider

为组件提供统一的全局化配置。
ConfigProvider 使用 React 的 [context](mdc:https:/facebook.github.io/react/docs/context.html) 特性，只需在应用外围包裹一次即可全局生效。

import {ConfigProvider} from 'acud'; // ... export default () => ( <ConfigProvider> <App /> </ConfigProvider> );

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|componentSize|设置 acud 组件大小|`small` \| `middle` \| `large`|-||
|getPopupContainer|弹出框（Select, Tooltip, Menu 等等）渲染父节点，默认渲染到 body 上。|function(triggerNode)|() => document.body||
|getTargetContainer|配置 Affix、Anchor 滚动监听容器。|()Element|() => window||
|locale|语言包配置，语言包可到 [acud/lib/locale](mdc:http:/unpkg.com/acud/lib/locale) 目录下寻找|object|-||
|prefixCls|设置统一样式前缀。注意：需要配合 `less` 变量 @acud-prefix|string|`acud`||
|iconPrefixCls|设置图标统一样式前缀。注意：需要配合 `less` 变量 @iconfont-css-prefix|string|`${prefixCls}icon`||
|renderEmpty|自定义组件空状态参考 [空状态](mdc:https:/acud.now.baidu-int.com/components/empty)|function(componentName: string): ReactNode|-||
|form|设置form.keepDisplayExtra控制控制表单中额外提示信息是否一直展示|object|-||

### ConfigProvider.config() [#](mdc:https:/acud.now.baidu-int.com/components/config-provider#ConfigProvider.config\(\))

设置`Modal.method()`的prefix

```
ConfigProvider.config({
  prefixCls: '',
});
```


## Link 属性说明

超链接的属性说明如下：

|属性|说明|类型|默认值|版本|
|---|---|---|---|---|
|type|设置按钮类型|`primary` \| `text` \| `default`|`default`||
|size|设置按钮大小|`medium` \| `small`|`small`||
|disabled|按钮失效状态|boolean|false||
|href|点击跳转的地址，指定此属性 button 的行为和 a 链接一致|string|-||
|icon|设置按钮的图标组件|ReactNode|-||
|target|相当于 a 链接的 target 属性，href 存在时生效|string|-||
|onClick|点击按钮时的回调|(event) => void|-||

支持原生链接标签的其他所有属性。

## Divider 属性说明

|参数|说明|类型|默认值|
|---|---|---|---|
|children|嵌套的标题|ReactNode|-|
|className|分割线样式类|string|-|
|dashed|是否虚线|boolean|false|
|orientation|分割线标题的位置|`left` \| `right` \| `center`|`center`|
|orientationMargin|标题和最近 left/right 边框之间的距离，去除了分割线，同时 `orientation` 必须为 `left` 或 `right`|string \| number|-|
|plain|文字是否显示为普通正文样式|boolean|false|
|style|分割线样式对象|CSSProperties|-|
|type|水平还是垂直类型|`horizontal` \| `vertical`|`horizontal`|

## Menu 属性说明

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|defaultOpenKeys|初始展开的 SubMenu 菜单项 key 数组|string[]|-||
|defaultSelectedKeys|初始选中的菜单项 key 数组|string[]|-||
|expandIcon|自定义展开图标|ReactNode \| `(props: SubMenuProps & { isSubMenu: boolean }) => ReactNode` |-||
|forceSubMenuRender|在子菜单展示之前就渲染进 DOM|boolean|false||
|inlineCollapsed|inline 时菜单是否收起状态|boolean|-||
|inlineIndent|inline 模式的菜单缩进宽度|number|14||
|scope|作用域，global 时，横向模式表示业务 header 导航（可定义 logo、icon 等），纵向模式表示业务侧边栏（有展开收起等操作区），如果为 local，则表示基础菜单|'local'\| 'global'|'local'||
|mode|菜单类型，现在支持纵向模式（一级导航纵向分布，子菜单在右侧弹出展示）、横向模式（一级导航横向分布，子菜单在下方弹出展示）、内嵌模式（特殊的纵向模式，子菜单内嵌在菜单区域）| `vertical` \| `horizontal` \| `inline` | `vertical` ||
|multiple|是否允许多选|boolean|false||
|openKeys|当前展开的 SubMenu 菜单项 key 数组|string[]|-||
|overflowedIndicator|自定义 Menu 折叠时的图标|ReactNode|-||
|selectable|是否允许选中|boolean|true||
|selectedKeys|当前选中的菜单项 key 数组|string[]|-||
|style|根节点样式|CSSProperties|-||
|subMenuCloseDelay|用户鼠标离开子菜单后关闭延时，单位：秒|number|0.1||
|subMenuOpenDelay|用户鼠标进入子菜单后开启延时，单位：秒|number|0||
|triggerSubMenuAction|SubMenu 展开/关闭的触发行为| `hover` \| `click` | `hover` ||
|onClick|点击 MenuItem 调用此函数|function ({ item, key, keyPath, domEvent })|-||
|onDeselect|取消选中时调用，仅在 multiple 生效|function ({ item, key, keyPath, selectedKeys, domEvent })|-||
|onOpenChange|SubMenu 展开/关闭的回调|function (openKeys: string[])|-||
|onSelect|被选中时调用|function ({ item, key, keyPath, selectedKeys, domEvent })|-||
|iconList|右侧的 icon 区，`mode="horizontal"` 且 `scope="local"` 时生效|ReactNode[]|[]|
|disabledOverflow|Menu 内容溢出时是否折叠|boolean|false||

### 业务 header 菜单

mode="horizontal" scope="global" 时，支持配置：

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|logoInfo|菜单图标|{logo: string \| ReactNode; urlConfig: {href: string; target?: string}}|-||
|titleInfo|菜单标题|{title: string \| ReactNode; urlConfig: {href: string; target?: string}}|-||
|headerMenu|菜单导航栏|ReactNode|-||
|viceHeaderMenu|副菜单导航栏|ReactNode|-||
|otherArea|右侧信息栏|ReactNode|-||
|headerStyle|自定义菜单样式|any|-||

mode="inline" scope="global" 时，支持配置：

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|collapsible|是否展示控制展开/收起区域|boolean|true||
|collapsePosition|展开控制区域的位置| `bottom` \| `right` | `bottom` ||
|withHead|业务侧边栏内置的标题|string|-||
|headIcon|业务侧边栏内置标题的 icon|ReactNode|-||
|headTip|业务侧边栏内置标题 hover 时展示的 tip|string|-||
|headPopupClassName|提示内容的类名|string|-|1.4.40|
|collapsedShowFirst|业务侧边栏左侧收起后保留首字母展示|boolean|false||
|onHeadClick|业务侧边栏内置标题的点击回调函数|() => void|-||
|onCollapse|展开/收起时的回调函数|(collapsed) => void|-||

### Menu. MenuHead

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|icon|图标|ReactNode|-||
|title|hover 时展示的标题|ReactNode|-||
|popupClassName|提示内容的类名|string|-|1.4.40|
|onClick|点击 head 时调用此函数|() => {}|-||

### Menu. Item

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|danger|展示错误状态样式|boolean|false||
|disabled|是否禁用|boolean|false||
|icon|菜单图标|ReactNode|-||
|key|item 的唯一标志|string|-||
|title|设置收缩时展示的悬浮标题|string|-||
|tooltipProps|属性参考 [Tooltip](mdc:https:/acud.now.baidu-int.com/components/tooltip/#API) |TooltipProps|-|1.4.41|

### Menu. SubMenu

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|children|子菜单的菜单项|Array<MenuItem \| SubMenu>|-||
|disabled|是否禁用|boolean|false||
|icon|菜单图标|ReactNode|-||
|key|唯一标志|string|-||
|popupClassName|子菜单类名，`mode="inline"` 时无效|string|-||
|popupOffset|子菜单偏移量，`mode="inline"` 时无效|[number, number]|-||
|popupPlacement|子菜单弹出位置| `topLeft` \| `bottomLeft` \| `bottomCenter` \| `bottomRight` \| `leftTop` \| `rightTop` |-||
|title|子菜单项值|ReactNode|-||
|onTitleClick|点击子菜单标题|function ({ key, domEvent })|-||

### Menu. ItemGroup

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|children|分组的菜单项|MenuItem[]|-||
|title|分组标题|ReactNode|-||

### Menu. Divider

菜单项分割线，只用在弹出菜单内。

### Menu. ItemCustom

用来包裹自定义组件，如 Select 等。

| 参数             | 说明                | 类型        | 默认值              | 版本     |
| -------------- | ----------------- | --------- | ---------------- | ------ |
| children       | 自定义组件，如 Select     | ReactNode | -                | 1.4.22 |
| icon           | menu 折叠时显示的 icon    | ReactNode | OutlinedAppstore | 1.4.22 |
| popup          | menu 折叠时 hover 提示的内容 | ReactNode | -                | 1.4.22 |
| popupClassName | 提示内容的类名           | string    | -                | 1.4.22 |

## Steps 属性说明

<Steps> <Step title="第一步" /> <Step title="第二步" /> <Step title="第三步" /> </Steps>

### Steps

整体步骤条。

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|className|步骤条类名|string|-||
|current|指定当前步骤，从 0 开始记数。在子 Step 元素中，可以通过 `status` 属性覆盖状态|number|0||
|direction|指定步骤条方向。目前支持水平（`horizontal`）和竖直（`vertical`）两种方向|string| `horizontal` ||
|initial|起始序号，从 0 开始记数|number|0||
|labelPlacement|指定标签放置位置，默认水平放图标右侧，可选 `vertical` 放图标下方|string| `horizontal` ||
|progressDot|点状步骤条，可以设置为一个 function，labelPlacement 将强制为 `vertical` |boolean \| (iconDot, {index, status, title, description}) => ReactNode|false||
|responsive|当屏幕宽度小于 532 px 时自动变为垂直模式|boolean|-||
|size|指定大小，目前支持普通（`default`）和迷你（`small`）|string| `default` ||
|status|指定当前步骤的状态，可选 `wait` `process` `finish` `error` |string| `process` ||
|type|步骤条类型，有 `default` 和 `navigation` 两种|string| `default` ||
|onChange|点击切换步骤时触发|(current) => void|-||

### Steps. Step

步骤条内的每一个步骤。

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|description|步骤的详情描述，可选|ReactNode|-||
|disabled|禁用点击|balse||
|icon|步骤图标的类型，可选|ReactNode|-||
|status|指定状态。当不配置该属性时，会使用 Steps 的 `current` 来自动指定状态。可选：`wait` `process` `finish` `error`|string|`wait`||
|subTitle|子标题|ReactNode|-||
|title|标题|ReactNode|-||

## Pagination 属性说明

<Pagination onChange={onChange} total={50} />

分页的属性说明如下：

|属性|说明|类型|默认值|版本|
|---|---|---|---|---|
|current|当前页数|number|-||
|defaultCurrent|默认的当前页数|number|1||
|defaultPageSize|默认的每页条数|number|10||
|disabled|禁用分页|boolean|-||
|hideOnSinglePage|只有一页时是否隐藏分页器|boolean|false||
|itemRender|用于自定义页码的结构，可用于优化 SEO|(page, type: 'page' \| 'prev' \| 'next', originalElement) => React.ReactNode|-||
|pageSize|每页条数|number|-||
|pageSizeOptions|指定每页可以显示多少条|string[] \| number[]|[10, 20, 50, 100]|V1.5.16之后支持number|
|responsive|当 size 未指定时，根据屏幕宽度自动调整尺寸|boolean|-||
|showLessItems|是否显示较少页面内容|boolean|false||
|showQuickJumper|是否可以快速跳转至某页|boolean \| { goButton: ReactNode }|false||
|showSizeChanger|是否展示 pageSize 切换器，当 total 大于 50 时默认为 true|boolean|-||
|showTitle|是否显示原生 tooltip 页码提示|boolean|true||
|showTotal|用于显示数据总量和当前数据顺序|function(total, range)|-||
|simple|当添加该属性时，显示为简单分页|boolean \| {readOnly?: boolean}|-||
|total|数据总数|number|0||
|onChange|页码或 pageSize 改变的回调，参数是改变后的页码及每页条数|function(page, pageSize)|-||
|onShowSizeChange|pageSize 变化的回调|function(current, size)|-||



## Tabs 属性说明

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|activeKey|当前激活 tab 面板的 key|string|-||
|addIcon|自定义添加按钮|ReactNode| `+` ||
|animated|是否使用动画切换 Tabs|boolean| `{ inkBar: boolean, tabPane: boolean }` ||
|centered|标签居中展示|boolean|false||
|defaultActiveKey|初始化选中面板的 key，如果没有设置 activeKey|string|第一个面板||
|hideAdd|是否隐藏加号图标，在 type="editable-card" 时有效|boolean|false||
|renderTabBar|替换 TabBar，用于二次封装标签头|(props: DefaultTabBarProps, DefaultTabBar: React. ComponentClass) => React. ReactElement|-||
|tabBarExtraContent|tab bar 上额外的元素|{left?: ReactNode, right?: ReactNode}|true||
|tabBarGutter|tabs 之间的间隙|number|-||
|tabBarStyle|tab bar 的样式对象|object|-||
|type|页签的基本样式，可选 `line、card editable-card` 类型|string| `line` ||
|onChange|切换面板的回调|function (activeKey) {}|-||
|onEdit|新增和删除页签的回调，在 type="editable-card" 时有效|(targetKey, action): void|-||
|onTabClick|tab 被点击的回调|function (key: string, event: MouseEvent)|-||
|onEditTabNav|双击 tabNav 后被编辑的回调（只允许 tab 为 string 的 tabNav 被编辑）|function (panes)| `[{ tab: string, content: string \\| ReactNode, key: string }]` ||

### Tabs. TabPane

| 参数          | 说明                                | 类型                  | 默认值   |
| ----------- | --------------------------------- | ------------------- | ----- |
| closeIcon   | 自定义关闭图标，在 type="editable-card"时有效 | ReactNode           | -     |
| forceRender | 被隐藏时是否渲染 DOM 结构                   | boolean             | false |
| key         | 对应 activeKey                      | string              | -     |
| tab         | 选项卡头显示文字                          | string \| ReactNode | -     |
| max         | 选项卡头显示最多显示文字字数，tab 为 string 时有效      | number              | -     |
| disabled    | 选项卡头不可点                           | boolean             | false |

## Select 属性说明

### Select props

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|allowClear|支持清除|boolean|false||
|autoClearSearchValue|是否在选中项后清空搜索框，只在 `mode` 为 `multiple` |boolean|true||
|showAllSelected|是否在全选后只显示一个『全选』tag，只在 `mode` 为 `multiple` 生效|boolean|false||
|autoFocus|默认获取焦点|boolean|false||
|bordered|是否有边框|boolean|true||
|clearIcon|自定义的多选框清空图标|ReactNode|-||
|defaultActiveFirstOption|是否默认高亮第一个选项|boolean|true||
|defaultOpen|是否默认展开下拉菜单|boolean|-||
|defaultValue|指定默认选中的条目|string \| string[]  <br>number \| number[]  <br>LabeledValue \| LabeledValue[]|-||
|disabled|是否禁用|boolean|false||
|dropdownClassName|下拉菜单的 className 属性|string|-||
|dropdownMatchSelectWidth|下拉菜单是否和选择器同宽，值为 false 时会关闭虚拟滚动。如果设置为 number，当值小于选择框宽度时会被忽略。|boolean \| number|true||
|dropdownRender|自定义下拉框内容|(originNode: ReactNode) => ReactNode|-||
|dropdownStyle|下拉菜单的 style 属性|CSSProperties|-||
|filterOption|是否根据输入项进行筛选。当其为一个函数时，会接收 `inputValue` `option` 两个参数，当 `option` 符合筛选条件时，应返回 true，反之则返回 false|boolean \| function (inputValue, option)|true||
|optionRender|自定义 option 渲染|(label: ReactNode, option) => ReactNode|-|1.1.21|
|filterSort|搜索时对筛选结果项的排序函数, 类似 [Array.sort](mdc:https:/developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort) 里的 compareFunction|(optionA: Option, optionB: Option) => number|-||
|getPopupContainer|菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位。|function (triggerNode)|() => document. body||
|labelInValue|是否把每个选项的 label 包装到 value 中，会把 Select 的 value 类型从 `string` 变为 { value: string, label: ReactNode } 的格式|boolean|false||
|listHeight|设置弹窗滚动高度|number|256||
|loading|加载中状态|boolean|false||
|maxTagCount|最多显示多少个 tag，响应式模式会对性能产生损耗|number \| `responsive` |-||
|maxTagPlaceholder|隐藏 tag 时显示的内容|ReactNode \| function (omittedValues)|-||
|maxTagTextLength|最大显示的 tag 文本长度|number|-||
|menuItemSelectedIcon|自定义多选时当前选中的条目图标|ReactNode|-||
|mode|设置 Select 的模式为多选 (默认单选)| `multiple` |-||
|notFoundContent|当下拉列表为空时显示的内容|ReactNode| `Not Found` ||
|open|是否展开下拉菜单|boolean|-||
|optionFilterProp|搜索时过滤对应的 `option` 属性，如设置为 `children` 表示对内嵌内容进行搜索。若通过 `options` 属性配置选项内容，建议设置 `optionFilterProp="label"` 来对内容进行搜索。|string| `value` ||
|optionLabelProp|回填到选择框的 Option 的属性值，默认是 Option 的子元素。比如在子元素需要高亮效果时，此值可以设为 `value` |string| `children` ||
|options|数据化配置选项内容，相比 jsx 定义会获得更好的渲染性能|{label, value}[]|-||
|placeholder|选择框默认文本|string|请选择||
|placement|选择框弹出位置| `bottomLeft` \| `bottomRight` \| `topLeft` \| `topRight` |bottomLeft|1.2.12|
|popupOverflow|弹层展示逻辑，默认为可视区域滚动，可配置成滚动区域滚动| `viewport` \| `scroll` |viewport|1.2.14|
|removeIcon|自定义的多选框清除图标|ReactNode|-||
|searchValue|控制搜索文本|string|-||
|showArrow|是否显示下拉小箭头|boolean|true||
|showSearch|使单选模式可搜索|boolean|false||
|showTitle|是否展示 option title|boolean|false||
|showSelectAll|是否展示全选项，多选时有效|boolean|true|1.1.8|
|suffixIcon|自定义的选择框后缀图标|ReactNode|-||
|groupSelectorRender|自定义分组选择器选中项的 label|(groupLable, value) => ReactNode|-||
|tagRender|自定义 tag 内容 render|(props) => ReactNode|-||
|tokenSeparators|在 `tags` 和 `multiple` 模式下自动分词的分隔符|string[]|-||
|value|指定当前选中的条目|string \| string[]  <br>number \| number[]  <br>LabeledValue \| LabeledValue[]|-||
|virtual|设置 false 时关闭虚拟滚动（可折叠分组模式下，始终关闭虚拟滚动）|boolean|true||
|keepExpand|分组始终展开，并且不可操作，只有单选时生效|boolean|false||
|defaultExpandGroupKey|默认展开的分组 key, 为 true 时全部展开，为 false 时全部收起，也可以指定展开组的 key（如果没有设置 key，则为该分组的 index 下标字符串）|string[] \| string \| number[] \| number \| boolean|false||
|onBlur|失去焦点时回调|function|-||
|onChange|选中 option，或 input 的 value 变化时，调用此函数|function (value, option:Option \| Array<Option>)|-||
|onClear|清除内容时回调|function|-||
|onDeselect|取消选中时调用，参数为选中项的 value (或 key) 值，仅在 `multiple` 或 `tags` 模式下生效|function (string \| number \| LabeledValue)|-||
|onDropdownVisibleChange|展开下拉菜单的回调|function (open)|-||
|onFocus|获得焦点时回调|function|-||
|onInputKeyDown|按键按下时回调|function|-||
|onMouseEnter|鼠标移入时回调|function|-||
|onMouseLeave|鼠标移出时回调|function|-||
|onPopupScroll|下拉列表滚动时的回调|function|-||
|onSearch|文本框值变化时回调|function (value: string)|-||
|onSelect|被选中时调用，参数为选中项的 value (或 key) 值|function (string \| number \| LabeledValue, option: Option)|-||
|onExpandChange|分组展开收起变化时触发，参数为所有展开项的 key|function (key: (string \| number)[])|-|1.1.9|

> 注意，如果发现下拉菜单跟随页面滚动，或者需要在其他弹层中触发 Select，请尝试使用 `getPopupContainer={triggerNode => triggerNode.parentElement}` 将下拉弹层渲染节点固定在触发器的父元素中。

### Select Methods

|名称|说明|版本|
|---|---|---|
|blur ()|取消焦点||
|focus ()|获取焦点||

### Option props

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|className|Option 器类名|string|-||
|disabled|是否禁用|boolean|false||
|title|选中该 Option 后，Select 的 title|string|-||
|value|默认根据此属性值进行筛选|string \| number|-||
|extra|额外的元素|ReactNode|-||

### OptGroup props

| 参数    | 说明  | 类型                      | 默认值 | 版本  |
| ----- | --- | ----------------------- | --- | --- |
| key   | Key | string                  | -   |     |
| label | 组名  | string \| React. Element | -   |     |

## Search 属性说明

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|multipleOption|多条件搜索|LabeledValue[]|[]||
|multipleValue|多条件搜索值|string ｜ number|-||
|multipleDefaultValue|多条件搜索默认值|string ｜ number|-||
|addonSelectStyle|设置条件选择器的样式|React.CSSProperties|-|1.6.3|
|onChangeMultiple|多条件搜索change|(e: string ｜ number): void|-||
|onSearch|点击搜索图标、清除图标，或按下回车键时的回调|(e: any): void|-||

## Checkbox 属性说明

### Checkbox

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|autoFocus|自动获取焦点|boolean|false||
|checked|指定当前是否选中|boolean|false||
|defaultChecked|初始是否选中|boolean|false||
|disabled|失效状态|boolean|false||
|indeterminate|设置 indeterminate 状态，只负责样式控制|boolean|false||
|onChange|变化时回调函数|function (e:Event)|-||

### Checkbox Group

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|defaultValue|默认选中的选项|string[]|[]||
|disabled|整组失效|boolean|false||
|name|CheckboxGroup 下所有 `input[type="checkbox"]` 的 `name` 属性|string|-||
|options|指定可选项|string[] \| Option[]|[]||
|value|指定选中的选项|string[]|[]||
|onChange|变化时回调函数|function (checkedValue)|-||
|optionType|用于设置 Checkbox options 类型|default \| button|default||

#### Option

```
interface Option {
    label: string;
    value: string;
    disabled?: boolean;
}
```

### 方法

#### Checkbox

| 名称      | 描述   | 版本  |
| ------- | ---- | --- |
| blur ()  | 移除焦点 |     |
| focus () | 获取焦点 |     |

## Input 属性说明

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|prefixCls|设置 class 前缀|string| `acud-input` |-|
|size|控件大小| `large` \| `middle` \| `small` | `middle` ||
|placeholder|占位字符|string|-|-|
|className|设置样式名|string|-|-|
|style|设置样式|React. CSSProperties|-|-|
|warning|带警示的文本框|boolean|false|-|
|warningPopover|带警示的文本框，以 popover 形式展示与 warning 设置一个即可|boolean|false|-|
|warningText|警示文本框的警示文字|string|-|-|
|tips|带提示的文本框如果有 warningText 则不展示提示|string|-|-|
|disabled|是否禁用|boolean|false|-|
|readOnly|是否只读|boolean|false|-|
|value|文本框中的值|string|-|-|
|defaultValue|文本框默认值|string|-|-|
|limitLength|文本框限制字符数|number|-|-|
|label|文本框标签|ReactNode|-|-|
|prefix|带有前缀图标的 input|ReactNode|-||
|suffixput|ReactNode|-||
|addonBefore|带标签的 input，设置前置标签|ReactNode|-||
|addonAfter|带标签的 input，设置后置标签|ReactNode|-||
|allowClear|是否允许一键清除|boolean|false|-|
|onChange|输入框内容变化时的回调|function (e)|-||
|onBlur|输入框失去焦点回调|function (e)|-|-|
|onFocus|输入框获得焦点回调|function (e)|-|-|
|onPressEnter|按下回车的回调|function (e)|-||
|onKeyDown|按下键盘回调|function (e)|-|-|
|onPaste|粘贴数据回调|function (e)|-|-|
|handleClearAll|一键清除后的回调|function ()|-|-|
|forbidIfLimit|设置 limitLength 时生效，达到限定字符长度后是否截断|boolean|false|-|

### Input Label

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|label|默认标签|string[]|[]||
|disabled|是否禁用|boolean|false||
|allowClear|是否支持全部清除|boolean|false||
|onPressEnter|按下回车的回调|function (value)|-||
|onLabelChange|受控组件 lable change 回调，清除一个也回触发该方法|function (e)|-||

### Input Password

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|prefixCls|设置 class 前缀|string| `acud-input-password` |-|
|className|设置样式名|string|-|-|
|style|设置样式|React. CSSProperties|-|-|
|disabled|是否禁用|boolean|false|-|
|visibilityToggle|切换图标是否可见|boolean|true|-|
|onPressEnter|按下回车的回调|function (e)|-||
|value|文本框中的值|string|-|-|
|defaultValue|文本框默认值|string|-|-|
|placeholder|占位字符|string| `请输入密码` |-|

### Input TextArea

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|prefixCls|设置 class 前缀|string| `acud-input-textarea` |-|
|placeholder|占位字符|string|-|-|
|className|设置样式名|string|-|-|
|style|设置样式|React. CSSProperties|-|-|
|width|设置宽度|number|-|-|
|readonly|是否只读|boolean|false|-|
|allowClear|是否支持全部清除, 无内容不显示|boolean|true|-|
|onPressEnter|按下回车的回调|function (e)|-||
|defaultValue|文本框默认值|string|-|-|
|style|样式|React. CSSProperties|-|-|
|autoSize|自适应内容高度，可设置为 true / false 或对象：{ minRows: 2, maxRows: 6 }| `boolean` \| `object` |-||
|limitLength|限制长度|number|-|-|
|onChange|输入框内容变化时的回调|function (e)|-||
|onPressEnter|按下回车的回调|function (e)|-||
|onBlur|输入框失去焦点回调|function (e)|-|-|
|onFocus|输入框获得焦点回调|function (e)|-|-|
|value|文本框中的值|string|-|-|
|defaultValue|文本框默认值|string|-|-|
|forbidIfLimit|设置 limitLength 时生效，达到限定字符长度后是否截断|boolean|false|-|
|disabled|是否禁用|boolean|false||

### Input AutoComplete [#](mdc:https:/acud.now.baidu-int.com/components/input#Input-AutoComplete)

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|allowClear|支持清除|boolean|`true`|-|
|autoFocus|自动获取焦点|boolean|-|-|
|backfill|使用键盘选择选项的时候把选中项回填到输入框中|boolean|-|-|
|children（自动完成的数据源）|自动完成的数据源|`React.ReactElement<OptionProps> \| Array<React.ReactElement<OptionProps>>`|-|-|
|children（自定义输入框）|自定义输入框|`HTMLInputElement \| HTMLTextAreaElement \| React.ReactElement<InputProps>`|`<Input />`|-|
|defaultActiveFirstOption|是否默认高亮第一个选项|boolean|true||
|defaultOpen|指定默认选中的条目|boolean|-|-|
|defaultValue|样式|string|-|-|
|disabled|是否禁用|boolean|false||
|dropdownClassName|下拉菜单的 className 属性|string|-|-|
|dropdownMatchSelectWidth|下拉菜单和选择器同宽。默认将设置 min-width，当值小于选择框宽度时会被忽略。false 时会关闭虚拟滚动|`boolean \| number`|true||
|filterOption|是否根据输入项进行筛选。当其为一个函数时，会接收 inputValue option 两个参数，当 option 符合筛选条件时，应返回 true，反之则返回 false|boolean|function(inputValue, option)|true||
|getPopupContainer|菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位。|function(triggerNode)|() => document.body|-|
|notFoundContent|notFoundContent|ReactNode|-|-|
|open|是否展开下拉菜单|boolean|-|-|
|options|数据化配置选项内容，相比 jsx 定义会获得更好的渲染性能|string|-|-|
|placeholder|输入框提示|{ label, value }[]|-|-|
|value|指定当前选中的条目|string|-|-|
|onBlur|失去焦点时的回调|function()|-|-|
|onChange|选中 option，或 input 的 value 变化时，调用此函数|function(value)|-|-|
|onDropdownVisibleChange|展开下拉菜单的回调|function(open)|-|-|
|onFocus|获得焦点时的回调|function()|-|-|
|onSearch|搜索补全项的时候调用|function(value)|-|-|
|onSelect|被选中时调用，参数为选中项的 value 值|function(value, option)|-|-|
|onClear|清除内容时回调|function|-|-|


## InputNumber 属性说明

|成员|说明|类型|默认值|版本|
|---|---|---|---|---|
|autoFocus|自动获取焦点|boolean|false|-|
|bordered|是否有边框|boolean|true|-|
|decimalSeparator|小数点|string|-|-|
|defaultValue|初始值|number|-|-|
|disabled|禁用|boolean|false|-|
|formatter|指定输入框展示值的格式|function (value: number \| string): string|-|-|
|keyboard|是否启用键盘快捷行为|boolean|true|-|
|max|最大值|number| [Number.MAX_SAFE_INTEGER](mdc:https:/developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER) |-|
|min|最小值|number| [Number.MIN_SAFE_INTEGER](mdc:https:/developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/MIN_SAFE_INTEGER) |-|
|parser|指定从 `formatter` 里转换回数字的方式，和 `formatter` 搭配使用|function (string): number|-|-|
|precision|数值精度，配置 `formatter` 时会以 `formatter` 为准|number|-|-|
|readOnly|只读|boolean|false|-|
|size|输入框大小|`large` \| `middle` \| `small`|-|-|
|step|每次改变步数，可以为小数|number \| string|1|-|
|stringMode|字符值模式，开启后支持高精度小数。同时 `onChange` 将返回 string 类型|boolean|false|-|
|symmetryMode|对称模式，开启后改变步数的按钮在两边|boolean|false|-|
|value|当前值|number|-|-|
|warning|带警示的文本框|boolean|false|-|
|warningPopover|带警示的文本框，以 popover 形式展示与 warning 设置一个即可|boolean|false|-|
|warningText|警示文本框的警示文字|string|-|-|
|onChange|变化回调|function (value: number \| string \| null)|-|-|
|onPressEnter|按下回车的回调|function (e)|-|-|
|onStep|点击上下箭头的回调|(value: number, info: { offset: number, type: 'up' \| 'down' }) => void|-|-|

### 方法

| 名称      | 描述   |
| ------- | ---- |
| blur ()  | 移除焦点 |
| focus () | 获取焦点 |

## Switch 属性说明

|参数|说明|类型|默认值|
|---|---|---|---|
|autoFocus|组件自动获取焦点|boolean|false|
|defaultChecked|初始是否选中|boolean|false|
|checked|指定当前是否选中|boolean|false|
|checkedChildren|选中时的内容|ReactNode|-|
|unCheckedChildren|非选中时的内容|ReactNode|-|
|className|Switch 器类名|string|-|
|disabled|是否禁用|boolean|false|
|loading|加载中的开关|boolean|false|
|size|开关大小，可选值：`default` `small`|string|`default`|
|onChange|变化时回调函数|function(checked: boolean, event: Event)|-|
|onClick|点击时回调函数|function(checked: boolean, event: Event)|-|

## Drawer 属性说明

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|afterVisibleChange|切换抽屉时动画结束后的回调|function(visible)|-||
|bodyStyle|可用于设置 Drawer 内容部分的样式|CSSProperties|-||
|className|对话框外层容器的类名|string|-||
|closable|是否显示右上角的关闭按钮|boolean|true||
|closeIcon|自定义关闭图标|ReactNode|<OutlinedClose />||
|contentWrapperStyle|可用于设置 Drawer 包裹内容部分的样式|CSSProperties|-||
|destroyOnClose|关闭时销毁 Drawer 里的子元素|boolean|false||
|drawerStyle|用于设置 Drawer 弹出层的样式|CSSProperties|-||
|footer|抽屉的页脚|ReactNode|-||
|footerStyle|抽屉页脚部件的样式|CSSProperties|-||
|forceRender|预渲染 Drawer 内元素|boolean|false||
|getContainer|指定 Drawer 挂载的 HTML 节点, false 为挂载在当前 dom|HTMLElement \| () => HTMLElement \| Selectors \| false|body||
|headerStyle|用于设置 Drawer 头部的样式|CSSProperties|-||
|height|高度, 在 `placement` 为 `top` 或 `bottom` 时使用|string \| number|256||
|keyboard|是否支持键盘 esc 关闭|boolean|true||
|mask|是否展示遮罩|boolean|true||
|maskClosable|点击蒙层是否允许关闭|boolean|true||
|maskStyle|遮罩样式|CSSProperties|{}||
|placement|抽屉的方向|`top` \| `right` \| `bottom` \| `left`|`right`||
|push|用于设置多层 Drawer 的推动行为|boolean \| { distance: string \| number }|{ distance: 0 }|1.1.16+|
|style|可用于设置 Drawer 最外层容器的样式，和 `drawerStyle` 的区别是作用节点包括 `mask`|CSSProperties|-||
|title|标题|ReactNode|-||
|visible|Drawer 是否可见|boolean|-||
|width|宽度|string \| number|400||
|zIndex|设置 Drawer 的 `z-index`|number|1000||
|onClose|点击遮罩层或右上角叉或取消按钮的回调|function(e)|-||


## Modal 属性说明

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|afterClose|Modal 完全关闭后的回调|function|-||
|size|尺寸|'small' \| 'normal' \| 'large' \| 'extraLarge'|'small'||
|cancelText|取消按钮文字|ReactNode|`取消`||
|okText|确认按钮文字|ReactNode|`确定`||
|okButtonProps|确认按钮 props| [ButtonProps](mdc:https:/acud.now.baidu-int.com/components/button/#API) |-||
|cancelButtonProps|取消按钮 props| [ButtonProps](mdc:https:/acud.now.baidu-int.com/components/button/#API) |-||
|confirmLoading|确定按钮 loading|boolean|false||
|destroyOnClose|关闭时销毁 Modal 里的子元素|boolean|false||
|footer|底部内容，当不需要默认底部按钮时，可以设为 `footer={null}`|ReactNode|(确定取消按钮)||
|forceRender|强制渲染 Modal|boolean|false||
|getContainer|指定 Modal 挂载的 HTML 节点|HTMLElement \| () => HTMLElement \| null|document. body|1.4.55|
|keyboard|是否支持键盘 esc 关闭|boolean|true||
|closable|是否显示右上角的关闭按钮|boolean|true||
|maskClosable|点击蒙层是否允许关闭|boolean|false||
|style|可用于设置浮层的样式，调整浮层位置等|CSSProperties|-||
|className|可添加自定义 class|string|-||
|title|标题|ReactNode|-||
|visible|对话框是否可见|boolean|-||
|width|宽度|string \| number|520||
|zIndex|设置 Modal 的 `z-index`|number|1000||
|Cancel|点击遮罩层或右上角叉或取消按钮的回调|function (e)|-||
|onOk|点击确定回调|function (e)|-||

#### 注意

- `<Modal />` 默认关闭后状态不会自动清空, 如果希望每次打开都是新内容，请设置 `destroyOnClose`。
    

#### Modal.method ()

目前支持：

- `Modal.confirm (config)`
    
- `Modal.success (config)`
    
- `Modal.info (config)`
    
- `Modal.error (config)`
    
- `Modal.warning (config)`
    

##### config
|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|afterClose|Modal 完全关闭后的回调|function|-||
|cancelText|取消按钮文字|ReactNode|`取消`||
|destroyOnClose|关闭时销毁 Modal 里的子元素|boolean|false||
|getContainer|指定 Modal 挂载的 HTML 节点|HTMLElement \| () => HTMLElement \| null|document.body|1.4.55|
|icon|弹窗icon|ReactNode|-||
|keyboard|是否支持键盘 esc 关闭|boolean|true||
|maskClosable|点击蒙层是否允许关闭|boolean|false||
|okText|确认按钮文字|ReactNode|`确定`||
|style|可用于设置浮层的样式，调整浮层位置等|CSSProperties|-||
|title|标题|ReactNode|-||
|content|内容|ReactNode|-||
|width|宽度|string \| number|400||
|zIndex|设置 Modal 的 `z-index`|number|1000||
|onCancel|点击遮罩层或右上角叉或取消按钮的回调|function(e)|-||
|onOk|点击确定回调|(e: React.MouseEvent) => (void \| Promise)，如果返回 Promise，按钮会根据 Promise 添加 loading 状态|-||



## Slider 属性说明

|参数|说明|类型|默认值|
|---|---|---|---|
|allowClear|支持清除, 单选模式有效|boolean|false|
|defaultValue|设置初始取值。当 `range` 为 false 时，使用 number，否则用 [number, number]|number \| [number, number]|0 \| [0, 0]|
|disabled|值为 true 时，滑块为禁用状态|boolean|false|
|dots|是否只能拖拽到刻度上|boolean|false|
|getTooltipPopupContainer|Tooltip 渲染父节点，默认渲染到 body 上|(triggerNode) => HTMLElement|() => document.body|
|included|`marks` 不为空对象时有效，值为 true 时表示值为包含关系，false 表示并列|boolean|true|
|marks|刻度标记，key 的类型必须为 `number` 且取值在闭区间 [min, max] 内，每个标签可以单独设置样式|object|{ number: ReactNode } or { number: { style: CSSProperties, label: ReactNode } }|
|max|最大值|number|100|
|min|最小值|number|0|
|range|双滑块模式|boolean \| [range](mdc:https:/acud.now.baidu-int.com/components/slider#range)|false|
|reverse|反向坐标轴|boolean|false|
|step|步长，取值必须大于 0，并且可被 (max - min) 整除。当 `marks` 不为空对象时，可以设置 `step` 为 null，此时 Slider 的可选值仅有 marks 标出来的部分|number \| null|1|
|tipFormatter|Slider 会把当前值传给 `tipFormatter`，并在 Tooltip 中显示 `tipFormatter` 的返回值，若为 null，则隐藏 Tooltip|value => ReactNode \| null|IDENTITY|
|tooltipPlacement|设置 Tooltip 展示位置。参考 [Tooltip](mdc:https:/acud.now.baidu-int.com/components/tooltip)|string|-|
|tooltipVisible|值为 true 时，Tooltip 将会始终显示；否则始终不显示，哪怕在拖拽及移入时|boolean|-|
|value|设置当前取值。当 `range` 为 false 时，使用 number，否则用 [number, number]|number \| [number, number]|-|
|vertical|值为 true 时，Slider 为垂直方向|boolean|false|
|onAfterChange|与 `onmouseup` 触发时机一致，把当前值作为参数传入|(value) => void|-|
|onChange|当 Slider 的值发生改变时，会触发 onChange 事件，并把改变后的值作为参数传入|(value) => void|-|

### range[#](mdc:https:/acud.now.baidu-int.com/components/slider#range)

|参数|说明|类型|默认值|
|---|---|---|---|
|draggableTrack|范围刻度是否可被拖拽|boolean|false|

### 方法

|名称|描述|
|---|---|
|blur()|移除焦点|
|focus()|获取焦点|


## Table 属性说明

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|bordered|是否展示外边框和列边框（目前设计规范都不带边框，不建议使用）|boolean|false||
|columns|表格列的配置描述，具体项见下表|[ColumnsType](mdc:https:/acud.now.baidu-int.com/components/table#Column)[]|-||
|components|覆盖默认的 table 元素|[TableComponents](mdc:https:/git.io/fANxz)|-||
|dataSource|数据数组|object[]|-||
|expandable|配置展开属性|[expandable](mdc:https:/acud.now.baidu-int.com/components/table#expandable)|-||
|footer|表格尾部|function(currentPageData)|-||
|getPopupContainer|设置表格内各类浮层的渲染节点，如筛选菜单|(triggerNode) => HTMLElement|() => TableHtmlElement||
|loading|页面是否加载中|boolean \| [Loading Props](mdc:https:/acud.now.baidu-int.com/components/loading/#API)|false||
|locale|本地化文案,需要注意的是，table默认使用Empty组件渲染空表格，故配置空文案可以通过ConfigProvider修改locale.Empty.description实现，如果要自定义空组件，则可以通过此locale.emptyText实现|object {filterTitle?: string;filterConfirm?: React.ReactNode;filterReset?: React.ReactNode;filterEmptyText?: React.ReactNode;filterCheckall?: React.ReactNode;filterAll?: React.ReactNode;filterSearchPlaceholder?: string;emptyText?: React.ReactNode \| (() => React.ReactNode);selectAll?: React.ReactNode;selectNone?: React.ReactNode;selectInvert?: React.ReactNode;selectionAll?: React.ReactNode;sortTitle?: string;expand?: string;collapse?: string;triggerDesc?: string;triggerAsc?: string;cancelSort?: string;}|{filterTitle: '筛选',filterConfirm: '确定',filterReset: '重置',filterEmptyText: '无筛选项',filterCheckall: '全选',filterAll: '全部',selectAll: '全选当页',emptyText:'',selectInvert: '反选当页',selectNone: '清空所有',selectionAll: '全选所有',sortTitle: '排序',expand: '展开行',collapse: '关闭行',triggerDesc: '点击降序',triggerAsc: '点击升序',cancelSort: '取消排序'}||
|pagination|分页器，参考[配置项](mdc:https:/acud.now.baidu-int.com/components/table#pagination)或 [pagination](mdc:https:/acud.now.baidu-int.com/components/pagination) 文档，设为 false 时不展示和进行分页|object|-||
|rowClassName|表格行的类名|function(record, index): string|-||
|rowKey|表格行 key 的取值，可以是字符串或一个函数|string \| function(record): string|`key`||
|rowSelection|表格行是否可选择，[配置项](mdc:https:/acud.now.baidu-int.com/components/table#rowSelection)|object|-||
|scroll|表格是否可滚动，也可以指定滚动区域的宽、高，[配置项](mdc:https:/acud.now.baidu-int.com/components/table#scroll)|object|-||
|showHeader|是否显示表头|boolean|true||
|showSorterTooltip|表头是否显示下一次排序的 tooltip 提示。当参数类型为对象时，将被设置为 Tooltip 的属性|boolean \| [Tooltip props](mdc:https:/acud.now.baidu-int.com/components/tooltip)|true||
|size|表格大小|`default`(常规，高度40px) \| `large`(宽松，高度60px) \| `small`（紧凑，高度32px）|default||
|sortDirections|支持的排序方式，取值为 `ascend` `descend`|Array|[`ascend`, `descend`]||
|sticky|设置粘性头部和滚动条|boolean \| `{offsetHeader?: number, offsetScroll?: number, getContainer?: () => HTMLElement}`|-||
|summary|总结栏|(currentData) => ReactNode|-||
|tableLayout|表格元素的 [table-layout](mdc:https:/developer.mozilla.org/zh-CN/docs/Web/CSS/table-layout) 属性，设为 `fixed` 表示内容不会影响列的布局|- \| `auto` \| `fixed`|无<br><br>---<br><br>固定表头/列或使用了 `column.ellipsis` 时，默认值为 `fixed`||
|title|表格标题|function(currentPageData)|-||
|onChange|分页、排序、筛选变化时触发|function(pagination, filters, sorter, extra: { currentDataSource: [], action: `paginate` \| `sort` \| `filter` })|-||
|onHeaderRow|设置头部行属性|function(columns, index)|-||
|onRow|设置行属性|function(record, index)|-||
|expandIndex|设置children数据展示时，展开图标显示在第几列|number|1|1.0.36|

#### onRow 用法

适用于 `onRow` `onHeaderRow` `onCell` `onHeaderCell`。

```
<Table
    onRow={record => {
        return {
            onClick: event => {}, // 点击行
            onDoubleClick: event => {},
            onContextMenu: event => {},
            onMouseEnter: event => {}, // 鼠标移入行
            onMouseLeave: event => {}
        };
    }}
    onHeaderRow={(columns, index) => {
        return {
            onClick: () => {}, // 点击表头行
        };
    }}
/>
```

### Column[#](mdc:https:/acud.now.baidu-int.com/components/table#Column)

列描述数据对象，是 columns 中的一项，Column 使用相同的 API。

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|align|设置列的对齐方式|`left` \| `right` \| `center`|`left`||
|className|列样式类名|string|-||
|colSpan|表头列合并,设置为 0 时，不渲染|number|-||
|dataIndex|列数据在数据项中对应的路径，支持通过数组查询嵌套路径|string \| string[]|-||
|defaultFilteredValue|默认筛选值|string[]|-||
|defaultS \| `descend`|-||
|edie||
|ellipsis|超过宽度将自动省略，暂不支持和排序筛选一起使用。  <br>设置为 `true` 或 `{ showTitle?: boolean }` 时，表格布局将变成 `tableLayout="fixed"`。|boolean \| { showTitle?: boolean }|false||
|filterDropdown|可以自定义筛选菜单，此函数只负责渲染图层，需要自行编写各种交互|ReactNode \| () => ReactNode|-||
|filterDropdownVisible|用于控制自定义筛选菜单是否可见|boolean|-||
|filtered|标识数据是否经过过滤，筛选图标会高亮|boolean|false||
|filteredValue|筛选的受控属性，外界可用此控制列的筛选状态，值为已筛选的 value 数组|string[]|-||
|filterIcon|自定义 filter 图标。|ReactNode \| (filtered: boolean) => ReactNode|false||
|filterMultiple|是否多选|boolean|true||
|filterMode|指定筛选菜单的用户界面|'menu' \| 'tree'|'menu'||
|filterSearch|筛选菜单项是否可搜索|boolean \| (searchValue: string, filterItem: ColumnFilterItem) =>  <br>filterItem.text?.toString().toLowerCase()  <br>.indexOf(searchValue.trim().toLowerCase()) > -1|false|1.1.7|
|filterPlaceholder|筛选菜单项搜索框placeholder|string|-|1.4.13|
|filters|表头的筛选菜单项|object[]|-||
|filterShowSelectAll|筛选菜单是否展示【单选全部/多选全选】选项|boolean|true|1.4.30|
|fixed|（IE 下无效）列是否固定，可选 true (等效于 left) `left` `right`|boolean \| string|false||
|key|React 需要的 key，如果已经设置了唯一的 `dataIndex`，可以忽略这个属性|string|-||
|render|生成复杂数据的渲染函数，参数分别为当前行的值，当前行数据，行索引|function(text, record, index) {}|-||
|responsive|响应式 breakpoint 配置列表。未设置则始终可见。|Breakpoint[]|-||
|shouldCellUpdate|自定义单元格渲染时机|(record, prevRecord) => boolean|-||
|showSorterTooltip|表头显示下一次排序的 tooltip 提示, 覆盖 table 中 `showSorterTooltip`|boolean \| [Tooltip props](mdc:https:/acud.now.baidu-int.com/components/tooltip/#API)|true||
|sortDirections|支持的排序方式，覆盖 `Table` 中 `sortDirections`， 取值为 `ascend` `descend`|Array|[`ascend`, `descend`]||
|sorter|排序函数，本地排序使用一个函数(参考 [Array.sort](mdc:https:/developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort) 的 compareFunction)，需要服务端排序可设为 true|function \| boolean|-||
|sortOrder|排序的受控属性，外界可用此控制列的排序，可设置为 `ascend` `descend` false|boolean \| string|-||
|title|列头显示文字|ReactNode \| ({ sortOrder, sortColumn, filters }) => ReactNode|-||
|width|列宽度|string \| number|-||
|onCell|设置单元格属性|function(record, rowIndex)|-||
|onFilter|本地模式下，确定筛选的运行函数|function|-||
|onFilterDropdownVisibleChange|自定义筛选菜单可见变化时调用|function(visible) {}|-||
|onHeaderCell|设置头部单元格属性|function(column)|-||

### ColumnGroup[#](mdc:https:/acud.now.baidu-int.com/components/table#ColumnGroup)

|参数|说明|类型|默认值|
|---|---|---|---|
|title|列头显示文字|ReactNode|-|

### Space[#](mdc:https:/acud.now.baidu-int.com/components/table#Space)

|参数|说明|类型|默认值|
|---|---|---|---|
|space|间距|number \| string|12|

### pagination[#](mdc:https:/acud.now.baidu-int.com/components/table#pagination)

分页的配置项。

|参数|说明|类型|默认值|
|---|---|---|---|
|position|指定分页显示的位置， 取值为`topLeft` \| `topCenter` \| `topRight` \|`bottomLeft` \| `bottomCenter` \| `bottomRight`|Array|[`bottomRight`]|

更多配置项，请查看 [`Pagination`](mdc:https:/acud.now.baidu-int.com/components/pagination)。

### expandable[#](mdc:https:/acud.now.baidu-int.com/components/table#expandable)

展开功能的配置。

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|childrenColumnName|指定树形结构的列名|string|children||
|columnWidth|自定义展开列宽度|string \| number|-||
|defaultExpandAllRows|初始时，是否展开所有行|boolean|false||
|defaultExpandedRowKeys|默认展开的行|string[]|-||
|expandedRowClassName|展开行的 className|function(record, index, indent): string|-||
|expandedRowKeys|展开的行，控制属性|string[]|-||
|expandedRowRender|额外的展开行|function(record, index, indent, expanded): ReactNode|-||
|expandIcon|自定义展开图标|function(props): ReactNode|-||
|expandRowByClick|通过点击行来展开子行|boolean|false||
|fixed|控制展开图标是否固定，可选 true `left` `right`|boolean \| string|false||
|indentSize|展示树形数据时，每层缩进的宽度，以 px 为单位|number|31||
|rowExpandable|设置是否允许行展开|(record) => boolean|-||
|showExpandColumn|设置是否展示行展开列|boolean|true||
|onExpand|点击展开图标时触发|function(expanded, record)|-||
|onExpandedRowsChange|展开的行变化时触发|function(expandedRows)|-||

### rowSelection[#](mdc:https:/acud.now.baidu-int.com/components/table#rowSelection)

选择功能的配置。

|参数|说明|类型|默认值|版本|
|---|---|---|---|---|
|checkStrictly|checkable 状态下节点选择完全受控（父子数据选中状态不再关联）|boolean|true||
|columnTitle|自定义列表选择框标题|ReactNode|-||
|columnWidth|自定义列表选择框宽度|string \| number|`32px`||
|fixed|把选择框列固定在左边|boolean|-||
|getCheckboxProps|选择框的默认属性配置|function(record)|-||
|hideSelectAll|隐藏全选勾选框与自定义选择项|boolean|false||
|preserveSelectedRowKeys|当数据被删除时仍然保留选项的 `key`|boolean|-||
|renderCell|渲染勾选框，用法与 Column 的 `render` 相同|function(checked, record, index, originNode) {}|-||
|selectedRowKeys|指定选中项的 key 数组，需要和 onChange 进行配合|string[] \| number[]|[]||
|defaultSelectedRowKeys|默认选中项的 key 数组|string[] \| number[]|[]||
|selections|自定义选择项 [配置项](mdc:https:/acud.now.baidu-int.com/components/table#selection), 设为 `true` 时使用默认选择项|object[] \| boolean|true||
|type|多选/单选|`checkbox` \| `radio`|`checkbox`||
|onChange|选中项发生变化时的回调|function(selectedRowKeys, selectedRows)|-||
|onSelect|用户手动选择/取消选择某行的回调|function(record, selected, selectedRows, nativeEvent)|-||
|onSelectAll|用户手动选择/取消选择所有行的回调|function(selected, selectedRows, changeRows)|-||
|onSelectInvert|用户手动选择反选的回调|function(selectedRowKeys)|-||
|onSelectNone|用户清空选择的回调|function()|-||

### scroll[#](mdc:https:/acud.now.baidu-int.com/components/table#scroll)

|参数|说明|类型|默认值|
|---|---|---|---|
|scrollToFirstRowOnChange|当分页、排序、筛选变化后是否滚动到表格顶部|boolean|-|
|x|设置横向滚动，也可用于指定滚动区域的宽，可以设置为像素值，百分比，true 和 ['max-content'](mdc:https:/developer.mozilla.org/zh-CN/docs/Web/CSS/width#max-content)|string \| number \| true|-|
|y|设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值|string \| number|-|

### selection[#](mdc:https:/acud.now.baidu-int.com/components/table#selection)

|参数|说明|类型|默认值|
|---|---|---|---|
|key|React 需要的 key，建议设置|string|-|
|text|选择项显示的文字|ReactNode|-|
|onSelect|选择项点击回调|function(changeableRowKeys)|-|