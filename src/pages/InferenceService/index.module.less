.inferenceServiceContainer {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
}

.actionBar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.rightActions {
  display: flex;
  align-items: center;
}

.formSection {
  margin-bottom: 24px;
  padding-bottom: 8px;
}

.formSectionLast {
  margin-bottom: 0;
  padding-bottom: 0;
}

.sectionTitle {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.readonlyField {
  color: rgba(0, 0, 0, 0.85);
  line-height: 32px;
}




.clusterListContainer :global(.acud-table) {
  table-layout: fixed;
  width: 100%;
}

.clusterListContainer :global(.acud-table-container) {
  max-width: 100%;
}

.clusterListContainer :global(.acud-table-thead > tr > th),
.clusterListContainer :global(.acud-table-tbody > tr > td) {
  white-space: normal;
  word-break: break-word;
}

.clusterListContainer :global(.acud-table-empty .acud-table-tbody > tr > td) {
  max-width: 100%;
  width: 100%;
}

/* 优化表格内容显示 */
.cellContent {
  word-break: break-all;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 备注列样式 */
.remarkCell {
  max-width: 200px;
  color: rgba(0, 0, 0, 0.65);
}

/* 关联时间列样式 */
.timeCell {
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.65);
}

/* 优化下拉选择器 */
.serviceSelector {
  width: 100%;
  :global {
    .acud-select-selection-item {
      line-height: 1.5;
      padding: 4px;
      display: inline-flex;
      align-items: center;
      margin-top: 1px;
      margin-bottom: 1px;
    }
    
    .acud-select-selection-overflow-item {
      display: flex;
      align-items: center;
    }
    
    .acud-select-dropdown {
      .acud-select-item {
        padding: 8px 12px;
        &.acud-select-item-option-selected {
          .acud-select-item-option-content {
            font-weight: 500;
          }
        }
        .acud-select-item-option-content {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}

/* 服务选项样式 */
.serviceOption {
  display: flex;
  align-items: center;
  padding: 2px 0;
  line-height: 20px;
  min-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 修复复选框对齐问题 */
:global {
  .acud-select-dropdown {
    .acud-select-item-option {
      display: flex;
      align-items: center;
    }
    
    .acud-checkbox-wrapper {
      display: flex;
      align-items: center;
      
      .acud-checkbox {
        top: 0;
      }
    }
  }
}



.clusterTable {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  border: 1px solid #E8E9EB;
  border-radius: 6px;
  overflow: hidden; // 确保圆角边框效果
  // 移除内边框
  
  :global(.acud-table-thead > tr > th) {
    border-right: none !important;
  }
  
  :global(.acud-table-tbody > tr > td) {
    border-right: none !important;
  }
}



