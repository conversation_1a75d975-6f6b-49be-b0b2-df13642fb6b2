import React, {useCallback} from 'react';
import {Button, Modal} from 'acud';

import {deleteServiceInstance} from '@/apis/serviceAdmin';

export default function DeleteServiceInstance(props: {
  item: any;
  instanceId: string;
  serviceId: string;
  refresh: () => void;
}) {
  const {instanceId, serviceId, item} = props;
  const onDelete = useCallback(() => {
    Modal.confirm({
      title: '删除确认',
      content: '确认删除实例吗？删除后无法恢复！',
      onOk() {
        return deleteServiceInstance({
          instanceId,
          serviceId,
          serviceInstanceId: item.serviceInstanceId
        }).then(() => {
          props?.refresh();
        });
      }
    });
  }, [instanceId, serviceId, item, props]);
  return (
    <Button type="actiontext" onClick={onDelete}>
      删除
    </Button>
  );
}
