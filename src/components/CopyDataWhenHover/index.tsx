import React, {useCallback, useMemo, useState} from 'react';
import {Clipboard} from '@baidu/bce-react-toolkit';

import style from './index.module.less';

interface IProps {
  copyValue: string;
  forceShow?: boolean;
}
export default function CopyDataWhenHover({copyValue, forceShow}: IProps) {
  const [showCopy, setShowCopy] = useState(false);
  const mouseOverHandler = useCallback(() => {
    setShowCopy(true);
  }, []);
  const mouseLeaveHandler = useCallback(() => {
    setShowCopy(false);
  }, []);

  const shouldShowCopy = forceShow || showCopy;

  return (
    <div
      onMouseEnter={mouseOverHandler}
      onMouseLeave={mouseLeaveHandler}
      className={style['hover-copy-wrap']}
    >
      {copyValue}
      {shouldShowCopy && (
        <Clipboard
          text={copyValue}
          successMessage="复制成功"
          className={style['cursor-pointer']}
        ></Clipboard>
      )}
    </div>
  );
}
