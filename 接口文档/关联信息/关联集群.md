# 关联集群接口

## 接口基本信息

- **接口说明**：将指定的CCE集群关联到AI网关实例
- **接口地址**：`/api/aigw/v1/aigateway/instance/{instanceId}/clusterList`
- **请求方式**：POST
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | AI网关实例ID | i-a1b2c3d4 |

### 请求体参数

```json
{
    "clusters": [
        {
            "clusterId": "cce-qhjz40ds",
            "clusterName": "zhoujun-user-1"
        }
    ],
    "remark": "测试关联集群",
    "ingressSettings": {
        "enableIngress": false,
        "enableAllIngressClasses": true,
        "enableAllNamespaces": true,
        "ingressClasses": [],
        "watchNamespaces": []
    }
}
```

| 参数名 | 类型 | 是否必填 | 描述 | 限制 |
| --- | --- | --- | --- | --- |
| clusters | Array | 是 | 要关联的CCE集群数组 | 数组长度最大为1，集群必须与网关实例在同一地域和VPC下 |
| clusters[].clusterId | String | 是 | 集群ID | 必须是有效的CCE集群ID |
| clusters[].clusterName | String | 是 | 集群名称 | - |
| remark | String | 否 | 关联备注信息 | 最多64个字符 |
| ingressSettings | Object | 否 | Ingress配置信息 | - |
| ingressSettings.enableIngress | Boolean | 否 | 是否开启监听Ingress | 默认为false |
| ingressSettings.enableAllIngressClasses | Boolean | 否 | 是否监听所有IngressClasses | 默认为true |
| ingressSettings.enableAllNamespaces | Boolean | 否 | 是否监听所有命名空间 | 默认为true，本期前端仅支持监听所有命名空间 |
| ingressSettings.ingressClasses | Array | 否 | IngressClass数组 | 可为空，仅当enableAllIngressClasses为false时传入，本期前端仅支持传入1个 |
| ingressSettings.watchNamespaces | Array | 否 | 监听的命名空间数组 | 可为空，仅当enableAllNamespaces为false时传入，本期该数组为空 |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| Content-Type | String | 是 | 内容类型 | application/json |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": {
        "taskId": "task-12345678",
        "instanceId": "i-a1b2c3d4"
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 结果对象 |
| result.taskId | String | 关联任务ID，可用于查询任务状态 |
| result.instanceId | String | 网关实例ID |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 确认用户是否有权限操作该实例 |
| 404 | 实例或集群不存在 | 确认实例ID和集群ID是否正确 |
| 409 | 集群已关联或不满足关联条件 | 检查集群是否已关联到其他实例或不符合关联条件（如不在同一VPC） |
| 429 | 已达到最大关联集群数量 | 当前仅支持关联最多1个集群 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
POST /api/aigw/v1/aigateway/instance/i-a1b2c3d4/clusterList
Content-Type: application/json

{
    "clusters": [
        {
            "clusterId": "cce-qhjz40ds",
            "clusterName": "zhoujun-user-1"
        }
    ],
    "remark": "测试环境集群关联",
    "ingressSettings": {
        "enableIngress": true,
        "enableAllIngressClasses": true,
        "enableAllNamespaces": true,
        "ingressClasses": [],
        "watchNamespaces": []
    }
}
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "taskId": "task-12345678",
        "instanceId": "i-a1b2c3d4"
    }
}
```

## 注意事项

1. 关联集群时，集群必须与网关实例在同一地域和同一VPC下
2. 当前版本每个网关实例最多只能关联1个CCE集群
3. 关联过程是异步的，接口返回成功表示关联任务已提交，可通过taskId查询具体进度
4. 备注信息为选填项，若传入多个集群（未来可能支持），则所有集群共用同一个备注
5. 关联成功后，网关实例将能够直接访问集群内的服务 