# 获取CProm实例详情

## 接口描述

根据CProm实例ID获取单个实例的详细信息，用于查看特定CProm实例的完整配置和状态信息。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/cprom/instances/{instanceId}
```

### 请求方法
GET

### 请求参数

#### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | CProm实例ID | cprom-x35s7xaq7ezx7 |

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": {
    "monitorGrafanaId": "",
    "monitorGrafanaName": "",
    "metadata": {
      "name": "",
      "labels": {
        "cprom-account-id": "1093",
        "cprom-instance-template": "advance-v2",
        "cprom-instance-type": "default",
        "cprom-namespace": "cprom-80h84dy147ra7",
        "cprom-project-id": "0"
      },
      "creationTimestamp": "2025-07-17T20:19:38+08:00"
    },
    "spec": {
      "instanceID": "cprom-x35s7xaq7ezx7",
      "instanceName": "aihc-cftoyjczc8cw",
      "region": "bj",
      "vmClusterConfig": {
        "retentionPeriod": "15d"
      }
    },
    "status": {
      "message": "all components are ready",
      "phase": "Running"
    }
  },
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Object | CProm实例详情 |
| result.monitorGrafanaId | String | 关联的Grafana实例ID |
| result.monitorGrafanaName | String | 关联的Grafana实例名称 |
| result.metadata | Object | 实例元数据信息 |
| result.metadata.name | String | 实例元数据名称 |
| result.metadata.labels | Object | 实例标签信息 |
| result.metadata.creationTimestamp | String | 实例创建时间（ISO 8601格式） |
| result.spec | Object | 实例规格配置 |
| result.spec.instanceID | String | 实例ID |
| result.spec.instanceName | String | 实例名称 |
| result.spec.region | String | 实例区域 |
| result.spec.vmClusterConfig | Object | VM集群配置 |
| result.spec.vmClusterConfig.retentionPeriod | String | 数据保留周期 |
| result.status | Object | 实例状态信息 |
| result.status.phase | String | 实例运行阶段（Running/Stopped/Creating等） |
| result.status.message | String | 状态描述信息 |
| status | Integer | HTTP状态码 |

## 请求示例

### 请求

```bash
curl -X GET \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/cprom/instances/cprom-x35s7xaq7ezx7' \
  -H 'X-Region: bj'
```

### 响应示例

#### 成功获取实例详情

```json
{
  "success": true,
  "result": {
    "monitorGrafanaId": "grafana-7v0hyglh7",
    "monitorGrafanaName": "bjtest",
    "metadata": {
      "name": "",
      "labels": {
        "cprom-account-id": "1062",
        "cprom-instance-template": "advance-v2",
        "cprom-instance-type": "BCM",
        "cprom-namespace": "cprom-9ldhmrf7k0w37",
        "cprom-project-id": "0"
      },
      "creationTimestamp": "2025-07-11T19:47:51+08:00"
    },
    "spec": {
      "instanceID": "cprom-9ldhmrf7k0w37",
      "instanceName": "rdma-mihayou-not-delete",
      "region": "bj",
      "vmClusterConfig": {
        "retentionPeriod": "15d"
      }
    },
    "status": {
      "message": "all components are ready",
      "phase": "Running"
    }
  },
  "status": 200
}
```

#### 实例不存在

```json
{
  "success": false,
  "message": "CProm instance not found",
  "status": 404
}
```

#### 参数错误

```json
{
  "success": false,
  "message": "instanceId is required",
  "status": 400
}
```

## 功能说明

### 使用场景

1. **实例详情查看**：查看特定CProm实例的完整配置信息
2. **状态监控**：检查实例的运行状态和健康状况
3. **配置验证**：验证实例的配置参数是否符合预期
4. **故障排查**：通过详细信息进行问题诊断
5. **Grafana集成检查**：确认实例是否已关联Grafana仪表板

### 实例状态说明

| 状态 | 描述 |
|------|------|
| Creating | 实例创建中 |
| Running | 实例运行中，可正常使用 |
| Stopped | 实例已停止 |
| Deleting | 实例删除中 |
| Error | 实例异常 |

### 字段详细说明

#### metadata 字段
- **labels**: 包含实例的分类和管理信息
  - `cprom-account-id`: 所属账户ID
  - `cprom-instance-template`: 实例使用的模板类型
  - `cprom-instance-type`: 实例类型分类
  - `cprom-namespace`: 实例所在的命名空间
  - `cprom-project-id`: 关联的项目ID

#### spec 字段
- **vmClusterConfig**: 虚拟机集群配置
  - `retentionPeriod`: 监控数据保留时间，格式如"15d"表示15天

#### status 字段
- **phase**: 实例当前的生命周期阶段
- **message**: 实例状态的详细描述信息

## 注意事项

1. 该接口只能获取当前用户有权限访问的CProm实例详情
2. 如果指定的实例ID不存在，将返回404错误
3. 实例详情会实时反映当前状态，可用于监控实例健康状况
4. `metadata.labels` 中的信息可用于实例分类和管理
5. `spec.vmClusterConfig.retentionPeriod` 决定了监控数据的保留时间
6. 通过 `status.phase` 和 `status.message` 可以判断实例是否可用
7. 如果 `monitorGrafanaId` 不为空，表示该实例已关联Grafana仪表板

## 相关接口

- [获取CProm实例列表](./获取CProm实例列表.md) - 获取所有可用的CProm实例
- [开启业务指标监控](./开启业务指标监控.md) - 使用CProm实例配置监控
- [检查监控状态](./检查监控状态.md) - 查看当前使用的CProm实例
- [关闭业务指标监控](./关闭业务指标监控.md) - 停止使用CProm实例
