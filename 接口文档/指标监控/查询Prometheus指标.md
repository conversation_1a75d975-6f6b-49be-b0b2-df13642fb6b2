# 查询Prometheus指标

## 接口描述

查询AI网关实例的Prometheus指标数据，支持基础指标和业务指标的查询。基础指标来自托管账号的CProm实例，业务指标来自用户账号的CProm实例。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/metrics/query_range
GET /api/aigw/v1/aigateway/{instanceId}/metrics/query_range
```

### 请求方法
GET

### 请求参数

#### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | String | 是 | AI网关实例ID |

#### 查询参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| metricType | String | 是 | 指标类型：basic（基础指标）或 business（业务指标） | basic |
| query | String | 是 | PromQL查询语句 | up{job="prometheus"} |
| step | String | 是 | 查询步长，支持单位：s（秒）、m（分钟）、h（小时），默认为秒 | 60s 或 5m 或 1h |
| start | String | 是 | 开始时间（Unix时间戳） | 1640995200 |
| end | String | 是 | 结束时间（Unix时间戳） | 1641081600 |

#### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | bj |

## 响应信息

### 响应体结构

```json
{
  "success": true,
  "result": {
    "data": {
      "result": [
        {
          "metric": {
            "__name__": "up",
            "job": "prometheus",
            "instance": "localhost:9090"
          },
          "values": [
            [1640995200, "1"],
            [1640995260, "1"],
            [1640995320, "1"],
            [1640995380, "1"]
          ]
        }
      ],
      "resultType": "matrix"
    },
    "isPartial": false,
    "stats": {
      "seriesFetched": "1"
    },
    "status": "success"
  },
  "status": 200
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| result | Object | 响应结果 |
| result.data | Object | Prometheus查询数据 |
| result.data.resultType | String | 结果类型：matrix、vector、scalar、string |
| result.data.result | Array | 指标数据数组 |
| result.data.result[].metric | Object | 指标标签 |
| result.data.result[].values | Array | 时间序列数据点 |
| result.isPartial | Boolean | 是否为部分结果 |
| result.stats | Object | 查询统计信息 |
| result.stats.seriesFetched | String | 获取的时间序列数量 |
| result.status | String | Prometheus查询状态：success 或 error |
| status | Integer | HTTP状态码 |

## 请求示例

### 请求

```bash
curl -X GET \
  'https://csm.bj.baidubce.com/api/aigw/v1/aigateway/aigw-12345678/metrics/query_range?metricType=basic&query=up%7Bjob%3D%22prometheus%22%7D&step=60s&start=1640995200&end=1641081600' \
  -H 'X-Region: bj'
```

### 响应示例

#### 成功查询

```json
{
  "success": true,
  "result": {
    "data": {
      "result": [
        {
          "metric": {
            "__name__": "up",
            "job": "prometheus",
            "instance": "localhost:9090"
          },
          "values": [
            [1640995200, "1"],
            [1640995260, "1"],
            [1640995320, "1"],
            [1640995380, "1"]
          ]
        }
      ],
      "resultType": "matrix"
    },
    "isPartial": false,
    "stats": {
      "seriesFetched": "1"
    },
    "status": "success"
  },
  "status": 200
}
```

#### 查询失败

```json
{
  "success": true,
  "result": {
    "status": "error",
    "error": "invalid query: parse error at char 1: no expression found in input"
  },
  "status": 200
}
```

#### 无数据结果

```json
{
  "success": true,
  "result": {
    "data": {
      "result": [],
      "resultType": "matrix"
    },
    "isPartial": false,
    "stats": {
      "seriesFetched": "0"
    },
    "status": "success"
  },
  "status": 200
}
```

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 404 | AI网关实例不存在 | 确认实例ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

### 错误响应示例

#### 参数错误（400）

```json
{
  "code": "InvalidParameterValue",
  "message": "metricType must be 'basic' or 'business'",
  "requestId": "12345678-1234-1234-1234-123456789012"
}
```

#### 查询语法错误（Prometheus错误格式包装在result中）

```json
{
  "success": true,
  "status": 200,
  "result": {
    "status": "error",
    "error": "invalid query: parse error at char 1: no expression found in input"
  }
}
```

## 功能说明

### 指标类型说明

#### basic（基础指标）
- **数据源**：托管账号的CProm实例
- **内容**：AI网关基础运行指标，如CPU、内存、网络等
- **权限**：所有用户都可以查询
- **用途**：监控AI网关的基础运行状态

#### business（业务指标）
- **数据源**：用户账号的CProm实例
- **内容**：AI网关业务相关指标，如请求量、响应时间、错误率等
- **权限**：需要先开启业务指标监控
- **用途**：监控AI网关的业务运行情况

### PromQL查询语法

支持标准的Prometheus查询语法，常用示例：

```promql
# 查询CPU使用率
cpu_usage_percent{instance="aigw-12345678"}

# 查询内存使用率
memory_usage_percent{instance="aigw-12345678"}

# 查询请求QPS
rate(http_requests_total{instance="aigw-12345678"}[5m])

# 查询响应时间P99
histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{instance="aigw-12345678"}[5m]))

# 查询错误率
rate(http_requests_total{instance="aigw-12345678",status=~"5.."}[5m]) / rate(http_requests_total{instance="aigw-12345678"}[5m])
```

### 时间参数说明

- **start/end**：Unix时间戳（秒），支持10位时间戳
- **step**：查询步长（秒），决定数据点的密度
- **时间范围**：建议查询时间范围不超过24小时，以保证查询性能

### 查询性能优化

1. **合理设置step**：根据查询时间范围设置合适的步长
   - 1小时内：step=60（1分钟）
   - 1天内：step=300（5分钟）
   - 1周内：step=3600（1小时）

2. **使用标签过滤**：在PromQL中添加标签过滤，减少查询数据量

3. **避免复杂查询**：避免使用过于复杂的PromQL表达式

## 注意事项

1. **权限要求**：
   - 查询基础指标：需要AI网关实例的查看权限
   - 查询业务指标：需要先开启业务指标监控

2. **查询限制**：
   - 单次查询时间范围建议不超过24小时
   - 并发查询数量有限制，避免频繁查询

3. **数据延迟**：
   - 基础指标：通常有1-2分钟延迟
   - 业务指标：通常有2-5分钟延迟

4. **数据保留**：
   - 基础指标：保留15天
   - 业务指标：根据CProm实例配置决定

## 相关接口

- [检查监控状态](./检查监控状态.md) - 查看业务指标监控状态
- [开启业务指标监控](./开启业务指标监控.md) - 开启业务指标监控
- [获取CProm实例列表](./获取CProm实例列表.md) - 查看可用的CProm实例
