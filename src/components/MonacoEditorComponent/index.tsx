import React, {useCallback} from 'react';
import React<PERSON><PERSON><PERSON>iewer from 'react-diff-viewer';
import MonacoEditor, {loader} from '@monaco-editor/react';
import * as monaco from 'monaco-editor';

loader.config({monaco});

interface Props {
  language: string;
  height: string;
  width: string;
  value?: string;
  theme?: string;
  options?: object;
  onChange?: (value: string) => void;
  onMount?: (value: any) => void;
}
let editorOptions = {
  minimap: {enabled: false}
};
export function MonacoEditorComponent({
  language,
  height,
  width,
  value,
  options,
  theme,
  onChange,
  onMount
}: Props) {
  editorOptions = {...editorOptions, ...options};
  const onValueChange = useCallback(
    (e) => {
      onChange && onChange(e);
    },
    [onChange]
  );

  return (
    <div style={{height, width, border: '1px solid #eee', borderRadius: 6}}>
      <MonacoEditor
        value={value}
        options={editorOptions}
        language={language}
        theme={theme}
        onChange={onValueChange}
        onMount={onMount}
      />
    </div>
  );
}

interface DiffProps {
  language: string;
  height: string;
  width: string;
  value?: string;
  oriValue?: string;
  theme?: string;
  options?: object;
  onMount?: (value: any) => void;
}
export function MonacoEditorDiffComponent({
  height,
  width,
  oriValue,
  value
}: DiffProps) {
  return (
    <div
      style={{
        height,
        width,
        border: '1px solid #eee',
        overflowY: 'auto',
        background: '#F7F7F9'
      }}
      className="react-diff-wrap"
    >
      <ReactDiffViewer
        oldValue={oriValue}
        newValue={value}
        splitView={true}
        showDiffOnly={false}
        styles={{
          variables: {
            light: {
              emptyLineBackground: '#F7F7F9',
              diffViewerBackground: '#F7F7F9'
            }
          }
        }}
      />
      <div className="react-diff-wrap-line"></div>
    </div>
  );
}
