/**
 * 标签清理功能测试工具
 * 用于在浏览器控制台中快速测试标签清理功能
 */

import { cleanLabelName, smartCleanLabelName, getLabelDisplayInfo, processChartDataLabels } from './labelCleaner';
import { processSeriesDataLabels } from './chartLegendConfig';

/**
 * 测试标签清理功能
 */
export function testLabelCleaning() {
  console.log('=== 开始测试标签清理功能 ===');

  // 测试数据
  const testLabels = [
    'destination_service: ollama-service-v1.ollama.svc.cluster.local',
    'destination_service_name: ollama-service-v1',
    'destination_address: ************',
    'ai_cluster: outbound|80|user-ollama-service-v1.ollama.svc.cluster.local',
    'source_service: gateway-proxy.istio-system.svc.cluster.local',
    'cluster_name: outbound|80|user-service.default.svc.cluster.local',
    'destination_app: user-service-v2',
    'source_app: frontend-app',
    '状态码 200',
    '状态码 404',
    '状态码类 4xx',
    '代码 500',
    'normal-service-name'
  ];

  console.log('📝 测试标签列表:', testLabels);

  // 测试单个标签清理
  console.log('\n🔍 测试单个标签清理:');
  testLabels.forEach((label, index) => {
    console.log(`\n--- 测试 ${index + 1}: "${label}" ---`);
    
    const cleaned = cleanLabelName(label);
    const smart = smartCleanLabelName(label);
    const displayInfo = getLabelDisplayInfo(label);
    
    console.log(`基础清理: "${cleaned}"`);
    console.log(`智能清理: "${smart}"`);
    console.log(`显示信息:`, displayInfo);
  });

  // 测试系列数据处理
  console.log('\n📊 测试系列数据处理:');
  const testSeriesData = testLabels.map((label, index) => ({
    name: label,
    data: [10 + index, 20 + index, 30 + index]
  }));

  console.log('原始系列数据:', testSeriesData.map(s => s.name));

  const processedSeries = processSeriesDataLabels(testSeriesData, true);
  console.log('处理后系列数据:', processedSeries.map(s => ({
    display: s.name,
    original: s.originalName
  })));

  console.log('\n=== 标签清理功能测试完成 ===');
  
  return {
    original: testLabels,
    processed: processedSeries.map(s => ({
      original: s.originalName,
      display: s.name,
      changed: s.name !== s.originalName
    }))
  };
}

/**
 * 在浏览器控制台中运行测试
 * 使用方法：在控制台中输入 window.testLabelCleaning()
 */
if (typeof window !== 'undefined') {
  (window as any).testLabelCleaning = testLabelCleaning;
  console.log('💡 标签清理测试函数已注册到 window.testLabelCleaning()');

  // 立即运行一次测试
  console.log('🚀 自动运行标签清理测试...');
  testLabelCleaning();
}

/**
 * 快速测试特定标签
 */
export function quickTestLabel(label: string) {
  console.log(`🔍 快速测试标签: "${label}"`);
  
  const cleaned = cleanLabelName(label);
  const smart = smartCleanLabelName(label);
  const displayInfo = getLabelDisplayInfo(label);
  
  console.log(`基础清理: "${cleaned}"`);
  console.log(`智能清理: "${smart}"`);
  console.log(`显示信息:`, displayInfo);
  
  return {
    original: label,
    cleaned,
    smart,
    displayInfo
  };
}

// 也注册快速测试函数
if (typeof window !== 'undefined') {
  (window as any).quickTestLabel = quickTestLabel;
  console.log('💡 快速测试函数已注册到 window.quickTestLabel("your-label")');
}
