import moment, { Moment } from 'moment';

/**
 * 解析存储时长格式并转换为天数
 * @param retentionPeriod 存储时长字符串，如 "15d", "24h", "30m" 等
 * @returns 天数，如果解析失败返回 null
 */
export const parseRetentionPeriodToDays = (retentionPeriod: string): number | null => {
  if (!retentionPeriod || retentionPeriod === 'Unknown') {
    return null;
  }
  
  // 匹配数字+单位的格式
  const match = retentionPeriod.match(/^(\d+)([a-zA-Z]+)$/);
  if (!match) {
    return null;
  }
  
  const [, numberStr, unit] = match;
  const number = parseInt(numberStr, 10);
  
  // 将不同单位转换为天数
  switch (unit.toLowerCase()) {
    case 'd': // 天
      return number;
    case 'h': // 小时
      return Math.floor(number / 24);
    case 'm': // 分钟
      return Math.floor(number / (24 * 60));
    case 's': // 秒
      return Math.floor(number / (24 * 60 * 60));
    case 'w': // 周
      return number * 7;
    case 'M': // 月（按30天计算）
      return number * 30;
    case 'y': // 年（按365天计算）
      return number * 365;
    default:
      return null;
  }
};

/**
 * 根据存储时长计算最早可选日期
 * @param retentionPeriod 存储时长字符串
 * @returns 最早可选日期的 Moment 对象，如果无限制返回 null
 */
export const getEarliestSelectableDate = (retentionPeriod: string): Moment | null => {
  const days = parseRetentionPeriodToDays(retentionPeriod);
  if (days === null || days <= 0) {
    return null;
  }
  
  // 从当前时间向前推算指定天数
  return moment().subtract(days, 'days').startOf('day');
};

/**
 * 创建日期禁用函数，用于 DatePicker 的 disabledDate 属性
 * @param retentionPeriod 存储时长字符串
 * @returns 日期禁用函数
 */
export const createDisabledDateFunction = (retentionPeriod: string) => {
  const earliestDate = getEarliestSelectableDate(retentionPeriod);
  
  return (current: Moment): boolean => {
    if (!current) {
      return false;
    }
    
    // 禁用未来日期（超过今天）
    if (current.isAfter(moment(), 'day')) {
      return true;
    }
    
    // 如果有存储时长限制，禁用超出范围的历史日期
    if (earliestDate && current.isBefore(earliestDate, 'day')) {
      return true;
    }
    
    return false;
  };
};

/**
 * 检查预设时间范围是否超出存储限制
 * @param timeRange 预设时间范围标识，如 'hour_1', 'day_7' 等
 * @param retentionPeriod 存储时长字符串
 * @returns 是否超出限制
 */
export const isTimeRangeExceedsRetention = (timeRange: string, retentionPeriod: string): boolean => {
  const days = parseRetentionPeriodToDays(retentionPeriod);
  if (days === null) {
    return false; // 无限制
  }
  
  // 解析预设时间范围对应的天数
  let rangeDays = 0;
  switch (timeRange) {
    case 'hour_1':
      rangeDays = 1 / 24; // 1小时 = 1/24天
      break;
    case 'hour_6':
      rangeDays = 6 / 24; // 6小时 = 1/4天
      break;
    case 'day_1':
      rangeDays = 1;
      break;
    case 'day_7':
      rangeDays = 7;
      break;
    case 'day_14':
      rangeDays = 14;
      break;
    case 'day_28':
      rangeDays = 28;
      break;
    default:
      return false;
  }
  
  return rangeDays > days;
};

/**
 * 获取在存储限制内的有效预设时间范围列表
 * @param retentionPeriod 存储时长字符串
 * @returns 有效的时间范围标识数组
 */
export const getValidTimeRanges = (retentionPeriod: string): string[] => {
  const allRanges = ['hour_1', 'hour_6', 'day_1', 'day_7', 'day_14', 'day_28'];
  
  return allRanges.filter(range => !isTimeRangeExceedsRetention(range, retentionPeriod));
};
