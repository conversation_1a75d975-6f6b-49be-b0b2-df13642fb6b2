# 查询实例关联的集群列表接口

## 接口基本信息

- **接口说明**：查询AI网关实例已关联的CCE集群列表
- **接口地址**：`/api/aigw/v1/aigateway/instance/{instanceId}/clusterList`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | AI网关实例ID | i-a1b2c3d4 |

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 | 1 |
| pageSize | Integer | 否 | 每页显示条数，默认为10 | 10 |
| orderBy | String | 否 | 排序字段，默认为relationTime | relationTime |
| order | String | 否 | 排序方式，可选值：asc（升序）、desc（降序），默认为desc | desc |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "relationTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 1,
        "result": [
            {
                "clusterId": "cce-qhjz40ds",
                "clusterName": "zhoujun-user-1",
                "status": "RUNNING",
                "relationStatus": "ASSOCIATED",
                "remark": "测试环境集群",
                "relationTime": "2023-05-20 10:30:45",
                "updateTime": "2023-05-22 16:18:30",
                "ingressSettings": {
                    "enableIngress": false,
                    "enableAllIngressClasses": true,
                    "enableAllNamespaces": true,
                    "ingressClasses": [],
                    "watchNamespaces": []
                }
            }
        ]
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| page.orderBy | String | 排序字段 |
| page.order | String | 排序方式 |
| page.pageNo | Integer | 当前页码 |
| page.pageSize | Integer | 每页条数 |
| page.totalCount | Integer | 总记录数 |
| page.result | Array | 集群列表 |

### 结果列表字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| clusterId | String | 集群ID，唯一标识符 |
| clusterName | String | 集群名称 |
| status | String | 集群状态，可能的值：RUNNING（运行中）、CREATING（创建中）、CREATE_FAILED（创建失败）、DELETING（删除中）、DELETE_FAILED（删除失败）、MASTER_UPGRADING（主节点升级中）、MASTER_UPGRADE_FAILED（主节点升级失败）、ERROR（错误状态）、DELETED（已删除）、WARNING（访问异常） |
| relationStatus | String | 关联集群状态，可能的值：ASSOCIATING（关联中）、ASSOCIATED（已关联）、ERROR（关联失败） |
| remark | String | 集群关联备注，若无则显示"-" |
| relationTime | String | 关联时间，格式：YYYY-MM-DD HH:mm:ss |
| updateTime | String | 最近更新时间，格式：YYYY-MM-DD HH:mm:ss |
| ingressSettings | Object | Ingress配置信息 |
| ingressSettings.enableIngress | Boolean | 是否开启监听Ingress，默认关闭 |
| ingressSettings.enableAllIngressClasses | Boolean | 是否监听所有IngressClasses，默认为true |
| ingressSettings.enableAllNamespaces | Boolean | 是否监听所有命名空间，默认为true |
| ingressSettings.ingressClasses | Array | IngressClass数组，可为空，仅当enableAllIngressClasses为false时有效 |
| ingressSettings.watchNamespaces | Array | 监听的命名空间数组，可为空，仅当enableAllNamespaces为false时有效 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 确认用户是否有权限访问该实例 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/instance/i-a1b2c3d4/clusterList?pageNo=1&pageSize=10&orderBy=relationTime&order=desc
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "relationTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 2,
        "result": [
            {
                "clusterId": "cce-qhjz40ds",
                "clusterName": "zhoujun-user-1",
                "status": "RUNNING",
                "relationStatus": "ASSOCIATED",
                "remark": "测试环境集群",
                "relationTime": "2023-06-15 14:30:25",
                "updateTime": "2023-06-18 09:45:12",
                "ingressSettings": {
                    "enableIngress": true,
                    "enableAllIngressClasses": true,
                    "enableAllNamespaces": true,
                    "ingressClasses": [],
                    "watchNamespaces": []
                }
            },
            {
                "clusterId": "cce-g74gwis23",
                "clusterName": "zhoujun-user-2",
                "status": "CREATING",
                "relationStatus": "ASSOCIATING",
                "remark": "-",
                "relationTime": "2023-06-10 09:15:30",
                "updateTime": "2023-06-12 11:22:40",
                "ingressSettings": {
                    "enableIngress": false,
                    "enableAllIngressClasses": true,
                    "enableAllNamespaces": true,
                    "ingressClasses": [],
                    "watchNamespaces": []
                }
            }
        ]
    }
}
```

## 注意事项

1. 当集群没有关联备注时，remark字段将显示为"-"
2. 集群状态(status)反映了CCE集群的实际状态，包括：
   - RUNNING：集群运行中
   - CREATING：集群创建中
   - CREATE_FAILED：集群创建失败
   - DELETING：集群删除中
   - DELETE_FAILED：集群删除失败
   - MASTER_UPGRADING：主节点升级中
   - MASTER_UPGRADE_FAILED：主节点升级失败
   - ERROR：集群错误状态
   - DELETED：集群已删除
   - WARNING：集群访问异常
3. 关联集群状态(relationStatus)有三种：ASSOCIATING（关联中）、ASSOCIATED（已关联）、ERROR（关联失败）
4. 关联时间(relationTime)表示集群首次关联到实例的时间
5. 最近更新时间(updateTime)表示集群中服务最近一次更新的时间
6. 请求参数支持分页和排序，以便于在大量关联集群时进行有效管理 