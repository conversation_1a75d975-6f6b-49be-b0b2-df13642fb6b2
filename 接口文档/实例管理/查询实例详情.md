# 查询实例详情接口

## 接口基本信息

- **接口说明**：查询AI网关实例详细信息
- **接口地址**：`/api/aigw/v1/aigateway/{instanceId}`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": {
        "namespace": "istio-system-gw-ist9vvin",
        "instanceId": "gw-ist9vvin",
        "ingressStatus": "running",
        "ClusterInfo": {
            "clusterId": "cce-qhjz40ds",
            "clusterName": "my-cluster"
        },
        "internalIP": "************",
        "externalIP": "*************",
        "createTime": "2025-04-13 16:48:30",
        "region": "gz",
        "replicas": 2,
        "vpcCidr": "***********/16",
        "vpcId": "vpc-55dfhgtw0x",
        "subnetId": "sbn-suwftkzv543e",
        "gatewayType": "small",
        "publicAccessible": true,
        "deleteProtection": false,
        "name": "gateway-test",
        "description": "测试环境网关",
        "loadBalanceInfo": {
            "loadBalanceId": "lb-a1b2c3d4",
            "loadBalanceName": "aigw-gateway-test",
            "vpcId": "vpc-55dfhgtw0x",
            "vpcName": "默认VPC",
            "publicAddress": "*************",
            "privateAddress": "************",
            "protocol": "HTTP",
            "port": 80,
            "status": "running",
            "createTime": "2025-04-13 16:48:30"
        }
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 实例详情对象 |

### 实例详情字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| instanceId | String | 实例ID，系统生成的唯一标识符 |
| name | String | 实例名称 |
| namespace | String | 命名空间 |
| ingressStatus | String | 实例运行状态，可能的值：<br>- running：运行中<br>- creating：创建中<br>- initializing：初始化中<br>- adjusting：调整中<br>- releasing：释放中<br>- error：运行异常<br>- failed：创建失败 |
| region | String | 所属地域 |
| vpcId | String | 私有网络ID |
| vpcCidr | String | VPC网段 |
| subnetId | String | 子网ID |
| gatewayType | String | 网关规格，如small |
| replicas | Integer | 实例节点数量 |
| description | String | 实例描述，若无则为空字符串 |
| createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| internalIP | String | 私网接入地址，若无则为空字符串 |
| externalIP | String | 公网接入地址，若无则为空字符串 |
| publicAccessible | Boolean | 是否可公网访问 |
| deleteProtection | Boolean | 是否开启删除保护 |
| ClusterInfo | Object | 关联的集群信息，若无则为空对象 |
| ClusterInfo.clusterId | String | 集群ID |
| ClusterInfo.clusterName | String | 集群名称 |
| loadBalanceInfo | Object | 负载均衡信息 |
| loadBalanceInfo.loadBalanceId | String | 负载均衡ID |
| loadBalanceInfo.loadBalanceName | String | 负载均衡名称 |
| loadBalanceInfo.vpcId | String | 负载均衡所在VPC ID |
| loadBalanceInfo.vpcName | String | 负载均衡所在VPC名称 |
| loadBalanceInfo.publicAddress | String | 公网地址，若未开启公网访问则为空 |
| loadBalanceInfo.privateAddress | String | 私网地址 |
| loadBalanceInfo.protocol | String | 监听协议，如HTTP、HTTPS、TCP |
| loadBalanceInfo.port | Integer | 监听端口 |
| loadBalanceInfo.status | String | 负载均衡状态 |
| loadBalanceInfo.createTime | String | 负载均衡创建时间，格式：YYYY-MM-DD HH:mm:ss |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 确认用户是否有查询实例详情的权限 |
| 404 | 实例不存在 | 确认实例ID是否正确 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/gw-ist9vvin
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "namespace": "istio-system-gw-ist9vvin",
        "instanceId": "gw-ist9vvin",
        "ingressStatus": "running",
        "ClusterInfo": {
            "clusterId": "cce-qhjz40ds",
            "clusterName": "my-cluster"
        },
        "internalIP": "************",
        "externalIP": "*************",
        "createTime": "2025-04-13 16:48:30",
        "region": "gz",
        "replicas": 2,
        "vpcCidr": "***********/16",
        "vpcId": "vpc-55dfhgtw0x",
        "subnetId": "sbn-suwftkzv543e",
        "gatewayType": "small",
        "publicAccessible": true,
        "deleteProtection": false,
        "name": "gateway-test",
        "description": "测试环境网关实例",
        "loadBalanceInfo": {
            "loadBalanceId": "lb-a1b2c3d4",
            "loadBalanceName": "aigw-gateway-test",
            "vpcId": "vpc-55dfhgtw0x",
            "vpcName": "默认VPC",
            "publicAddress": "*************",
            "privateAddress": "************",
            "protocol": "HTTP",
            "port": 80,
            "status": "running",
            "createTime": "2025-04-13 16:48:30"
        }
    }
}
```

## 注意事项

1. 实例详情接口包含了比列表接口更详细的信息，特别是负载均衡信息
2. 当`publicAccessible`为`false`时，`externalIP`和`loadBalanceInfo.publicAddress`字段将为空字符串
3. 当没有关联集群时，`ClusterInfo`对象中的字段将为空字符串
4. 实例规格（`gatewayType`）与副本数（`replicas`）共同决定了实例的处理能力
5. 接入地址分为公网地址和私网地址，分别对应`externalIP`/`loadBalanceInfo.publicAddress`和`internalIP`/`loadBalanceInfo.privateAddress`
6. 删除保护功能（`deleteProtection`）开启时，实例不可被删除 