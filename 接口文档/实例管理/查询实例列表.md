# 查询实例列表接口

## 接口基本信息

- **接口说明**：查询AI网关实例列表
- **接口地址**：`/api/aigw/v1/aigateway/list`
- **请求方式**：GET
- **数据格式**：JSON

## 请求参数

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |

### 查询参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| pageNo | Integer | 否 | 页码，默认为1 | 1 |
| pageSize | Integer | 否 | 每页显示条数，默认为10 | 10 |
| orderBy | String | 否 | 排序字段，默认为createTime | createTime |
| order | String | 否 | 排序方式，可选值：asc（升序）、desc（降序），默认为desc | desc |
| keyword | String | 否 | 实例名称关键字，用于搜索 | gateway |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 1,
        "result": [
            {
                "instanceId": "i-abcdefgh",
                "name": "ai-gateway-0413",
                "ingressStatus": "running",
                "replicas": 0,
                "vpcCidr": "***********/16",
                "vpcId": "vpc-55dfhgtw0x",
                "subnetId": "sbn-suwftkzv543e",
                "gatewayType": "small",
                "internalIP": "************",
                "publicAccessible": false,
                "externalIP": "",
                "description": "生产环境订单系统网关",
                "createTime": "2023-04-13 10:30:45",
                "deleteProtection": true,
                "region": "gz",
                "namespace": "istio-system-gw-vnrdr4y4"
            }
        ]
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| page.orderBy | String | 排序字段 |
| page.order | String | 排序方式 |
| page.pageNo | Integer | 当前页码 |
| page.pageSize | Integer | 每页条数 |
| page.totalCount | Integer | 总记录数 |
| page.result | Array | 实例列表 |

### 结果列表字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| instanceId | String | 实例ID，系统生成的唯一标识符 |
| name | String | 实例名称 |
| ingressStatus | String | 实例运行状态，可能的值：<br>- running：运行中<br>- creating：创建中<br>- initializing：初始化中<br>- adjusting：调整中<br>- releasing：释放中<br>- error：运行异常<br>- failed：创建失败 |
| replicas | Integer | 实例节点数量 |
| vpcCidr | String | VPC网段 |
| vpcId | String | VPC ID |
| subnetId | String | 子网ID |
| gatewayType | String | 网关规格，如small |
| internalIP | String | 私网接入地址，若无则为空字符串 |
| publicAccessible | Boolean | 是否可公网访问 |
| externalIP | String | 公网接入地址，若无则为空字符串 |
| description | String | 实例描述，若无则为空字符串 |
| createTime | String | 创建时间，格式：YYYY-MM-DD HH:mm:ss |
| deleteProtection | Boolean | 是否开启删除保护 |
| region | String | 所属地域 |
| namespace | String | 命名空间 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 确认用户是否有查询实例列表的权限 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
GET /api/aigw/v1/aigateway/list?pageNo=1&pageSize=10&orderBy=createTime&order=desc
X-Region: gz
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "page": {
        "orderBy": "createTime",
        "order": "desc",
        "pageNo": 1,
        "pageSize": 10,
        "totalCount": 2,
        "result": [
            {
                "instanceId": "i-abcdefgh",
                "name": "ai-gateway-0413",
                "ingressStatus": "running",
                "replicas": 2,
                "vpcCidr": "***********/16",
                "vpcId": "vpc-55dfhgtw0x",
                "subnetId": "sbn-suwftkzv543e",
                "gatewayType": "small",
                "internalIP": "************",
                "publicAccessible": false,
                "externalIP": "",
                "description": "生产环境订单系统网关",
                "createTime": "2023-04-13 10:30:45",
                "deleteProtection": true,
                "region": "gz",
                "namespace": "istio-system-gw-vnrdr4y4"
            },
            {
                "instanceId": "i-12345678",
                "name": "ai-gateway-testing",
                "ingressStatus": "running",
                "replicas": 1,
                "vpcCidr": "**********/16",
                "vpcId": "vpc-8jfhyd62n",
                "subnetId": "sbn-t7jht5y8j",
                "gatewayType": "small",
                "internalIP": "************",
                "publicAccessible": true,
                "externalIP": "*************",
                "description": "测试环境网关",
                "createTime": "2023-04-10 14:25:10",
                "deleteProtection": false,
                "region": "gz",
                "namespace": "istio-system-gw-aqk38fj2"
            }
        ]
    }
}
```

## 注意事项

1. 实例状态为非"running"(非运行中)状态时，不可点击实例名称进入实例详情页面
2. 创建失败或运行异常状态的实例会返回相应的错误原因
3. 当开启删除保护(`deleteProtection`为`true`)时，实例不可被删除
4. 接入地址分为公网(`externalIP`)和私网(`internalIP`)，当`publicAccessible`为`false`时，公网地址为空
5. 排序默认按创建时间降序（最新创建的实例排在前面）
6. 实例列表支持通过关键字搜索实例名称 