import React, {useRef} from 'react';
import {Tooltip} from 'acud';

import styles from './index.module.less';

interface EllipsisWrapProps {
  width: number;
  data: string | undefined | React.ReactNode;
}

const EllipsisWrap: React.FC<EllipsisWrapProps> = ({width, data}) => {
  const divRef = useRef(null);
  const showTip = () => {
    return divRef?.current?.scrollWidth > divRef?.current?.clientWidth;
  };
  return (
    <Tooltip title={showTip() ? data : ''}>
      <div
        className={styles['eddipsis-wrap']}
        style={{
          width: width
        }}
        ref={divRef}
      >
        {data || '-'}
      </div>
    </Tooltip>
  );
};

export default EllipsisWrap;
