---
description: 
globs: 
alwaysApply: true
---

# 前端规范

# 前端团队代码规范指南

## 1. 项目结构规范

### 1.1 目录结构
```
|- project
    |- src
        |- assets        # 静态资源文件
            |- images    # 图片资源
            |- svgs      # SVG 组件
            |- medias    # 音视频资源
        |- components   # 组件目录
        |- constants    # 全局常量
        |- hooks        # 全局 Hooks
        |- pages        # 页面组件
        |- services     # API 服务
        |- stores       # 状态管理
        |- styles       # 全局样式
        |- utils        # 工具函数
```

### 1.2 就近原则
- 组件私有的资源（样式、工具函数、子组件等）应放置在组件目录下
- 示例结构：
```
|- components
    |- feature-card
        |- components     # 子组件
        |- hooks         # 私有 hooks
        |- utils         # 私有工具函数
        |- constants.ts  # 私有常量
        |- index.tsx     # 主组件
```

## 2. 命名规范

### 2.1 文件命名
- 目录和文件名使用驼峰命名法
- 组件目录/文件使用 PascalCase

### 2.2 代码命名
- 接口/类型：使用 PascalCase，以 I 开头
- 组件 Props 类型：以 Props 结尾
- 事件处理函数：使用 handle 前缀
- Props 回调函数：使用 on 前缀

## 3. 代码质量规范

### 3.1 组件编写规范
```typescript
const Component = ({ prop1, prop2 }: ComponentProps) => {
    // 1. 状态定义
    // 2. 派生数据
    // 3. 副作用
    // 4. 事件处理
    // 5. 渲染函数
    return (...)
}
```

### 3.2 样式处理
- 避免使用内联样式
- 禁止滥用 !important

### 3.3 数据处理
- 使用早期返回提高代码可读性
- 避免深层嵌套的可选链
- 必要时使用专门的工具库处理数据

### 3.4 性能优化
- 合理使用 useMemo/useCallback
- 避免不必要的重渲染

## 4. 工具使用规范

### 4.1 组件库使用
- 优先使用团队基础组件（acud 组件库）
- 自定义组件需要团队评审
- 禁止大量覆盖基础组件样式

## 5. 开发准则

### 5.1 代码质量原则
- 遵循 DRY（Don't Repeat Yourself）原则
- 优先考虑代码可读性，其次是性能优化
- 保持代码简洁，避免过度工程化
- 确保代码完整性，不遗留 TODO 或占位符

### 5.2 开发流程规范
- 编写代码前先进行详细的技术方案设计
- 使用伪代码描述实现逻辑，再进行具体编码
- 确保所有功能完整实现，不留未完成项
- 编写代码时必须包含必要的类型定义和导入声明

### 5.3 代码审查准则
- 验证功能完整性和正确性
- 检查组件命名的规范性和语义性
- 确认是否有遗漏的依赖项或类型定义