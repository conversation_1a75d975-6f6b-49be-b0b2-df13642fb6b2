import React from 'react';

interface ProductIntroIconProps {
  color?: string;
  className?: string;
}

const ProductIntroIcon: React.FC<ProductIntroIconProps> = ({ color = '#151B26', className }) => {
  return (
    <svg 
      width="16" 
      height="16" 
      viewBox="0 0 16 16" 
      xmlns="http://www.w3.org/2000/svg" 
      className={className}
    >
      <g fill="none" fillRule="evenodd" stroke={color} strokeLinejoin="round">
        <path d="M10 2H3v12h10V5zM5 6.5h4M5 9.5h6" />
      </g>
    </svg>
  );
};

export default ProductIntroIcon; 