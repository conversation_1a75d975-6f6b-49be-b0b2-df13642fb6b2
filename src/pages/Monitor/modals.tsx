import React, {FC, useCallback, useMemo, useState} from 'react';
import {Button, Form, Input, Modal, toast, Tooltip, Drawer, Alert, Select, Table} from 'acud';

import {
  createRegistrationNamespace,
  deleteRegistrationNamespace,
  getRegistrationNamespace,
  putRegistrationNamespace
} from '@/apis/instance';
import urls from '@/utils/urls';

interface EditModalProps {
  initialValues?: Record<string, any>;
  instanceId: string;
  refresh(): void;
}

export const EditModal: FC<EditModalProps> = ({
  initialValues = {},
  instanceId,
  refresh
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [formDataInstante] = Form.useForm();

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    formDataInstante.setFieldsValue({
      name: initialValues.name,
      comment: initialValues.comment
    });
  }, [formDataInstante, initialValues]);
  const handleCancel = useCallback(() => setIsModalVisible(false), []);

  const onClickOK = useCallback(
    async (e: React.MouseEvent<HTMLElement>) => {
      e.preventDefault();
      try {
        await formDataInstante.validateFields();
        const initialValues = formDataInstante.getFieldsValue();
        await putRegistrationNamespace({
          instanceId: instanceId,
          namespaces: [initialValues]
        });
        refresh();
        handleCancel();
      } catch (error) {
        console.error(error);
      }
    },
    [formDataInstante, refresh, instanceId, handleCancel]
  );
  return (
    <>
      <Tooltip
        trigger={initialValues.name === 'default' ? 'hover' : ''}
        title={'默认命名空间不支持编辑操作'}
      >
        <Button
          type="actiontext"
          disabled={initialValues.name === 'default'}
          onClick={showModal}
        >
          编辑
        </Button>
      </Tooltip>
      <Modal
        title="编辑命名空间"
        maskClosable={false}
        destroyOnClose
        onOk={onClickOK}
        onCancel={handleCancel}
        visible={isModalVisible}
      >
        <Form
          name="initialValues"
          labelAlign="left"
          form={formDataInstante}
          labelWidth="100px"
        >
          <Form.Item
            label="命名空间名称"
            name="name"
            rules={[{required: true, message: ''}]}
            initialValue={initialValues.name}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            rules={[{max: 1024, message: '描述长度不能超过1024个字符'}]}
            initialValue={initialValues.comment}
            name="comment"
            label="描述"
            extra="命名空间描述，不超过1024个字符"
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export const deleteConfirm =
  (record: Record<string, any>, instanceId: string, refresh: () => void) =>
  () => {
    const onOk = () => {
      return deleteRegistrationNamespace({
        instanceId: instanceId,
        namespaces: record.name
      })
        .then(() => {
          toast.success({
            message: '删除成功',
            duration: 5
          });
          refresh();
        })
        .catch(() =>
          toast.error({
            message: '删除失败，请稍后重试',
            duration: 5
          })
        );
    };

    Modal.confirm({
      title: '删除命名空间',
      content: `确定要删除该命名空间${record.name}吗？删除后无法恢复，请谨慎操作`,
      onOk
    });
  };

interface CreateModalProps {
  instanceId: string;
  refresh(): void;
}

export const CreateModal: FC<CreateModalProps> = ({refresh, instanceId}) => {
  // 使用导航到二级页面替代抽屉
  const navigateToCreateRoute = useCallback(() => {
    // 使用urls常量
    window.location.href = `#${urls.routeCreate}?instanceId=${instanceId}`;
  }, [instanceId]);
  
  return (
    <>
      <Button type="primary" onClick={navigateToCreateRoute}>
        创建路由
      </Button>
    </>
  );
};
