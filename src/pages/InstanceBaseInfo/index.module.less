.instance-detail-edit-modal{
  :global{
    .acud-modal-body{
      margin: 24px;
    }
  }

  .form-item{
    margin-bottom: 16px;
  }
  .last-form-item{
    margin-bottom: 0px;
  }
}


.detail-base-card {
  background: #fff;
  // box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  transition: all 0.3s;
  margin-bottom: 40px;

  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #151B26;
    margin-bottom: 16px;
  }

  &-content {
    width: 100%;
  }

  &-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }

  &-label {
    min-width: 60px;
    color: #5C5F66;
    margin-right: 4px;
  }

  &-value {
    color: rgba(21,27,38,0.95);
    display: flex;
    align-items: center;
    word-break: break-all;
    line-height: 20px;
  }

  &-line-wrap {
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 0px;
  }

  &-line {
    height: 1px;
    background: #e9eef6;
  }

  &-table-wrap {
    width: 100%;
  }
}

// 编辑图标样式
.edit-icon {
  cursor: pointer;
  margin-left: 8px;
  color: #0055CC;
  opacity: 0.7;
  transition: opacity 0.3s;
  width: 16px;
  
  &:hover {
    opacity: 1;
  }
}

// 编辑弹窗内容样式
.edit-popover {
  width: 100%;
  max-width: 300px;
  
  .error-text {
    color: #f5222d;
    font-size: 12px;
    margin-top: 4px;
  }
  
  .tip-text {
    color: #666;
    font-size: 12px;
    margin-top: 8px;
    margin-bottom: 12px;
  }
  
  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
  }
}

.detail-base-card-line {
  width: 100%;
  height: 1px;
  background-color: #e8e9eb;
}

.detail-base-card-line-wrap {
  padding: 0 32px;
}
