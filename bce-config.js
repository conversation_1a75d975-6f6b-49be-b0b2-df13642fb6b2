const {defineConfig} = require('@baidu/cba-cli');
const crypto = require('crypto');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

module.exports = defineConfig({
  appName: 'aigw',
  appTitle: '云原生网关',
  presets: ['@baidu/cba-preset-console-react'],
  babelOptions: {
    plugins: ['@babel/plugin-proposal-optional-chaining']
  },
  onlineConfigName: 'online-config.cce',
  proxyTarget: 'https://console.bce.baidu.com',
  i18n: {
    enabled: true,
    independent: false
  },
  // 自定义webpack，可选，默认一个入口 index.js/index.ts
  webpack: (config, merge) => {
    return merge(config, {
      devServer: {
        https: true,
        proxy: {
          '/api': {
            target: 'https://console.bce.baidu.com/',
            changeOrigin: true,
            secure: true,
            onProxyReq(proxyReq) {
              const hash = crypto.randomBytes(12).toString('hex').slice(0, 8);
              // 以下几行代码注释掉请求的就是线上的接口，不注释就是灰度线上的接口
              proxyReq.setHeader(
                'x-bce-request-id',
                // `${hash}-bb3f--aigw-bcecanarytag`
                 `${hash}-bb3f-test-aigw-bcecanarytag`
              );
              // 某些接口会验证origin
              proxyReq.setHeader('origin', 'https://console.bce.baidu.com');
            }
          }
        }
      },
      plugins: [
        new MonacoWebpackPlugin({
          languages: [
            'json',
            'javascript',
            'html',
            'css',
            'xml',
            'yaml',
            'java',
            'plaintext'
          ]
        })
      ]
    });
  }
});
