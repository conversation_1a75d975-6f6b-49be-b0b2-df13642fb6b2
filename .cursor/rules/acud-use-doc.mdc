---
description: 
globs: 
alwaysApply: true
---
> 本文为组件使用指南，当进行前端样式书写时，你需要优先将 prompt 中以=开头，=结尾中包裹的自然语言内容使用组件进行样式匹配，例如，当我在 prompt 中下达指令：点击创建=按钮=后，页面右侧会出现一个=抽屉=，标题为创建，抽屉中包含=表单=，总共有 2 个字段（用户名/密码），其中密码必填，另外表单项排列方式是水平。那么你需要查询按钮、抽屉、表单对应的组件名，然后使用这些组件作为前端样式，再根据 prompt 中的其他内容完善相应的字段。

你可以根据下表中的自然语言，查找对应的组件名（acud），如果有涉及到 icon 的你可以在 acud-icon 中查找，查找到对应的组件名后，可以看看说明，里面写了我的偏好和一些基本说明。另外，若有需要可以去 [acud组件属性说明.mdc](mdc:.cursor/rules/acud组件属性说明.mdc) 这个文档中参考对应的属性说明(不需要每次都参考)。

所有 toast 需要符合以下结构：
 toast.success({
        message: '成功提示文案',
        duration: 5,});

# 自然语言和组件名映射关系

| 自然语言                    | 组件名      | 说明                                                                                                                                                                                                                              |
| --------------------------- | ----------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 按钮                        | Button      | 1. 按钮有五种类型：主要按钮加强按钮普通按钮文字按钮强调按钮操作文字按钮。2. 添加 loading 属性即可让按钮处于加载状态。                                                                                                             |
| 带 icon 的按钮              | Button      | 当需要在 Button 内嵌入 Icon 时，可以在 Button 内使用 Icon 组件。 如果想控制 Icon 具体的位置，只能直接使用 Icon 组件，而非 icon 属性。                                                                                             |
| 超链接                      | Link        | 可以通过 size 控制超链接中的字体大小（small、medium、primary）                                                                                                                                                                    |
| 分割线                      | Divider     |                                                                                                                                                                                                                                   |
| 导航栏                      | Menu        |                                                                                                                                                                                                                                   |
| 面包屑                      | Breadcrumb  |                                                                                                                                                                                                                                   |
| 分 Step                     | Steps       | 引导用户按照流程完成任务的导航条。                                                                                                                                                                                                |
| 分页                        | Pagination  | 展现的信息行数过多时，可采用分页控制单页内的信息数量，也可帮助用户快速到定位某一内容的位置。                                                                                                                                      |
| 标签页                      | Tabs        |                                                                                                                                                                                                                                   |
| 下拉列表选择                | Select      | 弹出一个下拉菜单给用户选择操作，用于代替原生的选择器，或者需要一个更优雅的多选器时。                                                                                                                                              |
| 搜索                        | Search      | 用户需要在输入框中搜索时                                                                                                                                                                                                          |
| 多选                        | Checkbox    | 在一组可选项中进行多项选择时；<br> <br>单独使用可以表示两种状态之间的切换，和  `switch`  类似。区别在于切换  `switch`  会直接触发状态改变，而  `checkbox`  一般用于状态标记，需要和提交操作配合。                                 |
| 输入框                      | Input       | 需要用户输入表单域                                                                                                                                                                                                                |
| 步进器                      | InputNumber | 能让用户输入数值型内容，并进行上下调节数值。**我偏好加上 symmetryMode 开启左右对称模式**                                                                                                                                          |
| 单选                        | Radio       | **我偏好使用 Radio.Button 样式**                                                                                                                                                                                                  |
| 空状态                      | Empty       | 空状态时的展示占位图。                                                                                                                                                                                                            |
| 开关                        | Switch      |                                                                                                                                                                                                                                   |
| 表单                        | Form        | 高性能表单控件，自带数据域管理。包含数据录入、校验以及对应样式。**我偏好设置 layout="horizontal"**                                                                                                                                |
| 穿梭选择框                  | Transfer    |                                                                                                                                                                                                                                   |
| 抽屉                        | Drawer      | **我偏好使用可编辑抽屉**                                                                                                                                                                                                          |
| 鼠标 hover/点击出现气泡     | Popover     | 可以在气泡内操作。点击/鼠标移入元素，弹出气泡式的卡片浮层。**使用 trigger="hover" 将触发方式设置为鼠标移入**                                                                                                                      |
| 列表                        | Table       | 表格是⼀种对数据的结构化呈现⽅式，便于⽤户查看和编辑数据。                                                                                                                                                                        |
| 标签                        | Tag         | 表示目标对象某种属性的提示性控件，如状态、类别等。优先使用描边样式：color="active-outline"                                                                                                                                        |
| 鼠标 hover 提示文案         | Tooltip     | 仅作提示文案，无法操作。                                                                                                                                                                                                          |
| 高亮提示文案                | Alert       | 用于展示需要关注的信息，我偏好使用 <Alert message="提示通知的文案" type="info" showIcon />                                                                                                                                        |
| 弹窗                        | Modal       |                                                                                                                                                                                                                                   |
| 出现弹窗/对话框进行二次确认 | DialogBox   |                                                                                                                                                                                                                                   |
| 出现气泡进行确认            | Popconfirm  | 点击某些元素后，在元素附近出现气泡式的确认框                                                                                                                                                                                      |
| 右上角 toast 提示           | Toast       | 成功则使用 toast.success，包含：- `toast.success(config)`<br> <br>- `toast.error(config)`<br> <br>- `toast.info(config)`<br> <br>- `toast.warning(config)`<br> <br>- `toast.open(config)`<br> <br>- `toast.destroy(key?: String)` |
| 滑动输入条           | Slider      |                                                                                                                                                                                                                          |

