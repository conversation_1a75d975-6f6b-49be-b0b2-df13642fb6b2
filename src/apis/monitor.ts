import { request } from '@baidu/bce-react-toolkit';

// 监控指标查询参数接口
export interface MetricsQueryParams {
  instanceId: string;
  metricType: 'basic' | 'business'; // 根据接口文档修正
  query: string;
  start: string; // 根据接口文档，应该是字符串类型
  end: string;   // 根据接口文档，应该是字符串类型
  step: string;  // 根据接口文档，应该是字符串类型
}

// 监控数据响应接口 - 根据实际响应结构定义
// 响应结构: { success, status, result: { status, data, isPartial, stats, error } }
export interface MetricsQueryResponse {
  success: boolean;
  status: number;
  result: {
    status: string; // success 或 error
    data?: {
      resultType: string; // matrix、vector、scalar、string
      result: Array<{
        metric: Record<string, string>;
        values: Array<[number, string]>; // [timestamp, value]
      }>;
    };
    isPartial?: boolean;
    stats?: {
      seriesFetched: string;
    };
    error?: string; // 仅在result.status为error时返回
  };
}

/**
 * 查询监控指标数据
 * @param params 查询参数
 * @param region 地域代码，作为X-Region请求头传递
 * @returns Promise<any> 实际响应结构: { success, status, result: { status, data, isPartial, stats, error } }
 */
export const queryMetrics = (params: MetricsQueryParams, region: string): Promise<any> => {
  console.log('调用监控指标查询API:', params, 'region:', region);

  return request({
    url: `/api/aigw/v1/aigateway/${params.instanceId}/metrics/query_range`,
    method: 'GET',
    params: {
      metricType: params.metricType,
      query: params.query,
      start: params.start,
      end: params.end,
      step: params.step
    },
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('监控指标查询API响应:', response);
    return response;
  }).catch(error => {
    console.error('监控指标查询API错误:', error);
    throw error;
  });
};

/**
 * 获取可用的监控指标列表
 * @param instanceId 实例ID
 * @returns Promise<any>
 */
export const getAvailableMetrics = (instanceId: string): Promise<any> => {
  console.log('获取可用监控指标列表:', instanceId);

  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/metrics/labels`,
    method: 'GET'
  }).then(response => {
    console.log('获取监控指标列表响应:', response);
    return response;
  }).catch(error => {
    console.error('获取监控指标列表错误:', error);
    throw error;
  });
};

// 监控状态响应接口
export interface MonitorStatusResponse {
  success: boolean;
  result: {
    enabled: boolean;
    cpromInstanceId?: string;
  };
  status: number;
}

// CProm实例接口
export interface CPromInstance {
  instanceId: string;
  instanceName: string;
  region: string;
  metadata: {
    name: string;
    labels: {
      [key: string]: string;
    };
    annotations: any;
    creationTimestamp: string;
  };
  spec: {
    instanceID: string;
    instanceName: string;
    region: string;
    vmClusterConfig: {
      retentionPeriod: string;
    };
    grafanaConfig: {
      enable: boolean;
      adminPassword: string;
    };
    tags: any;
  };
  status: {
    phase: string;
    ready: boolean;
    message: string;
    accessEndpoint: {
      privateDomain: string;
      publicDomain: string;
    };
  };
  monitorGrafanaId: string;
  monitorGrafanaName: string;
}

// CProm实例列表响应接口
export interface CPromInstanceListResponse {
  success: boolean;
  result: CPromInstance[];
  status: number;
}

// CProm实例详情响应接口
export interface CPromInstanceDetailResponse {
  success: boolean;
  result: CPromInstance;
  status: number;
}

// 开启监控响应接口
export interface EnableMonitorResponse {
  success: boolean;
  result: {
    instanceId: string;
    cpromInstanceId: string;
    token: string;
    message: string;
  };
  status: number;
}

/**
 * 检查监控状态
 * @param instanceId AI网关实例ID
 * @param region 地域代码
 * @returns Promise<any>
 */
export const checkMonitorStatus = (instanceId: string, region: string): Promise<any> => {
  console.log('检查监控状态:', instanceId, 'region:', region);

  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/metrics/status`,
    method: 'GET',
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('检查监控状态响应:', response);
    return response;
  }).catch(error => {
    console.error('检查监控状态错误:', error);
    throw error;
  });
};

/**
 * 获取CProm实例列表
 * @param region 地域代码
 * @returns Promise<any>
 */
export const getCPromInstanceList = (region: string): Promise<any> => {
  console.log('获取CProm实例列表, region:', region);

  return request({
    url: '/api/aigw/v1/aigateway/cprom/instances',
    method: 'GET',
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('获取CProm实例列表响应:', response);
    return response;
  }).catch(error => {
    console.error('获取CProm实例列表错误:', error);
    throw error;
  });
};

/**
 * 获取CProm实例详情
 * @param instanceId CProm实例ID
 * @param region 地域代码
 * @returns Promise<any>
 */
export const getCPromInstanceDetail = (instanceId: string, region: string): Promise<any> => {
  console.log('获取CProm实例详情:', instanceId, 'region:', region);

  return request({
    url: `/api/aigw/v1/aigateway/cprom/instances/${instanceId}`,
    method: 'GET',
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('CProm实例详情响应:', response);
    return response;
  }).catch(error => {
    console.error('获取CProm实例详情错误:', error);
    throw error;
  });
};

/**
 * 开启业务指标监控
 * @param instanceId AI网关实例ID
 * @param cpromInstanceId CProm实例ID
 * @param region 地域代码
 * @returns Promise<any>
 */
export const enableMonitoring = (instanceId: string, cpromInstanceId: string, region: string): Promise<any> => {
  console.log('开启业务指标监控:', instanceId, cpromInstanceId, 'region:', region);

  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/metrics/${cpromInstanceId}/enable`,
    method: 'POST',
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('开启业务指标监控响应:', response);
    return response;
  }).catch(error => {
    console.error('开启业务指标监控错误:', error);
    throw error;
  });
};

/**
 * 关闭业务指标监控
 * @param instanceId AI网关实例ID
 * @param region 地域代码
 * @returns Promise<any>
 */
export const disableMonitoring = (instanceId: string, region: string): Promise<any> => {
  console.log('关闭业务指标监控:', instanceId, 'region:', region);

  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/metrics/disable`,
    method: 'POST',
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('关闭业务指标监控响应:', response);
    return response;
  }).catch(error => {
    console.error('关闭业务指标监控错误:', error);
    throw error;
  });
};

/**
 * 获取路由列表
 * @param instanceId AI网关实例ID
 * @param region 地域代码
 * @returns Promise<any>
 */
export const getRouteList = (instanceId: string, region: string): Promise<any> => {
  console.log('获取路由列表:', instanceId, 'region:', region);

  return request({
    url: `/api/aigw/v1/aigateway/cluster/${instanceId}/route`,
    method: 'GET',
    params: {
      pageSize: 1000
    },
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('获取路由列表响应:', response);
    return response;
  }).catch(error => {
    console.error('获取路由列表错误:', error);
    throw error;
  });
};

/**
 * 获取服务列表
 * @param instanceId AI网关实例ID
 * @param region 地域代码
 * @returns Promise<any>
 */
export const getServiceList = (instanceId: string, region: string): Promise<any> => {
  console.log('获取服务列表:', instanceId, 'region:', region);

  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/service/list`,
    method: 'GET',
    params: {
      pageSize: 1000
    },
    headers: {
      'X-Region': region
    }
  }).then(response => {
    console.log('获取服务列表响应:', response);
    return response;
  }).catch(error => {
    console.error('获取服务列表错误:', error);
    throw error;
  });
};
