import React, {useCallback, useEffect, useMemo, useState, useRef} from 'react';
import {useNavigate} from 'react-router-dom';
import {useFrameworkContext, useRegion} from '@baidu/bce-react-toolkit';
import ServiceSourceRadio from '@/components/ServiceSourceRadio';
import {
  Alert,
  Button,
  Form,
  Input,
  InputNumber,
  Popover,
  Radio,
  Select,
  Switch,
  Table,
  Tag,
  Tooltip,
  Checkbox,
  toast,
  Empty
} from 'acud';
import {
  OutlinedLeft,
  OutlinedQuestionCircle,
  OutlinedRefresh,
  MultiToneSuccess,
  MultiToneError
} from 'acud-icon';
import {useRequest} from 'ahooks';
import {get, isEmpty} from 'lodash';
import {
  createRegistrationInstance,
  getEsgInstanceList,
  getSubnetList,
  getVpcNetworkList,
  getVpcClusters,
  getInstanceList
} from '@/apis/instance';
import urls from '@/utils/urls';
import styles from './index.module.less';

// 修改接口定义，增加onBack可选属性
interface CreateInstanceProps {
  onBack?: () => void;
}

const CreateInstance: React.FC<CreateInstanceProps> = ({ onBack }) => {
  const {frameworkData} = useFrameworkContext();
  const {region} = useRegion();
  const [form] = Form.useForm();
  const [vpcList, setVpcList] = useState([]);
  const [subList, setSubList] = useState([]);
  const [subNetId, setSubNetId] = useState('');
  const [submitLoading, setLoading] = useState(false);
  const [formRegion, setFormRegion] = useState('bj'); // 添加表单地域状态

  // CProm实例相关状态
  const [cpromList, setCpromList] = useState<any[]>([]);
  const [cpromLoading, setCpromLoading] = useState(false);
  const navigate = useNavigate();
  
  // 处理表单值变化
  const onValuesChange = useCallback(
    // eslint-disable-next-line complexity
    (changedValues) => {
      console.log('表单值变化:', changedValues);
      const {vpcId, subnetId, region, nodeCount} = changedValues;
      if (vpcId) {
        console.log('VPC ID 已更新:', vpcId);
        form.setFieldsValue({
          subnetId: undefined,
          vpcId // 确保vpcId被正确设置
        });
      }
      if (subnetId) {
        setSubNetId(subnetId);
      }
      if (region) {
        setFormRegion(region);
        // 当地域变化时，清空VPC和子网的选择
        form.setFieldsValue({
          vpcId: undefined,
          subnetId: undefined
        });
      }
      if (nodeCount !== undefined) {
        console.log('节点数量已更新:', nodeCount);
      }
    },
    [form]
  );

  const {data: _esgInstanceList, loading: esgLoading} = useRequest(() =>
    getEsgInstanceList({
      pageNo: 1,
      pageSize: 1000
    })
  );

  const esgOptions = useMemo(() => {
    // 使用安全的方式获取结果列表
    const resultList = get(_esgInstanceList, 'result', []);
    return (
      resultList.map(
        ({esgId, name}: {name: string; esgId: string}) => ({
          label: `${name}(${esgId})`,
          value: esgId
        })
      ) ?? []
    );
  }, [_esgInstanceList]);

  // 注释掉requestIp声明和调用，以防止发送API请求
  // const {run: requestIp, data: ipData} = useRequest(getSubNetIpNumber, {
  //   manual: true
  // });

  // useEffect(() => {
  //   const subNetTotalData: any = get(ipData, 'result.subnets[0]', {});
  //   if (!isEmpty(subNetTotalData)) {
  //     const num = subNetTotalData.totalIps - subNetTotalData.usedIps;
  //     setIpNumber(num);
  //   }
  // }, [ipData]);

  // 获取CProm实例列表
  const requestCpromList = useCallback(async () => {
    if (!formRegion) return;

    setCpromLoading(true);
    try {
      const response = await fetch(`/api/aigw/v1/aigateway/cprom/instances`, {
        method: 'GET',
        headers: {
          'X-Region': formRegion,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.result)) {
          // 只显示运行中且就绪的实例
          const availableInstances = data.result.filter((instance: any) =>
            instance.status?.phase === 'Running' && instance.status?.ready === true
          );
          setCpromList(availableInstances);
        } else {
          console.error('获取CProm实例列表失败:', data.message);
          setCpromList([]);
        }
      } else {
        console.error('获取CProm实例列表请求失败:', response.status);
        setCpromList([]);
      }
    } catch (error) {
      console.error('获取CProm实例列表异常:', error);
      setCpromList([]);
    } finally {
      setCpromLoading(false);
    }
  }, [formRegion]);

  useEffect(() => {
    if (subNetId) {
      // requestIp(subNetId); // 注释掉API调用
    }
  }, [subNetId]);

  const {run: requestSubList, loading: subLoading} = useRequest(
    (params) => getSubnetList(params, formRegion), // 传递formRegion作为请求头
    {
      manual: true,
      onSuccess: (res) => {
        setSubList(res?.result || []);
      }
    }
  );

  const {loading: vpcLoading, run: requestVpcList} = useRequest(() => getVpcNetworkList({}, formRegion), {
    onSuccess: (res) => {
      setVpcList(res?.result || []);
    },
    refreshDeps: [formRegion] // 当选择的地域变化时，重新请求VPC列表
  });

  const vpcOptions = useMemo(() => {
    return (
      vpcList?.map(({vpcId, name, cidr}) => ({
        label: `${name}（${cidr}）`,
        value: vpcId
      })) ?? []
    );
  }, [vpcList]);

  const subnetOptions = useMemo(() => {
    return (
      subList?.map(({subnetId, name, cidr}) => ({
        label: `${name}（${cidr}）`,
        value: subnetId
      })) ?? []
    );
  }, [subList]);

  const goBack = () => {
    // 如果传入了onBack函数，则调用，否则使用原有的导航逻辑
    if (onBack) {
      onBack();
    } else {
      navigate({
        pathname: urls.registrationList
      });
    }
  };

  const onSubmit = useCallback(
    // 表单提交事件
    async () => {
      setLoading(true);
      try {
        // 验证表单 并拿到表单中的值
        const values = await form.validateFields();
        console.log('表单验证通过，获取到的数据:', values);
        
        const {
          name,
          vpcId,
          subnetId,
          region,
          description,
          deleteProtection,
          gatewaySpec,
          nodeCount,
          publicAccess,
          clusterRelation,
          clusterId,
          clusterName,
          aiObservabilityEnabled,
          cpromInstanceId
        } = values;
        
        // 格式校验：必须以字母或中文开头，支持字母、数字、中文、-、_、/、.，长度1-64字符
        const nameFormatRegex = /^[\u4e00-\u9fa5A-Za-z][\u4e00-\u9fa5A-Za-z0-9\-_/\.]{0,63}$/;
        if (!nameFormatRegex.test(name)) {
          toast.error({ message: '实例名称格式不符合要求：必须以字母或中文开头，支持字母、数字、中文、-、_、/、.，长度1-64字符', duration: 3 });
          setLoading(false);
          return;
        }
        
        // 同名校验
        const checkRes: any = await getInstanceList({ pageNo: 1, pageSize: 1, keyword: name }, region);
        if (checkRes.success && checkRes.page && Array.isArray(checkRes.page.result) && checkRes.page.result.length > 0 && checkRes.page.result[0].name === name) {
          toast.error({ message: '该实例名称已存在，请更换名称', duration: 3 });
          setLoading(false);
          return;
        }
        
        // 添加调试日志，特别检查 name 字段
        console.log('从表单获取的实例名称:', name);
        console.log('实例名称是否为空:', !name);
        console.log('实例名称类型:', typeof name);
        console.log('原始表单的 name 字段:', form.getFieldValue('name'));
        
        // 获取选中的VPC的CIDR
        const selectedVpc = (vpcList as any[]).find(vpc => vpc.vpcId === vpcId);
        const vpcCidr = selectedVpc?.cidr || '';
        console.log('获取到的VPC信息:', selectedVpc);
        console.log('VPC CIDR:', vpcCidr);
        
        // 构造符合接口要求的参数
        const param: any = {
          name, // 实例名称
          vpcId, // 私有网络ID
          vpcCidr, // VPC网段
          subnetId, // 子网ID
          gatewayType: gatewaySpec || 'small', // 网关规格，当前只支持small
          isInternal: publicAccess ? 'false' : 'true', // 按照接口文档，这是一个字符串值
          replicas: Number(nodeCount) || 2, // 节点数量，默认为2
          description: description || '', // 网关实例描述
          deleteProtection: deleteProtection !== false, // 默认为true
        };

        // 如果启用了AI可观测并且选择了CProm实例，添加到参数中
        if (aiObservabilityEnabled && cpromInstanceId) {
          param.cpromInstanceId = cpromInstanceId;
        }
        
        // 额外检查构建好的参数对象中是否包含 name
        console.log('构建的请求参数中的 name 字段:', param.name);
        
        // 如果选择了立即关联并且有集群ID，添加到请求参数中
        if (clusterRelation === 'immediate' && clusterId) {
          param.clusters = [
            {
              clusterId, 
              clusterName
            }
          ];
        } else {
          param.clusters = [];
        }
        
        // 如果地域信息存在，添加到请求中
        if (region) {
          param.region = region;
        }
        
        // 添加debug日志，查看请求参数
        console.log('创建实例请求参数:', param);
        
        // 调用创建接口
        const res = await createRegistrationInstance(param);
        console.log('创建实例响应:', res);
        
        if (res.success) {
          toast.success({
            message: '实例创建成功',
            duration: 3
          });
          goBack();
        } else {
          // 显示错误信息
          // 处理错误消息是对象的情况
          let errorMsg = '创建实例失败，请重试';
          const errorRes = res as any; // 使用类型断言
          if (errorRes.message) {
            if (typeof errorRes.message === 'string') {
              errorMsg = errorRes.message;
            } else if (typeof errorRes.message === 'object') {
              // 如果message是对象，尝试获取global字段或第一个可用的错误消息
              errorMsg = errorRes.message.global || Object.values(errorRes.message)[0] || errorMsg;
            }
          }
          
          toast.error({
            message: errorMsg,
            duration: 3
          });
        }
      } catch (err) {
        console.error('创建实例失败:', err);
        toast.error({
          message: '表单验证失败或创建过程中出现错误',
          duration: 3
        });
      } finally {
        setLoading(false);
      }
    },
    [form, goBack, vpcList]
  );

  useEffect(() => {
    // 当VPC ID变化时，触发表单重新渲染以更新关联信息模块状态
    const currentVpcId = form.getFieldValue('vpcId');
    console.log('监听到VPC ID变化:', currentVpcId);
    
    // 如果没有选择VPC，且关联方式是"立即关联"，则重置为"暂不关联"
    if (!currentVpcId && form.getFieldValue('clusterRelation') === 'immediate') {
      form.setFieldsValue({ clusterRelation: 'none' });
    }
  }, [form.getFieldValue('vpcId')]);

  return (
    <div className={`${styles['create-instance-wrap']} custom-layout`}>
      <div className={styles['create-instance-header']}>
        <Button
          className="common-back-btn"
          type="text"
          onClick={() => goBack()}
          icon={<OutlinedLeft />}
        >
          返回
        </Button>
        <h2 className="custom-header-title">创建网关实例（云原生网关）</h2>
      </div>
      <div className={styles['create-instance-content']}>
        <Form
          labelAlign="left"
          form={form}
          onValuesChange={onValuesChange}
          autoComplete="off"
          labelWidth="93px"
        >
          <BasicInfoModule form={form} frameworkData={frameworkData} />
          <SpecificationModule form={form} />
          <NetworkModule
            form={form}
            vpcLoading={vpcLoading}
            vpcOptions={vpcOptions}
            subLoading={subLoading}
            subnetOptions={subnetOptions}
            requestSubList={requestSubList}
            requestVpcList={requestVpcList}
            setSubList={setSubList}
          />
          <ObservabilityModule
            form={form}
            cpromList={cpromList}
            cpromLoading={cpromLoading}
            requestCpromList={requestCpromList}
          />
          <RelatedInfoModule
            form={form}
            vpcId={form.getFieldValue('vpcId')}
          />
        </Form>
      </div>
      <div className={styles['create-instance-footer']}>
        <div className={styles['create-instance-footer-content']}>
          <Button type="primary" onClick={onSubmit} loading={submitLoading}>
            创建
          </Button>
          <Button onClick={goBack}>取消</Button>
        </div>
      </div>
    </div>
  );
};

// 新增组件代码
// 基本信息模块组件
const BasicInfoModule = ({form, frameworkData}) => {
  const { region } = useRegion();
  // 设置默认区域为北京，从表单中获取当前值
  const [selectedRegion, setSelectedRegion] = useState(() => {
    const formRegion = form.getFieldValue('region');
    return formRegion || 'bj';
  });
  const [nameErrorList, setNameErrorList] = useState<string[]>([]);
  const [nameValidating, setNameValidating] = useState(false);
  const validateTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  // 添加状态跟踪当前输入值
  const [currentInput, setCurrentInput] = useState('');
  
  // 添加状态来跟踪每条规则的验证结果
  const [nameRules, setNameRules] = useState({
    length: false,  // 1-64位字符
    startChar: false, // 必须以字母或中文开头
    validChars: false // 仅限大小写字母、数字、中文以及-_/.特殊字符
  });
  
  // 添加状态来控制气泡显示
  const [popoverVisible, setPopoverVisible] = useState(false);

  // 组件挂载时设置表单的region值
  useEffect(() => {
    // 确保表单有初始值
    if (!form.getFieldValue('region')) {
      form.setFieldsValue({ region: selectedRegion });
    }
  }, []);

  // 当用户手动选择地域时，更新region值到表单
  const handleRegionChange = (e) => {
    const target = e.target;
    if (target && 'value' in target) {
      const regionValue = target.value as string;
      setSelectedRegion(regionValue);
      // 通过触发表单的onValuesChange更新父组件中的formRegion
      form.setFieldsValue({ region: regionValue });
    }
  };

  // 验证名称的各项规则
  const validateNameRules = (value: string) => {
    // 如果是空字符串，所有规则都不满足
    if (!value || value.trim() === '') {
      const emptyRules = {
        length: false,
        startChar: false,
        validChars: false
      };
      setNameRules(emptyRules);
      return false;
    }
    
    const rules = {
      length: value.length >= 1 && value.length <= 64,
      startChar: /^[a-zA-Z\u4e00-\u9fa5]/.test(value),
      validChars: /^[a-zA-Z0-9\u4e00-\u9fa5\-_\/\.]+$/.test(value)
    };
    
    setNameRules(rules);
    
    // 检查所有规则是否都满足
    return rules.length && rules.startChar && rules.validChars;
  };

  // 进行正则校验（同步校验）
  const validateNameFormat = (value: string): boolean => {
    // 按照接口文档：必须以字母或中文开头，支持大小写字母、数字、中文以及-_/.特殊字符，长度1-64
    return validateNameRules(value);
  };

  // 真实唯一性校验
  const validateInstanceName = async (name: string): Promise<boolean> => {
    if (!validateNameFormat(name)) {
      return true;
    }
    setNameValidating(true);
    try {
      const apiRes: any = await getInstanceList(
        { pageNo: 1, pageSize: 1, keyword: name },
        region
      );
      // 获取可能的结果数组：优先从 page.result，其次从 result
      const itemsFromPage = apiRes.page && Array.isArray(apiRes.page.result) ? apiRes.page.result : null;
      const itemsFromResult = apiRes.result && Array.isArray(apiRes.result) ? apiRes.result : null;
      const items: any[] = itemsFromPage || itemsFromResult || [];
      if (apiRes.success && items.length > 0 && items[0].name === name) {
        return false;
      }
      return true;
    } catch (error) {
      console.error('校验实例名称唯一性失败:', error);
      return true;
    } finally {
      setNameValidating(false);
    }
  };

  // 处理输入变化，实时校验
  const handleNameChange = useCallback(async (e) => {
    const value = e.target.value as string;
    setCurrentInput(value);
    form.setFieldsValue({ name: value });
    // 格式校验
    validateNameRules(value);
    // 唯一性校验
    const isUnique = await validateInstanceName(value);
    if (!isUnique) {
      setNameErrorList(['该实例名称已存在，请更换名称']);
    } else {
      setNameErrorList([]);
    }
  }, [form, validateNameFormat, validateInstanceName]);

  // 额外的useEffect确保输入变化时立即更新规则状态
  useEffect(() => {
    if (!currentInput || currentInput.trim() === '') {
      setNameRules({
        length: false,
        startChar: false,
        validChars: false
      });
      return;
    }
    validateNameRules(currentInput);
  }, [currentInput]);

  // 防抖处理函数
  const debouncedValidate = useCallback((value: string) => {
    if (validateTimerRef.current) {
      clearTimeout(validateTimerRef.current);
    }
    
    // 立即进行格式校验
    const isFormatValid = validateNameFormat(value);
    const errors: string[] = [];
    
    if (!isFormatValid) {
      errors.push('名称格式不符合要求：必须以字母或中文开头，支持字母、数字、中文、-、_、/、.，长度1-64字符');
      setNameErrorList(errors);
      return;
    }
    
    // 延迟进行唯一性校验
    setNameValidating(true);
    validateTimerRef.current = setTimeout(async () => {
      try {
        const isUnique = await validateInstanceName(value);
        if (!isUnique) {
          errors.push('该实例名称已存在，请更换名称');
        }
        setNameErrorList(errors);
      } catch (error) {
        console.error('Name validation error:', error);
      } finally {
        setNameValidating(false);
      }
    }, 300);
  }, []);

  // 失焦时完整校验
  const handleNameBlur = useCallback((e: React.FocusEvent<HTMLInputElement, Element>) => {
    // 首先确保气泡隐藏
    setPopoverVisible(false);
    
    try {
      const inputElement = e.target as HTMLInputElement;
      const value = inputElement.value || '';
      if (!value) {
        setNameErrorList([]);
        setNameRules({
          length: false,
          startChar: false,
          validChars: false
        });
        return;
      }
      
      // 直接调用debouncedValidate
      debouncedValidate(value);
    } catch (error) {
      console.error('Name validation error:', error);
    }
  }, [debouncedValidate]);
  
  // 气泡内容
  const nameRulesContent = (
    <div style={{ padding: '0px' }}>
      <div style={{ margin: '4px 0' }}>
        {nameRules.length ? <MultiToneSuccess /> : <MultiToneError />}
        <span style={{ marginLeft: '4px' }}>1-64位字符</span>
      </div>
      <div style={{ margin: '4px 0' }}>
        {nameRules.startChar ? <MultiToneSuccess /> : <MultiToneError />}
        <span style={{ marginLeft: '4px' }}>必须以字母或中文开头</span>
      </div>
      <div style={{ margin: '4px 0' }}>
        {nameRules.validChars ? <MultiToneSuccess /> : <MultiToneError />}
        <span style={{ marginLeft: '4px' }}>仅限大小写字母、数字、中文以及-_/.特殊字符</span>
      </div>
    </div>
  );

  return (
    <div className={styles["module-container"] + " " + styles["basic-info-module"]}>
      <div className={styles["module-title"]}>基本信息</div>
      <div className={styles["module-content"]}>
        <Form.Item label="地域" required name="region">
          <Radio.Group 
            defaultValue="bj" 
            value={selectedRegion} 
            onChange={handleRegionChange}
          >
            <Radio.Button value="bj" >华北 - 北京</Radio.Button>
            {/* <Radio.Button value="gz" >华南 - 广州</Radio.Button> */}
          </Radio.Group>
        </Form.Item>
        
        <Form.Item
          label="实例名称"
          name="name"
          required
          validateStatus={nameErrorList.length > 0 ? 'error' : ''}
          help={nameErrorList.length > 0 ? nameErrorList[0] : undefined}
          extra={
            <div style={{whiteSpace: 'nowrap'}}>
              <span>
                必须以字母或中文开头，支持大小写字母、数字、中文以及-_/.特殊字符
              </span>
            </div>
          }
        >
          <div style={{ display: 'flex', alignItems: 'flex-start'}}>
            <Input 
              placeholder="请输入实例名称" 
              style={{ width: 400 }}
              onChange={handleNameChange}
              onFocus={() => setPopoverVisible(true)}
              onBlur={handleNameBlur}
              suffix={null}
              limitLength={64}
            />
            <Popover
              content={nameRulesContent}
              trigger="manual"
              placement="right"
              overlayStyle={{ maxWidth: '320px' }}
              overlayInnerStyle={{ padding: '8px' }}
              visible={popoverVisible}
              mouseEnterDelay={0}
              mouseLeaveDelay={0.1}
            >
              <div style={{ width: 0, height: 32, display: 'inline-block' }}></div>
            </Popover>
          </div>
        </Form.Item>
        
        <Form.Item
          label="描述"
          name="description"
         
        >
          <Input.TextArea
            placeholder="请输入实例描述信息"
            style={{ width: 400 }}
            limitLength={64}
          />
        </Form.Item>
        
        <Form.Item
          label="删除保护"
          name="deleteProtection"
          valuePropName="checked"
          initialValue={true}
          extra={<div style={{whiteSpace: 'nowrap',marginTop:'-4px'}}>开启后，可防⽌通过控制台或 API 误删除⽹关</div>}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox defaultChecked={true} />
            <span style={{ marginLeft: 8 }}>开启保护</span>
            <Tag type="enhance" style={{marginLeft: 8, color: '#fff', backgroundColor: '#F33E3E',padding:'0 4px'}}>推荐开启</Tag>
          </div>
        </Form.Item>
      </div>
    </div>
  );
};

// 规格配置模块组件
const SpecificationModule = ({ form }) => {
  const [gatewaySpec, setGatewaySpec] = useState('small');
  
  // 确保组件挂载时设置表单的初始值
  useEffect(() => {
    // 如果表单中没有gatewaySpec值，则设置默认值
    if (!form.getFieldValue('gatewaySpec')) {
      form.setFieldsValue({ gatewaySpec: 'small' });
    }
  }, []);
  
  return (
    <div className={styles["module-container"] + " " + styles["spec-module"]}>
      <div className={styles["module-title"]}>规格配置</div>
      <div className={styles["module-content"]}>
        <Form.Item 
          label="网关规格" 
          name="gatewaySpec"
          // extra={
          //   <span style={{whiteSpace: 'nowrap'}}>
          //     最⼤并发数连接数 50000，每秒新建连接数 5000，每秒查询数 5000，带宽上限 1Gbps;
          //     <br/>
          //     最⼤集群关联数 1，最⼤服务管理数 300
          //   </span>
          // }
          required
          initialValue="small"
        >
          <Radio.Group 
            defaultValue="small" 
            value={gatewaySpec} 
            onChange={(e) => {
              const target = e.target;
              if (target && 'value' in target) {
                const value = target.value as string;
                setGatewaySpec(value);
                // 确保值被正确设置到表单中
                form.setFieldsValue({ gatewaySpec: value });
              }
            }}
          >
            <Radio.Button value="small">小型</Radio.Button>
          </Radio.Group>
        </Form.Item>
        
        <Form.Item 
          label="节点数量" 
          name="nodeCount"
          required
          initialValue={2}
          extra="为了保证网关的高可用性，建议设置最少 2 个节点"
          style={{display: 'none'}}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
          <InputNumber 
            min={1} 
            max={5} 
            defaultValue={2}
            step={1} 
            precision={0}  
            symmetryMode 
            onChange={(value) => {
              console.log('节点数量变更为:', value);
              // 确保值被正确设置到表单中
              form.setFieldsValue({ nodeCount: value });
            }}
          />
          </div>
        </Form.Item>
      </div>
    </div>
  );
};

// 网络配置模块组件
const NetworkModule = ({form, vpcLoading, vpcOptions, subLoading, subnetOptions, requestSubList, requestVpcList, setSubList}) => {
  const [publicNetworkEnabled, setPublicNetworkEnabled] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0); // 添加强制更新状态
  const [selectedSubnetId, setSelectedSubnetId] = useState(form.getFieldValue('subnetId')); // 添加本地状态跟踪子网选择
  
  // 组件挂载时初始化状态
  useEffect(() => {
    // 同步表单中的publicAccess值到组件状态
    const publicAccess = form.getFieldValue('publicAccess');
    if (publicAccess !== undefined) {
      setPublicNetworkEnabled(publicAccess);
    }
    
    // 同步表单中的subnetId值到本地状态
    const subnetId = form.getFieldValue('subnetId');
    if (subnetId !== undefined) {
      setSelectedSubnetId(subnetId);
    }
  }, []);
  
  // 当表单子网值变化时，更新本地状态
  useEffect(() => {
    const subnetId = form.getFieldValue('subnetId');
    setSelectedSubnetId(subnetId);
  }, [form.getFieldValue('subnetId'), forceUpdate]);
  
  // 添加表单值变化的监听
  useEffect(() => {
    console.log('当前选择的VPC:', form.getFieldValue('vpcId'));
  }, [form.getFieldValue('vpcId'), forceUpdate]); // 将forceUpdate添加到依赖项
  
  return (
    <div className={styles["module-container"] + " " + styles["network-module"]}>
      <div className={styles["module-title"]}>网络配置</div>
      <div className={styles["module-content"]}>
        <div className={styles["network-group"]}>
          <Form.Item label={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>私有网络：</span>
              <Tooltip title="请选择目标 CCE 集群所在的私有网络">
                <OutlinedQuestionCircle style={{ marginLeft: '4px', color: '#999', cursor: 'pointer' }} />
              </Tooltip>
            </div>
          } required>
            <Form.Item
              name="vpcId"
              extra={
                <span>
                  <span style={{ color: '#ff9325' }}>创建后不⽀持修改。</span>
                  如需创建私有⽹络，可以 <a href="https://console.bce.baidu.com/network/#/vpc/instance/create" target="_blank" rel="noopener noreferrer">去创建私有⽹络</a>
                </span>
              }
              rules={[{required: true, message: '请选择私有网络'}]}
              // noStyle
            >
              <div className={styles["vpc-selection"]} style={{width: 'fit-content'}}>
              <Select
                loading={vpcLoading}
                options={vpcOptions}
                placeholder="请选择VPC网络"
                style={{width: 400}}
                value={form.getFieldValue('vpcId')}
                onChange={(val) => {
                  console.log('选择的VPC ID:', val);
                  // 显式设置表单字段值，同时清空子网选择
                  form.setFieldsValue({ 
                    vpcId: val,
                    subnetId: undefined  // 重置子网选择
                  });
                  // 然后请求子网列表
                  requestSubList({
                    vpcId: val
                  });
                }}
              />
               <Button 
              icon={<OutlinedRefresh />}
              onClick={() => {
                // 清空私有网络和子网的表单值
                form.setFieldsValue({ 
                  vpcId: undefined, 
                  subnetId: undefined 
                });
                // 同时清空子网本地状态
                setSelectedSubnetId(undefined);
                // 重新获取VPC列表
                requestVpcList();
                // 清空子网列表
                setSubList([]);
                // 强制触发UI更新
                setForceUpdate(prev => prev + 1);
                // 显示Toast提示
                toast.success({
                  message: '已重新获取VPC列表',
                  duration: 3
                });
              }}
            />
            </div>
            </Form.Item>
            
           
        
          </Form.Item>
          
          <Form.Item
            label="子网"
            required
          >
            <div>
              <Form.Item
                name="subnetId"
                rules={[{required: true, message: '请选择子网'}]}
                extra={
                  <span className={styles["subnet-ip-count"]}>
                   
                      <span>如需创建⼦⽹，可以 <a href="https://console.bce.baidu.com/network/#/vpc/subnet/create" target="_blank" rel="noopener noreferrer">去创建⼦⽹</a></span> 
                      
                    
                  </span>
                }
              >
                <div className={styles["vpc-selection"]} style={{width: 'fit-content'}}>
                <Select
                  loading={subLoading}
                  options={subnetOptions}
                  placeholder="请选择子网"
                  style={{width: 400}}
                  value={selectedSubnetId}
                  onChange={(val) => {
                    // 同时更新表单和本地状态
                    form.setFieldsValue({ subnetId: val });
                    setSelectedSubnetId(val);
                  }}
                />
                <Button 
                  icon={<OutlinedRefresh />}
                  onClick={() => {
                    // 同时清空表单和本地状态的子网选择
                    form.setFieldsValue({ subnetId: undefined });
                    setSelectedSubnetId(undefined);
                    
                    // 强制刷新组件状态
                    setForceUpdate(prev => prev + 1);
                    
                    // 重新请求子网列表
                    const currentVpcId = form.getFieldValue('vpcId');
                    if (currentVpcId) {
                      requestSubList({
                        vpcId: currentVpcId
                      });
                      
                      // 显示Toast提示
                      toast.success({
                        message: '已重新获取子网列表',
                        duration: 3
                      });
                    } else {
                      // 如果没有选择VPC，提示用户
                      toast.warning({
                        message: '请先选择私有网络',
                        duration: 3
                      });
                    }
                  }}
                />
                </div>
              </Form.Item>
              
              
            </div>
          </Form.Item>
        </div>
        
        <Form.Item
          label="公网访问"
          name="publicAccess"
          valuePropName="checked"
          extra= {<div style={{whiteSpace: 'nowrap',marginTop:'-4px'}}>开启公网访问，会自动为网关创建公网接入地址，若不开启则仅支持私有网络访问</div>}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox onChange={(e) => {
              const checked = e.target.checked;
              setPublicNetworkEnabled(checked);
              // 确保值被正确设置到表单中
              form.setFieldsValue({ publicAccess: checked });
            }} />
            <span style={{ marginLeft: 8 }}>开启公网</span>
          </div>
        </Form.Item>
        
        

        {/* <Form.Item
          label="企业安全组"
          name="esgId"
          extra="安全组用于控制网关实例的网络访问控制，建议选择默认安全组"
        >
          <Select
            placeholder="请选择安全组"
            style={{width: 300}}
            options={[
              { label: '默认安全组(sg-default)', value: 'sg-default' },
              { label: '安全组1(sg-001)', value: 'sg-001' },
              { label: '安全组2(sg-002)', value: 'sg-002' }
            ]}
          />
        </Form.Item> */}
      </div>
    </div>
  );
};

// 关联信息模块组件
const RelatedInfoModule = ({form, vpcId}) => {
  // 状态用于跟踪是否显示Popover
  const [clusterRelationOption, setClusterRelationOption] = useState(() => {
    // 从表单中获取初始值，如果没有则默认为'none'
    return form.getFieldValue('clusterRelation') || 'none';
  });
  
  // 添加集群列表状态管理
  const [clusterListVisible, setClusterListVisible] = useState(false);
  const [clusterList, setClusterList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedClusterId, setSelectedClusterId] = useState(() => {
    // 从表单中获取初始值
    return form.getFieldValue('clusterId') || '';
  });
  const [selectedClusterName, setSelectedClusterName] = useState(() => {
    // 从表单中获取初始值
    return form.getFieldValue('clusterName') || '';
  });
  
  // 分页相关状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  
  // 确保组件挂载时设置表单的初始值
  useEffect(() => {
    // 设置默认值
    if (!form.getFieldValue('serviceSource')) {
      form.setFieldsValue({ serviceSource: 'cce' });
    }
    if (!form.getFieldValue('clusterRelation')) {
      form.setFieldsValue({ clusterRelation: 'none' });
    }
    
    // 如果表单已经有集群ID和名称，设置选中状态
    const clusterId = form.getFieldValue('clusterId');
    const clusterName = form.getFieldValue('clusterName');
    if (clusterId && clusterName) {
      setSelectedClusterId(clusterId);
      setSelectedClusterName(clusterName);
    }
  }, []);
  
  // 监听vpcId变化，如果vpcId为空且当前选择的是立即关联，则切换为暂不关联
  useEffect(() => {
    if (!vpcId && clusterRelationOption === 'immediate') {
      setClusterRelationOption('none');
      form.setFieldsValue({ clusterRelation: 'none' });
      setClusterListVisible(false);
    }
  }, [vpcId]);
  
  // 监听关联选项变化
  useEffect(() => {
    console.log('关联方式发生变化:', clusterRelationOption, '当前VPC:', vpcId);
    
    if (clusterRelationOption === 'immediate' && vpcId) {
      // 如果选择了立即关联，并且已经选择了VPC，则获取集群列表
      fetchClusterList();
      setClusterListVisible(true);
    } else {
      // 否则隐藏集群列表
      setClusterListVisible(false);
      // 如果选择暂不关联，清空已选择的集群
      if (clusterRelationOption === 'none') {
        setSelectedClusterId('');
        setSelectedClusterName('');
        form.setFieldsValue({ 
          clusterId: undefined, 
          clusterName: undefined 
        });
      }
    }
  }, [clusterRelationOption, vpcId]);
  
  // 获取集群列表
  const fetchClusterList = async () => {
    if (!vpcId) return;
    
    setLoading(true);
    try {
      const region = form.getFieldValue('region') || 'bj';  // 获取当前选择的地域
      console.log('查询同VPC中的CCE集群, vpcId:', vpcId, 'region:', region);
      
      // 调用API获取集群列表
      const response = await getVpcClusters(vpcId);
      console.log('获取同VPC中的CCE集群成功:', response);
      
      if (response?.success) {
        const result = response.result || [];
        setClusterList(result);
        setPagination({
          ...pagination,
          total: result.length
        });
        
        // 如果有已选中的集群ID，需要检查它是否在返回的列表中
        if (selectedClusterId) {
          const clusterExists = result.some(cluster => cluster.clusterId === selectedClusterId);
          if (!clusterExists) {
            // 如果已选择的集群不在列表中，清空选择
            setSelectedClusterId('');
            setSelectedClusterName('');
            form.setFieldsValue({ 
              clusterId: undefined, 
              clusterName: undefined 
            });
            console.log('已选择的集群不在可用列表中，已清空选择');
          }
        }
      } else {
        setClusterList([]);
        setPagination({
          ...pagination,
          total: 0
        });
      }
    } catch (error) {
      console.error('获取集群列表失败:', error);
      setClusterList([]);
      setPagination({
        ...pagination,
        total: 0
      });
      
      // 显示错误提示
      toast.error({
        message: '获取集群列表失败，请重试',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理关联方式变更
  const handleRelationChange = (value) => {
    console.log('关联方式变更:', value);
    
    setClusterRelationOption(value);
    form.setFieldsValue({ clusterRelation: value });
    
    // 如果选择立即关联，但VPC为空，则提示用户
    if (value === 'immediate' && !vpcId) {
      toast.warning({
        message: '请先选择私有网络后再进行关联',
        duration: 3
      });
      // 重置为暂不关联
      setTimeout(() => {
        setClusterRelationOption('none');
        form.setFieldsValue({ clusterRelation: 'none' });
      }, 0);
      return;
    }
  };
  
  // 选择集群
  const handleSelectCluster = (clusterId: string, clusterName: string) => {
    setSelectedClusterId(clusterId);
    setSelectedClusterName(clusterName);
    
    // 确保值被正确设置到表单中
    form.setFieldsValue({ 
      clusterId,
      clusterName,
      // 确保关联方式为立即关联
      clusterRelation: 'immediate'
    });
    
    // 设置选中状态到组件状态
    setClusterRelationOption('immediate');
    
    // 显示选择成功提示
    // toast.success({
    //   message: `已选择集群: ${clusterName}`,
    //   duration: 3
    // });
    
    console.log('已选择集群:', clusterId, clusterName);
  };
  
  // 处理分页变化
  const handleTableChange = (pagination) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: clusterList.length
    });
  };
  
  // 控制立即关联是否可选
  const isRelateDisabled = !vpcId;
  
  // 刷新集群列表
  const refreshClusterList = () => {
    fetchClusterList();
    toast.success({
      message: '已刷新集群列表',
      duration: 3
    });
  };
  
  // 集群列表列定义
  const columns = [
    {
      title: '',
      key: 'action',
      width: 50,
      render: (_, record: any) => (
        <Radio 
          checked={selectedClusterId === record.clusterId} 
          onChange={() => handleSelectCluster(record.clusterId, record.clusterName)}
        />
      )
    },
    {
      title: '集群名称/ID',
      dataIndex: 'clusterName',
      key: 'clusterName',
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start'}}>
          <Tooltip title={text}>
            <a
              href={`https://console.bce.baidu.com/cce/#/cce/cluster/detail?clusterUuid=${record.clusterId}&clusterName=${encodeURIComponent(record.clusterName)}`}
              target="_blank"
              rel="noreferrer"
              style={{
                color: '#2468f2',
                cursor: 'pointer',
                display: 'inline-block',
                maxWidth: '100%',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.clusterName}
            </a>
          </Tooltip>
          <div style={{ color: '#151B26', fontSize: '12px' }}>{record.clusterId}</div>
        </div>
      )
    },
    {
      title: '运行状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let statusClass = '';
        let text = status;
        
        if (status === 'RUNNING') {
          statusClass = 'status-success';
          text = '运行中';
        } else if (status === 'CREATING') {
          statusClass = 'status-processing';
          text = '创建中';
        } else if (status === 'ERROR') {
          statusClass = 'status-error';
          text = '异常';
        } else if (status === 'DELETING') {
          statusClass = 'status-warning';
          text = '删除中';
        } else if (status === 'DELETED') {
          statusClass = 'status-inactive';
          text = '已删除';
        } else if (status === 'MASTER_UPGRADING') {
          statusClass = 'status-processing';
          text = '升级中';
        } else if (status === 'MASTER_UPGRADE_FAILED') {
          statusClass = 'status-error';
          text = '升级失败';
        } else {
          statusClass = 'status-inactive';
        }
        
        return (
          <Tag color="transparent" icon={<span className={`circle ${statusClass}`}></span>}>
            {text}
          </Tag>
        );
      }
    },
    {
      title: 'VPC网段',
      dataIndex: 'vpcCidr',
      key: 'vpcCidr'
    },
    {
      title: '地域',
      dataIndex: 'region',
      key: 'region',
      render: (region: string) => {
        const regionMap = {
          'bj': '华北 - 北京',
          'gz': '华南 - 广州'
        };
        return regionMap[region] || region;
      }
    }
  ];
  
  return (
    <div className={styles["module-container"] + " " + styles["related-info-module"]}>
      <div className={styles["module-title"]}>关联信息（可选）</div>
      <div className={styles["module-content"]}>
        {/* 服务来源字段 */}
        <Form.Item 
          label="服务来源"
          name="serviceSource"
          initialValue="cce"
        >
          <div className={styles["service-source-container"]} style={{display: 'flex', alignItems: 'center'}}>
           <ServiceSourceRadio />
          </div>
        </Form.Item>
        
        {/* 关联容器集群字段 */}
        <Form.Item
          label="关联容器集群"
          name="clusterRelation"
          initialValue="none"
        >
          <div>
            <Radio.Group 
              value={clusterRelationOption}
              onChange={(e) => {
                // 确保e.target不为null，并且value属性存在
                if (e && e.target && 'value' in e.target) {
                  const value = e.target.value;
                  handleRelationChange(value);
                }
              }}
            >
              <Radio.Button
                value="immediate"
                disabled={isRelateDisabled}
              >
                立即关联
              </Radio.Button>
              <Radio.Button value="none">
                暂不关联
              </Radio.Button>
            </Radio.Group>
            
           
          </div>
        </Form.Item>
        
        {/* 隐藏的表单项用于保存选中的集群ID和名称 */}
        <Form.Item name="clusterId" style={{ display: 'none' }} />
        <Form.Item name="clusterName" style={{ display: 'none' }} />
        
        {/* 集群列表部分 */}
        {clusterListVisible && (
          <>
          <Form.Item  label="">
            <Alert
              type="info"
              message="将与⽹关⽹络互通的集群关联，使⽹关可以直接访问集群内信息（关联时请您确保⽬标集群已创建有效的kubeconfig)"
              showIcon
              style={{ marginTop: '-16px', width: '700px' }}
            />
            </Form.Item>
            <Form.Item label="选择集群">
                  <Table className="cluster-table-fixed-width"
                  tableLayout="fixed"
                  size="small"
                    rowKey="clusterId"
                    dataSource={clusterList}
                    columns={columns}
                    loading={loading}
                    pagination={{ 
                      current: pagination.current,
                      pageSize: pagination.pageSize,
                      total: pagination.total,
                      // showSizeChanger: true,
                      showTotal: (total) => `共 ${total} 条`,
                      // pageSizeOptions: ['10']
                    }}
                    locale={{ 
                      emptyText: (
                        <Empty
                          style={{ 
                            margin: '40px 0',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center'
                          }}
                          description={
                            <span>
                              暂无集群，
                              可以 <a href='https://console.bce.baidu.com/cce/#/cce/cluster/list' target='_blank'>去创建 CCE 集群</a>
                            </span>
                          }
                        />
                      )
                    }}
                   
                    onChange={handleTableChange}
                    onRow={(record) => ({
                      onClick: () => handleSelectCluster(record.clusterId, record.clusterName),
                      // 添加选中行样式
                      style: record.clusterId === selectedClusterId 
                        ? { background: '#f0f5ff' } 
                        : {}
                    })}
                  />
               
            </Form.Item>
          </>
        )}
      </div>
    </div>
  );
};

// 可观测配置模块组件
const ObservabilityModule = ({ form, cpromList, cpromLoading, requestCpromList }) => {
  const [aiObservabilityEnabled, setAiObservabilityEnabled] = useState(false);

  // 监听表单中的aiObservabilityEnabled字段变化
  useEffect(() => {
    const enabled = form.getFieldValue('aiObservabilityEnabled');
    if (enabled !== undefined) {
      setAiObservabilityEnabled(enabled);
    }
  }, [form]);

  // 处理AI可观测开关变化
  const handleAiObservabilityChange = (checked: boolean) => {
    setAiObservabilityEnabled(checked);
    form.setFieldsValue({
      aiObservabilityEnabled: checked,
      cpromInstanceId: undefined // 关闭时清空选择的实例
    });

    // 如果开启且没有CProm实例列表，则获取列表
    if (checked && cpromList.length === 0) {
      requestCpromList();
    }
  };

  // 处理CProm实例选择变化
  const handleCpromInstanceChange = (value: string) => {
    form.setFieldsValue({ cpromInstanceId: value });
  };

  // 处理刷新按钮点击
  const handleRefresh = () => {
    requestCpromList();
  };

  // 生成CProm实例选项
  const cpromOptions = cpromList.map((instance: any) => {
    const retentionPeriod = instance.spec?.vmClusterConfig?.retentionPeriod || '未知';
    // 将存储时长格式化为中文显示，如 "15d" -> "15天"
    const formattedRetention = retentionPeriod === '未知'
      ? '未知'
      : retentionPeriod.replace(/(\d+)d/g, '$1天');

    return {
      label: `${instance.instanceName}（${instance.instanceId}）（${formattedRetention}）`,
      value: instance.instanceId
    };
  });

  return (
    <div className={styles["module-container"] + " " + styles["observability-module"]}>
      <div className={styles["module-title"]}>可观测配置</div>
      <div className={styles["module-content"]}>
        <Form.Item
          label="AI 可观测"
          name="aiObservabilityEnabled"
          valuePropName="checked"
          initialValue={false}
        >
          <div style={{ display: 'flex', flexDirection: 'column',whiteSpace: 'nowrap'}}>
            <div style={{ display: 'flex', alignItems: 'center',minHeight:'32px'}}>
              <Checkbox
                checked={aiObservabilityEnabled}
                onChange={(e) => handleAiObservabilityChange(e.target.checked)}
              />
              <span style={{ marginLeft: 8 }}>使用 Prometheus 监控服务（CProm）</span>
            </div>
            <div style={{ color: '#84868C', fontSize: '12px', lineHeight: '20px' ,marginTop:'-4px'}}>
              提供 AI 相关指标采集、海量存储、监控告警等功能，该功能单独计费。<a href='https://cloud.baidu.com/doc/CProm/s/Gl0gcsh46'>Prometheus 监控服务费用详情</a>
            </div>
          </div>
        </Form.Item>

        {aiObservabilityEnabled && (
          <Form.Item
            label="CProm 实例"
            name="cpromInstanceId" 
            inputMaxWidth={'100%'}
            rules={[{ required: true, message: '请选择Prometheus监控实例' }]}
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px'}}>
                <Select
                  style={{ width: 400 }}
                  placeholder="请选择 Prometheus 监控实例"
                  loading={cpromLoading}
                  options={cpromOptions}
                  onChange={handleCpromInstanceChange}
                  onDropdownVisibleChange={(open) => {
                    // 当下拉框打开且没有数据时，获取CProm实例列表
                    if (open && cpromList.length === 0) {
                      requestCpromList();
                    }
                  }}
                />
                <Button
                  icon={<OutlinedRefresh />}
                  onClick={handleRefresh}
                  loading={cpromLoading}
                  style={{ width: 32, height: 32 }}
                />
              </div>
              <div style={{ color: '#84868C', fontSize: '12px', lineHeight: '20px' }}>
                如需创建监控实例，可以 <a href='https://console.bce.baidu.com/cprom/#/create' target='_blank'>去创建 CProm 实例</a>
              </div>
            </div>
          </Form.Item>
        )}
      </div>
    </div>
  );
};

export default CreateInstance;
