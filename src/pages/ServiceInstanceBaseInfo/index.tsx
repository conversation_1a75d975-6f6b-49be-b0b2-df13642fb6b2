import React, {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {getQueryParams} from '@baidu/bce-react-toolkit';
import {Button, Col, Loading, Popover, Row, Tag} from 'acud';
import {OutlinedLeft} from 'acud-icon';
import {useRequest} from 'ahooks';

import {getServiceInstanceList} from '@/apis/serviceAdmin';
import Label from '@/components/CreateServiceInstance/Label';
import urls from '@/utils/urls';

import styles from './index.module.less';

const ServiceInstanceBaseInfo: React.FC = () => {
  const [detailInfo, setDetailInfo] = useState<any>({});
  const {instanceId, serviceName, serviceId, instanceServiceId, namespace} =
    getQueryParams();
  const navigate = useNavigate();

  const {run, loading} = useRequest(
    () =>
      getServiceInstanceList({
        serviceId,
        serviceName,
        queryInstanceId: instanceServiceId,
        instanceId: instanceId
      }),
    {
      manual: true,
      onSuccess: (res) => {
        setDetailInfo(res?.result?.result[0]);
      }
    }
  );

  useEffect(() => {
    run();
  }, [run]);

  const goBack = () => {
    navigate(
      `${urls.serviceInstanceList}?instanceId=${instanceId}&serviceName=${serviceName}&serviceId=${serviceId}&namespace=${namespace}`
    );
  };

  return (
    <Loading loading={loading}>
      <div className={styles['create-instance-wrap']}>
        <div className={styles['create-instance-header']}>
          <Button
            className="common-back-btn"
            type="text"
            onClick={() => goBack()}
            icon={<OutlinedLeft />}
          >
            返回
          </Button>
          <h2 className="custom-header-title">实例详情</h2>
        </div>
        <div className={styles['create-instance-content']}>
          <div className={styles['detail-base-card']}>
            <div className={styles['detail-base-card-title']}>基本信息</div>
            <div className={styles['detail-base-card-content']}>
              <Row gutter={[48, 16]}>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label1']}>
                      Id：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      {detailInfo.serviceInstanceId || '-'}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label2']}>
                      IP：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      {detailInfo.host || '-'}{' '}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label2']}>
                      端口：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      {detailInfo.port || '-'}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label1']}>
                      健康状态：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      <Tag
                        color="transparent"
                        className="table-status"
                        style={{paddingLeft: 0}}
                        icon={
                          <span
                            className={`circle status-${detailInfo.healthStatus ? 'success' : 'warning'}`}
                          />
                        }
                      >
                        {detailInfo.healthStatus === true
                          ? '健康'
                          : detailInfo.healthStatus === false
                            ? '异常'
                            : '-'}
                      </Tag>
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label2']}>
                      隔离状态：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      <Tag
                        color="transparent"
                        className="table-status"
                        style={{paddingLeft: 0}}
                        icon={
                          <span
                            className={`circle status-${detailInfo.isolateEnable ? 'warning' : 'success'}`}
                          />
                        }
                      >
                        {detailInfo.isolateEnable === true
                          ? '隔离'
                          : detailInfo.isolateEnable === false
                            ? '不隔离'
                            : '-'}
                      </Tag>
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label2']}>
                      创建时间：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      {detailInfo.createTime || '-'}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label1']}>
                      修改时间：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      {detailInfo.updateTime || '-'}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label2']}>
                      健康检查：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      <Tag
                        color="transparent"
                        className="table-status"
                        style={{paddingLeft: 0}}
                        icon={
                          <span
                            className={`circle status-${detailInfo.healthCheckEnable ? 'success' : 'warning'}`}
                          />
                        }
                      >
                        {detailInfo.healthCheckEnable ? '开启' : '关闭'}
                      </Tag>
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label2']}>
                      TTL：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      {detailInfo.healthCheckEnable
                        ? `${detailInfo.ttl}秒`
                        : '-'}
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label1']}>
                      最近健康检查时间：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      {detailInfo.lastHeartbeatTime || '-'}
                    </div>
                  </div>
                </Col>
              </Row>
              <div className={styles['instance-detail-border']}></div>
              <Row gutter={[48, 16]}>
                <Col span={24}>
                  <div className={styles['detail-base-card-item']}>
                    <div className={styles['detail-base-card-label2']}>
                      实例标签：
                    </div>
                    <div className={styles['detail-base-card-value']}>
                      <Label
                        width={558}
                        value={Object.keys(detailInfo?.metadata || {}).map(
                          (key) => {
                            return {
                              value: detailInfo.metadata[key],
                              label: key
                            };
                          }
                        )}
                        isView={true}
                      />
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    </Loading>
  );
};

export default ServiceInstanceBaseInfo;
