# 移除集群接口

## 接口基本信息

- **接口说明**：从AI网关实例中移除关联的集群
- **接口地址**：`/api/aigw/v1/aigateway/cluster/{instanceId}/{clusterId}`
- **请求方式**：DELETE
- **数据格式**：JSON

## 请求参数

### 路径参数

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| instanceId | String | 是 | 网关实例ID | gw-ist9vvin |
| clusterId | String | 是 | 集群ID | cce-qhjz40ds |

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| X-Region | String | 是 | 地域代码 | gz |


## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": null
}
```

### 错误响应体结构

```json
{
    "success": false,
    "status": 403,
    "message": "实例中仍有服务来源当前集群，无法被移除"
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Null | 移除集群成功后返回null |
| message | String | 错误信息，仅在失败时返回 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数格式是否正确 |
| 401 | 未授权 | 确认用户是否有移除集群的权限 |
| 403 | 禁止操作 | 集群可能有相关服务关联到实例 |
| 404 | 实例或集群不存在 | 确认实例ID和集群ID是否正确 |
| 409 | 资源冲突 | 集群或实例可能正在被其他操作修改，请稍后重试 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
DELETE /api/aigw/v1/aigateway/cluster/gw-ist9vvin/cce-qhjz40ds
X-Region: gz
```

## 响应示例

### 成功响应示例

```json
{
    "success": true,
    "status": 200,
    "result": null
}
```

### 禁止操作响应示例

```json
{
    "success": false,
    "status": 403,
    "message": "实例中仍有服务来源当前集群，无法被移除"
}
```

## 注意事项

1. 实例ID必须符合规范格式，通常以特定前缀（如`gw-`）开头
2. 集群ID必须符合规范格式，通常以特定前缀（如`cce-`）开头
3. 移除集群操作不可逆，请谨慎操作
4. 系统会先检查集群是否有服务关联到实例，如果有则会拒绝移除操作
5. 如果需要移除仍有关联服务的集群，需要先移除这些服务
6. 移除集群不会影响集群本身，只会解除集群与网关实例的关联关系
7. 用户需要具有对应实例和集群的管理权限才能执行移除操作
8. 如果实例或集群正在其他操作中，可能无法立即移除 