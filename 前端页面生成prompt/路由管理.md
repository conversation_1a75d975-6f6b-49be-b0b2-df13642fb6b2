执行路径：分 3 次 prompt 1. 路由列表->创建路由 2. 查询路由详情 3. 编辑路由 4. 删除路由->调用示例

# 路由列表 & 创建路由 ✅

1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、监控信息、消费者列表），点击每个导航栏右侧会切换成当前导航栏下对应的页面。

2. 需求描述：现在，我期望在推理服务导航栏的下方新增一个导航栏【路由配置】，即在实例详情页面中左侧导航栏总共包含 5 个（实例详情/推理服务/路由配置/监控信息/消费者列表），然后点击路由配置导航栏右侧页面为路由列表页面。我会在第 3 点中详细描述路由列表这个页面的交互与样式，你需要在遵循相关规范的前提下生成这个页面。另外，【路由列表】这个页面中涉及的所有接口请求、返回需要和接口文档中涉及的参数完全符合。

    1. 这个页面中会涉及到 5 个接口：1. 查询实例详情 2. 查询路由列表 3. 创建路由 <font color="#ff0000">4. 根据服务来源查询服务名称及命名空间</font> <font color="#ff0000">5. 查询实例中所有消费者</font>（接口文档在接口文档文件夹中）。我会在第 4 点中分点描述这三个接口和页面的对应关系，你需要在遵循相关规范的前提下修改路由列表这个页面涉及模块相关的参数和字段。你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。

3. 页面交互与样式：在路由列表页面从上往下首先有水平排列的 3 个元素： 1. 创建路由=按钮= 2. =搜索=框 3. 刷新=按钮=，创建路由按钮靠页面左侧，搜索框和刷新按钮靠页面右侧，其中搜索框可根据路由名称进行模糊搜索。在这 3 个元素下方有一个=列表=，用于展示当前实例中已创建的路由信息，列表中的内容在第 4 点中详细描述。点击创建路由按钮会进入一个新的子页面，页面的标题为创建路由，创建路由页面主体为一个=表单=，我期望分成 3 个模块（基础配置/路由规则/认证授权）以让结构更加清晰。你可以参考实例列表中点击创建实例按钮后的页面样式，在创建路由页面右下角有两个=按钮=，1 个是发布，1 个是取消，点击取消重新回到路由列表页面，点击发布会调用创建路由接口，接口返回成功后才会回到路由列表页面，页面和接口的对应关系在第 4 点中会详细说明。接下来我将详细描述创建路由页面中每个模块中的字段： 2. 基础配置模块中总共有 1 个字段（路由名称），路由名称右侧为=输入框=； 3. 路由规则模块中分成 2 个主要字段（匹配规则/目标服务）： 1. 匹配规则右侧为一个子=表单=，这个子表单中包含 4 个字段（路径/请求方法/请求头/请求参数），其中路径字段右侧水平排列总共包含 3 个元素（1.=下拉列表= 2. =输入框= 3. Checkbox 复选框），下拉列表用于选择匹配规则，点击后包含 2 个选项（前缀匹配/精确匹配）；输入框用于输入路径匹配值；Chexbox 用于勾选是否开启大小写敏感，默认勾选。请求方法字段右侧为一个=下拉列表=，用于选择具体匹配哪些请求方法，支持多选、全选默认勾选所有请求方法，并回显全部：包含 9 种请求方法（GET/POST/PUT/DELETE/OPTIONS/HEAD/PATCH/TRACE/CONNECT）。请求头右侧水平排列总共包含 4 个元素（1. =输入框= 2. =下拉列表= 3. 输入框 4. 删除），第 1 个输入框用于输入请求头的 Key，输入框 Placeholder 为 “Header Key”，下拉列表用于选择匹配规则（前缀匹配/精确匹配），第 2 个输入框用于输入请求头的值，输入框 Placeholder 为“值”，删除是一个可点击的 span，用于移除这一条请求头，在这 4 个元素下方有一个添加请求头文案，可以执行添加请求头操作，点击后在当前这条请求头的下方新增同样 4 个元素（1. =输入框= 2. =下拉列表= 3. 输入框 4. 删除）。请求参数和请求头样式一致，右侧同样水平排列总共包含 4 个元素（1. =输入框= 2. =下拉列表= 3. 输入框 4. 删除），第 1 个输入框用于输入请求参数的 Key，输入框 Placeholder 为 “Query Key”，下拉列表用于选择匹配规则（前缀匹配/精确匹配），第 2 个输入框用于输入请求参数的值，输入框 Placeholder 为“值”，删除是一个可点击的 span，用于移除这一条请求参数，在这 4 个元素下方有一个添加请求参数文案，可以执行添加请求参数操作，点击后在当前这条请求参数的下方新增同样 4 个元素（1. =输入框= 2. =下拉列表= 3. 输入框 4. 删除）。 2. 目标服务右侧为一个=列表=，这个列表是带单元格编辑功能的表格，总共包含 4 列（服务来源/服务名称/服务端口/负载均衡算法），除了表头之外，目前只包含 1 行，第 1 列为一个=下拉列表=，用于选择服务来源（CCE），第 2 列为=下拉列表=，需要选择服务来源后才可以选择服务名称，点击后根据服务来源回显所有服务，这里需要调用根据服务来源查询服务列表接口，<font color="#ff0000">具体的接口调用我会在第 4 点中说明，第 3 列为=输入框=，需要输入目标服务的某个服务端口</font>，第 4 列负载均衡算法为一个=下拉列表=，点击后回显支持的负载均衡算法（轮询、随机、最小连接数），如果当前行第 2 列中没有选择服务名称，则无法点击负载均衡算法这个下拉列表。 4. 认证授权模块中包含 1 个字段：消费者认证，消费者认证右侧为一个=开关=，默认关闭，并且在开关下方有一行提示文案：“开启后需配置可访问消费者，仅与当前路由关联的消费者可访问，可在消费者管理中创建消费者“。当开关开启后，开关下方出现一个 Transfer 穿梭选择框，左侧多选组件中展示当前实例中所有消费者，这里需要根据实例 ID 请求查询实例中所有消费者的接口，具体接口调用我会在第 4 点中说明，穿梭选择框中支持全选。

4. 页面与接口的对应关系：

    1. 用户点击导航栏【路由列表】后在右侧路由列表页面看到的=列表=页面：总共包含 6 列（路由名称、路由状态、匹配路径、目标服务、创建时间、操作），这个列表页面列表支持=分页=，默认 10 条/页。其中匹配路径这一列中需要包含 2 个字段值（匹配规则和路径匹配值），并且需要分成 2 行展示，操作列中包含 3 个操作：调用示例、编辑、删除。路由名称、路由状态、匹配路径、目标服务、创建时间这 5 个参数来自于【查询实例中路由列表】这个接口中返回的字段，所以，在刚开始进入路由列表页面的时候就需要调用【查询实例中路由列表】这个接口用于回显字段值，同时，当用户 1. 点击刷新按钮 2. 在搜索框中输入路由名称执行搜索时也需要重新调用查询实例中路由列表接口。另外，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。因为现在只有点击实例名称才会进入实例详情页，才能进入路由配置导航栏中的路由列表页面。
    2. 点击创建路由按钮会进入二级页面，页面是一个=表单=，在路由规则这个模块中的目标服务字段中，当用户点击服务来源的下拉列表，选择某一个选项后，再点击服务名称这个下拉列表时会根据实例 ID 和服务来源请求【查询服务列表】接口，用于在下拉列表中的每个选项中展示接口返回的服务名称、命名空间，并且以"服务名称（命名空间）"进行拼接展示，下拉列表中会根据接口返回回显当前实例中指定服务来源的所有“服务名称（命名空间）“，【查询服务列表】这个接口需要实例 id，可以在路径参数中查询到。当用户开启消费者认证这个开关后，需要调用【查询实例中消费者列表】这个接口，用于在下方的穿梭选择框中回显所有的消费者名称（消费者 ID）。当用户点击页面右下角的发布按钮后，会根据当前页面表单中的相关参数去请求【创建路由】接口，如果成功就先返回路由列表页面，并重新请求【查询实例路由列表】这个接口，当页面加载完后需要在右上角提示路由发布成功。如果失败需要在创建路由页面的右上角 toast 提示接口返回的失败原因，不需要返回路由列表页面。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：1. . md 2. . md 3. . md（参见附件）

7. 遵守原则：所有字段以接口文档为准，不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改路由列表这个页面中路由列表和创建路由 2 个模块中涉及的相关接口、字段，所以你不应该再自动为我完善点击路由列表中操作列的编辑、删除之后的交互和接口。

# 查询路由详情 ✅

1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、路由配置、消费者列表），点击每个导航栏后右侧会切换成当前导航栏下对应的页面。当前点击路由配置导航栏右侧会出现路由列表页面，在路由列表页面中有一个列表用于展示当前实例中所有路由信息。

2. 需求描述：现在我期望当用户点击路由列表中某条路由的名称后路由配置右侧的路由列表页面会变更为路由详情页面，在这个页面顶部为 Breadcrumb 面包屑样式，从左到右Breadcrumb.Item依次为"路由列表 > {当前路由名称}"。点击 Breadcrumb 中的“路由列表”则再次返回路由列表页面。我会在第 3 点中详细描述路由详情这个页面的交互与样式，你需要在遵循相关规范的前提下生成这个页面。另外，【路由详情】这个页面中涉及的所有接口请求、返回需要和接口文档中涉及的参数完全符合。
	1. 这个页面中可能会涉及到 2 个接口：1. 查询实例详情 2. 查询路由详情 （接口文档在接口文档文件夹中）。我会在第 4 点中描述这 2个接口和页面的对应关系，你需要在遵循相关规范的前提下修改路由列表这个页面涉及模块相关的参数和字段。你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。

3. 页面交互与样式：路由详情页面头部为面包屑Breadcrumb，下方包含 2 个标签页（基本信息/统计），点击不同标签页切换到不同的页面，默认选中基本信息标签页，基本信息标签页中整体分成 3 个模块（基本信息/匹配规则/目标服务），每个模块包含不同的字段。基本信息模块包含 4 个字段（路由名称、更新时间、创建时间、消费者认证），其中消费者认证右侧展示是否开启消费者认证，鼠标移入字段值时，出现一个气泡展示已授权的消费者名称/ID，在第 4 点中我会详细描述每个字段与接口的对应关系，匹配规则模块中包含 4 个字段 (路径/请求方法/请求头/请求参数)，路径右侧包含 3 部分内容（匹配方式/路径值/是否开启大小写敏感），请求方法右侧包含这条路由的请求方法，可以使用无语义的标签顺序色 Tag/TagList（type=“outline”）进行展示；请求头右侧包含这条路由中的请求头，是用默认 Tag 进行展示，每个标签是 1 个请求头-由 3 部分内容拼接而成（Header Key+匹配方式+值），多个请求头就展示为多条标签；请求参数右侧包含这条路由中的请求参数，是用默认 Tag 进行展示，每个标签是 1 个请求参数-由 3 部分内容拼接而成（Query Key+匹配方式+值），多个请求参数就展示为多条标签；第 3 个模块目标服务中包含一个=列表=，展示这条路由目标服务的相关信息，列表包含 6 列（服务名称/服务端口/服务来源/命名空间/创建时间/负载均衡算法）。

4. 页面与接口对应关系：当用户点击路由名称后需要根据实例 ID 和路由名称调用查询路由详情这个接口，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。在基本信息模块中，路由名称、更新时间、创建时间、消费者认证来自接口返回的字段值，其中消费者认证展示的为是否开启认证；匹配规则模块中，路径、请求方法、请求头、请求参数均来自于接口返回的相关字段值；目标服务模块中，列表中字段值均来自于接口返回字段。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：1. 查询实例详情. md 2. 查询路由详情. md （参见附件）

7. 遵守原则：所有字段以接口文档为准，不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改查看路由详情这个页面中各模块涉及的相关接口、字段，所以你不应该再自动为我完善编辑路由的交互和接口。


# 编辑路由 ✅

1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、路由配置、消费者列表），点击每个导航栏后右侧会切换成当前导航栏下对应的页面。当前点击路由配置导航栏右侧会出现路由列表页面，在路由列表页面中有一个列表用于展示当前实例中所有路由信息。

2. 需求描述：现在我期望当用户点击路由列表中某条路由操作列中的编辑操作后进入编辑路由页面，这个页面整体和创建路由页面布局、样式、字段均保持一致，唯一不一致的是需要先查询路由详情，查询后根据接口返回的字段预先填充相关信息，如果某个字段值为空则不需要填充，其中路由名称无法编辑，因此仅作为静态回显，其他字段均可编辑。在这个页面顶部为返回列表、编辑路由，点击返回列表则再次返回路由列表页面。我会在第 3 点中详细描述路由详情这个页面的交互与样式，你需要在遵循相关规范的前提下生成这个页面。另外，【编辑路由】这个页面中涉及的所有接口请求、返回需要和更新路由接口文档中涉及的参数完全符合。
	1. 这个页面中可能会涉及到 5 个接口：1. 查询路由详情 2. 更新路由 3. 查询消费者列表 4. 根据服务来源查询服务名称及命名空间 5. 查询实例中所有消费者（接口文档在接口文档文件夹中）。我会在第 4 点中描述这 2 个接口和页面的对应关系，你需要在遵循相关规范的前提下修改编辑路由这个页面涉及模块相关的参数和字段。你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。

3. 页面交互与样式：编辑路由页面整体布局、样式、交互逻辑方式、字段和创建路由页面保持一致。你可以直接复用创建路由页面的代码，在这个基础上进行修改。

4. 页面与接口对应关系：当用户点击路由列表中操作列中的编辑操作后，进入编辑路由页面，需要根据实例 ID 和当前路由名称调用查询路由详情这个接口，然后根据接口返回值对相关的字段值进行预填充，其中路由名称不可编辑，因此应该为编辑禁用态，或者仅作静态回显，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：1. 查询路由详情 2. 更新路由 3. 查询消费者列表 4. 根据服务来源查询服务名称及命名空间 5. 查询实例中所有消费者（参见附件）

7. 遵守原则：所有字段以接口文档为准，不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改编辑路由这个页面中各模块涉及的相关接口、字段，所以你不应该再自动为我完善创建路由的交互和接口。

# 删除路由 ✅
