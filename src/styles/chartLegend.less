/**
 * 图表图例优化样式
 * 用于解决图例过多时遮挡图表内容的问题
 */

/* 图表容器基础样式 */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  
  /* 确保图表容器有足够的空间 */
  .chart-wrapper {
    width: 100%;
    height: 100%;
    min-height: 300px;
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .chart-wrapper {
      min-height: 250px;
    }
  }
  
  @media (max-width: 480px) {
    .chart-wrapper {
      min-height: 200px;
    }
  }
}

/* 图例优化样式 */
.legend-optimized {
  /* 图例文本样式优化 */
  .echarts-legend-item {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* 图例滚动按钮样式优化 */
  .echarts-legend-scroll-btn {
    opacity: 0.7;
    transition: opacity 0.3s ease;
    
    &:hover {
      opacity: 1;
    }
  }
  
  /* 图例选择器样式优化 */
  .echarts-legend-selector {
    font-size: 12px;
    color: #666;
    
    &:hover {
      color: #1890ff;
    }
  }
}

/* 弹窗模式下的图例样式 */
.modal-chart {
  .legend-optimized {
    /* 弹窗模式下图例文本可以更长 */
    .echarts-legend-item {
      max-width: 300px;
    }
    
    /* 弹窗模式下的选择器样式 */
    .echarts-legend-selector {
      font-size: 14px;
    }
  }
}

/* 图例过多时的特殊处理 */
.legend-many-series {
  /* 当系列数量超过8个时的样式 */
  &.series-count-8-plus {
    .echarts-legend-item {
      font-size: 10px;
      max-width: 150px;
    }
  }
  
  /* 当系列数量超过12个时的样式 */
  &.series-count-12-plus {
    .echarts-legend-item {
      font-size: 9px;
      max-width: 120px;
    }
  }
  
  /* 当系列数量超过15个时的样式 */
  &.series-count-15-plus {
    .echarts-legend-item {
      font-size: 8px;
      max-width: 100px;
    }
  }
}

/* 图例工具提示样式 */
.legend-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  max-width: 300px;
  word-wrap: break-word;
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  &.show {
    opacity: 1;
  }

  /* 箭头样式 */
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
  }
}

/* 图例项悬停效果 */
.echarts-legend-item {
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }

  /* 被截断的图例项特殊样式 */
  &.truncated {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(to right, transparent, #ccc, transparent);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}

/* 图例加载状态样式 */
.legend-loading {
  .echarts-legend-item {
    opacity: 0.5;
    animation: legend-pulse 1.5s ease-in-out infinite;
  }
}

@keyframes legend-pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}

/* 图例错误状态样式 */
.legend-error {
  .echarts-legend-item {
    color: #ff4d4f;
    text-decoration: line-through;
  }
}

/* 图例隐藏动画 */
.legend-fade-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.legend-fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.legend-fade-exit {
  opacity: 1;
  transform: translateY(0);
}

.legend-fade-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 图例响应式布局 */
@media (max-width: 1200px) {
  .legend-optimized {
    .echarts-legend-item {
      max-width: 150px;
      font-size: 11px;
    }
  }
}

@media (max-width: 768px) {
  .legend-optimized {
    .echarts-legend-item {
      max-width: 120px;
      font-size: 10px;
    }
    
    .echarts-legend-selector {
      font-size: 11px;
    }
  }
}

@media (max-width: 480px) {
  .legend-optimized {
    .echarts-legend-item {
      max-width: 100px;
      font-size: 9px;
    }
    
    .echarts-legend-selector {
      font-size: 10px;
    }
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .legend-optimized {
    .echarts-legend-item {
      font-weight: bold;
      text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .legend-optimized,
  .legend-fade-enter-active,
  .legend-fade-exit-active {
    transition: none;
    animation: none;
  }
}
