现在，我需要开发1个接口（创建消费者），你需要给每个接口中的参数起字段名，并规定这个字段的相关格式和限制。写1份接口文档，以md格式存储，存储在接口文档的消费者管理文件夹中，如果没有这个文件夹你需要新建一个。

1. 创建消费者的接口路径为：/api/aigw/v1/aigateway/{instanceId}/consumer

2. 需要传以下请求参数：
	1. 实例ID (路径参数)
	2. 消费者名称
	3. 描述
	4. 认证方式（目前只有JWT）
	5. 授权路由范围（选填，支持多条路由-路由名称）
3 返回值result字段为空

|   |   |   |
|---|---|---|
|**参数**|**说明**|**相关限制**|
|消费者名称|必填，创建消费者的名称|1. 长度为2-64个字符<br>2. 支持英文、数字、中划线、下划线<br>3. 同一实例下不可重名|
|描述|选填，消费者的描述信息|最多255个字符|
|认证方式|必填，目前仅支持JWT认证|固定值为"JWT"|
|授权路由范围|选填，可选择允许该消费者访问的路由|可以选择多条路由| 