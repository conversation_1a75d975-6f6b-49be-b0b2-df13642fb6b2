import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  Button,
  Drawer,
  Form,
  Input,
  Loading,
  Modal,
  Radio,
  Steps,
  Table,
  Tag,
  toast,
  Tooltip
} from 'acud';
import {OutlinedPlusNew} from 'acud-icon';
import cx from 'classnames';
import _ from 'lodash';

import {
  createConfigFile,
  deleteConfigFile,
  editConfigFile,
  getConfigFileDetail,
  getConfigFileList,
  getConfigReleseDetail,
  getConfigReleseList,
  releaseConfigFile
} from '@/apis/configManage';
import MSETagEditPanel from '@/components/ConfigTag';
import EllipsisWrap from '@/components/Ellipsis';
import {
  MonacoEditorComponent,
  MonacoEditorDiffComponent
} from '@/components/MonacoEditorComponent';
import {
  ConfigFileStatusDict,
  FormatMapping,
  FormatOptions
} from '@/utils/enums';

import styles from './index.module.less';

const {Step} = Steps;

export const deleteConfirm =
  (instanceId: string, refresh: () => void, selectedRows: any) => () => {
    const onOk = () => {
      return deleteConfigFile({
        instanceId: instanceId,
        configFiles: selectedRows.map((d) => {
          return {
            name: d.name,
            group: d.group,
            namespace: d.namespace,
            currentReleaseName: d.currentReleaseName
          };
        })
      })
        .then((res) => {
          if (res?.success) {
            toast.success({
              message: '配置文件已删除',
              duration: 5
            });
            refresh();
          } else {
            toast.error({
              message: '删除失败，请稍后重试',
              duration: 5
            });
          }
        })
        .catch(() =>
          toast.error({
            message: '删除失败，请稍后重试',
            duration: 5
          })
        );
    };

    Modal.confirm({
      title: '删除配置文件',
      content: (
        <>
          确定删除以下配置文件吗？删除后配置文件若存在历史发布版本，所有历史版本也将一同被永久删除，无法恢复和访问，请谨慎操作。
          <Table
            dataSource={selectedRows}
            pagination={false}
            className="mse-delete-modal-table"
            columns={[
              {
                title: '配置文件名',
                dataIndex: 'name',
                width: 225,
                render(value) {
                  return <EllipsisWrap data={value} width={225} />;
                }
              },
              {
                title: '配置分组',
                dataIndex: 'group',
                width: 225,
                render(value) {
                  return <EllipsisWrap data={value} width={225} />;
                }
              }
            ]}
          />
        </>
      ),
      onOk,
      width: 520
    });
  };

interface CreateModalProps {
  instanceId: string;
  refresh(): void;
  groupInfo: any;
  initialValues?: any;
  isEdit?: boolean;
  isView?: boolean;
}

export const CreateModal: FC<CreateModalProps> = ({
  refresh,
  instanceId,
  groupInfo,
  initialValues,
  isEdit,
  isView
}) => {
  const [nameSource, setNameSource] = useState<any>({});
  const editorRef = useRef<any>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [formDataInstante] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [curValue, setCurValue] = useState('');
  const [detailLoading, setDetailLoading] = useState(false);
  const [showContentError, setShowContentError] = useState(false);
  const [language, setLanguage] = useState('plaintext');
  const [defaultForm, setDefaultForm] = useState<any>({});
  const tagRef = useRef<any>(null);

  const tagValidate = async () => {
    return tagRef.current?.validate();
  };

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };

  useEffect(() => {
    if (!_.isEmpty(defaultForm)) {
      formDataInstante.setFieldsValue(defaultForm);
      setCurValue(defaultForm?.content);
    }
  }, [defaultForm]);

  const getDetail = useCallback(() => {
    return getConfigFileDetail({
      instanceId: instanceId,
      group: groupInfo.group,
      namespace: groupInfo.namespace,
      name: initialValues.name
    })
      .then((res: any) => {
        setDefaultForm(res?.result || {});
      })
      .finally(() => {
        setDetailLoading(false);
      });
  }, [instanceId, groupInfo, initialValues]);

  const getList = useCallback(() => {
    return getConfigFileList({
      instanceId: instanceId,
      pageNo: 1,
      pageSize: 1000,
      group: groupInfo.group,
      namespace: groupInfo.namespace
    }).then((res: any) => {
      if (res?.result?.configFiles?.length) {
        const names = res?.result?.configFiles.reduce((acc: any, item: any) => {
          acc[item.name] = true;
          return acc;
        }, {});
        setNameSource(names);
      }
    });
  }, [instanceId, groupInfo]);

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    if (isEdit || isView) {
      setDetailLoading(true);
      getDetail();
      return;
    }
    getList();
  }, [getList, getDetail]);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
    formDataInstante.resetFields();
    setCurValue('');
  }, [formDataInstante]);

  const onClickOK = useCallback(
    async (e: React.MouseEvent<HTMLElement>) => {
      if (isView) {
        handleCancel();
        return;
      }
      setLoading(true);
      e.preventDefault();
      try {
        await Promise.all([
          formDataInstante?.validateFields(),
          tagValidate?.()
        ]);
        if (!curValue) {
          setShowContentError(true);
          return;
        }
        const {name, format, comment} = formDataInstante.getFieldsValue();
        const tags = await tagRef.current?.getTags();
        const param = {
          instanceId: instanceId,
          group: groupInfo.group,
          namespace: groupInfo.namespace,
          content: curValue,
          name,
          format,
          comment,
          tags: (tags || []).filter((d) => d.key)
        };
        const res = isEdit
          ? await editConfigFile(param)
          : await createConfigFile(param);
        if (res?.success) {
          if (isEdit) {
            toast.success({
              message: '配置文件已更新，若需发布请手动发布',
              duration: 5
            });
          } else {
            toast.success({
              message: '配置文件创建成功',
              duration: 5
            });
          }
          refresh();
          handleCancel();
        }
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    },
    [formDataInstante, refresh, instanceId, handleCancel, curValue]
  );

  const onChange = (value: any) => {
    if (value) {
      setShowContentError(false);
    }
    setCurValue(value);
  };

  const creatRules = useMemo(() => {
    return {
      name: [
        {
          required: true,
          message: '请输入配置文件名'
        },
        {
          max: 128,
          message: '不可超过128个字符'
        },
        {
          validator: (rule: any, value: string) => {
            if (value && !/^[a-zA-Z0-9._-]+$/.test(value)) {
              return Promise.reject(new Error('配置文件名称不符合规则'));
            }
            if (nameSource[value]) {
              return Promise.reject(new Error('配置文件名称已存在'));
            }
            return Promise.resolve();
          }
        }
      ],
      comment: [
        {
          max: 200,
          message: '不可超过200个字符'
        }
      ]
    };
  }, [nameSource]);

  return (
    <>
      {isView ? (
        <Button type="actiontext" onClick={showModal}>
          {initialValues?.name || '-'}
        </Button>
      ) : isEdit ? (
        <Button type="actiontext" onClick={showModal}>
          编辑
        </Button>
      ) : (
        <Button type="primary" icon={<OutlinedPlusNew />} onClick={showModal}>
          新建
        </Button>
      )}

      <Modal
        title={`${isView ? '查看' : isEdit ? '编辑' : '新建'}配置文件`}
        maskClosable={false}
        onOk={onClickOK}
        onCancel={handleCancel}
        visible={isModalVisible}
        confirmLoading={loading}
        width={800}
        className={styles['mse-create-config-file-modal']}
      >
        <Loading size="small" loading={detailLoading}>
          <Form labelAlign="left" form={formDataInstante} labelWidth="84px">
            <Form.Item label="命名空间">
              <span>{groupInfo.namespace}</span>
            </Form.Item>
            <Form.Item label="配置分组">
              <span>{groupInfo.group}</span>
            </Form.Item>
            {isEdit ? (
              <Form.Item label="配置文件名" name="name">
                <span>{initialValues.name}</span>
              </Form.Item>
            ) : (
              <Form.Item
                rules={creatRules.name}
                label="配置文件名"
                name="name"
                extra="仅支持英文字母、数字、'.'、'-'、'_',不超过128个字符"
              >
                <Input disabled={isEdit || isView} />
              </Form.Item>
            )}

            <Form.Item
              required={true}
              name="format"
              label="配置格式"
              inputMaxWidth={800}
            >
              <Radio.Group
                onChange={(e: any) => {
                  setLanguage(FormatMapping[e?.target?.value]);
                }}
                options={FormatOptions}
                disabled={isView}
              ></Radio.Group>
            </Form.Item>
            <Form.Item
              required={true}
              name="content"
              label="配置内容"
              inputMaxWidth={800}
            >
              <MonacoEditorComponent
                language={language}
                width="560px"
                height="250px"
                value={curValue}
                onMount={handleEditorDidMount}
                onChange={(e) => onChange(e)}
                options={{
                  scrollBeyondLastLine: false,
                  readOnly: isView,
                  wordWrap: 'on',
                  domReadOnly: isView,
                  lineNumbers: 'on',
                  contextmenu: false
                }}
              />
              {showContentError && (
                <div className="mse-custom-form-error-wrap">请输入配置内容</div>
              )}
            </Form.Item>
            <Form.Item label="配置文件标签">
              <MSETagEditPanel
                defaultTags={defaultForm?.tags}
                ref={tagRef}
                width={558}
              />
            </Form.Item>
            <Form.Item
              rules={creatRules.comment}
              name="comment"
              label="备注"
              extra="配置文件备注，不超过200个字符"
            >
              <Input.TextArea limitLength={200} disabled={isView} />
            </Form.Item>
          </Form>
        </Loading>
      </Modal>
    </>
  );
};

interface ViewDrawerProps {
  groupInfo: any;
  initialValues?: any;
  instanceId: string;
  width?: number;
}

export const ViewDrawer: FC<ViewDrawerProps> = ({
  groupInfo,
  initialValues,
  instanceId,
  width
}) => {
  const editorRef = useRef<any>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [formDataInstante] = Form.useForm();
  const [curValue, setCurValue] = useState('');
  const [detailLoading, setDetailLoading] = useState(false);
  const [defaultForm, setDefaultForm] = useState<any>({});
  const tagRef = useRef<any>(null);
  const divRef = useRef<any>(null);

  useEffect(() => {
    if (!_.isEmpty(defaultForm)) {
      formDataInstante.setFieldsValue(defaultForm);
    }
  }, [defaultForm]);

  const getDetail = useCallback(() => {
    return getConfigFileDetail({
      instanceId: instanceId,
      group: groupInfo.group,
      namespace: groupInfo.namespace,
      name: initialValues.name
    })
      .then((res: any) => {
        setDefaultForm(res?.result || {});
      })
      .finally(() => {
        setDetailLoading(false);
      });
  }, [instanceId, groupInfo, initialValues]);

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };

  const showModal = useCallback(() => {
    setDetailLoading(true);
    getDetail();
    setIsModalVisible(true);
  }, []);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
    formDataInstante.resetFields();
    setCurValue('');
  }, [formDataInstante]);

  const showTip = () => {
    return divRef?.current?.scrollWidth > divRef?.current?.clientWidth;
  };

  return (
    <>
      <Tooltip title={showTip() ? initialValues?.name : ''}>
        <a
          ref={divRef}
          style={{
            width: width
          }}
          onClick={showModal}
          className={styles['eddipsis-wrap']}
        >
          {initialValues?.name || '-'}
        </a>
      </Tooltip>

      <Drawer
        title="查看配置文件"
        onClose={handleCancel}
        visible={isModalVisible}
        width={800}
        className="mse-detail-drawer"
      >
        <Loading size="small" loading={detailLoading}>
          <Form labelAlign="left" form={formDataInstante} labelWidth="84px">
            <Form.Item label="命名空间">
              <span>{groupInfo.namespace}</span>
            </Form.Item>
            <Form.Item label="配置分组">
              <span>{groupInfo.group}</span>
            </Form.Item>
            <Form.Item label="配置文件名">
              <span>{defaultForm.name}</span>
            </Form.Item>
            <Form.Item label="状态">
              <Tag
                color="transparent"
                className="table-status"
                style={{paddingLeft: 0}}
                icon={
                  <span
                    className={
                      ConfigFileStatusDict.get(defaultForm.status)?.iconClass
                    }
                  />
                }
              >
                {ConfigFileStatusDict.get(defaultForm.status)?.text}
              </Tag>
            </Form.Item>
            <Form.Item name="format" label="配置格式" inputMaxWidth={800}>
              <Radio.Group
                options={FormatOptions}
                disabled={true}
              ></Radio.Group>
            </Form.Item>
            <Form.Item
              required={true}
              name="content"
              label="配置内容"
              inputMaxWidth={800}
            >
              <MonacoEditorComponent
                language={defaultForm.format}
                width="560px"
                height="250px"
                value={curValue}
                onMount={handleEditorDidMount}
                options={{
                  scrollBeyondLastLine: false,
                  readOnly: true,
                  wordWrap: 'on',
                  domReadOnly: true,
                  lineNumbers: 'on',
                  contextmenu: false
                }}
              />
            </Form.Item>
            <Form.Item label="配置文件标签" name="tags">
              <MSETagEditPanel
                defaultTags={defaultForm?.tags}
                ref={tagRef}
                width={558}
                isView={true}
              />
            </Form.Item>
            <Form.Item label="备注">
              <span>{defaultForm.comment || '-'}</span>
            </Form.Item>
            <Form.Item label="创建时间">
              <span>{defaultForm.createTime || '-'}</span>
            </Form.Item>
            <Form.Item label="最后修改时间">
              <span>{defaultForm.updateTime || '-'}</span>
            </Form.Item>
            <Form.Item label="最后发布时间">
              <span>
                {!defaultForm.releaseTime ||
                defaultForm.releaseTime === '0001-01-01 00:00:00'
                  ? '-'
                  : defaultForm.releaseTime}
              </span>
            </Form.Item>
          </Form>
        </Loading>
      </Drawer>
    </>
  );
};

interface ReleaseModalProps {
  instanceId: string;
  refresh(): void;
  fileInfo: any;
}

export const ReleaseModal: FC<ReleaseModalProps> = ({
  instanceId,
  refresh,
  fileInfo
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [formDataInstante] = Form.useForm();
  const [curStep, setStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [versionSource, setVersionSource] = useState<any>({});
  const [curVersionDetail, setCurVersionDetail] = useState<any>({});

  // 获取版本详情
  const getVersionDetail = useCallback(() => {
    return getConfigReleseDetail({
      instanceId: instanceId,
      group: fileInfo.group,
      namespace: fileInfo.namespace,
      file: fileInfo.name,
      name: fileInfo.currentReleaseName
    }).then((res: any) => {
      setCurVersionDetail(res?.result || {});
    });
  }, [fileInfo]);

  const getList = useCallback(() => {
    return getConfigReleseList({
      instanceId: instanceId,
      pageNo: 1,
      pageSize: 1000,
      group: fileInfo.group,
      namespace: fileInfo.namespace,
      file: fileInfo.name
    }).then((res: any) => {
      if (res?.result?.configReleases?.length) {
        const names = res.result?.configReleases.reduce(
          (acc: any, item: any) => {
            acc[item.name] = true;
            return acc;
          },
          {}
        );
        setVersionSource(names);
      }
    });
  }, [instanceId]);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
    formDataInstante.resetFields();
    setStep(0);
    setCurVersionDetail({});
  }, [formDataInstante]);

  const showModal = useCallback(() => {
    setIsModalVisible(true);
    getList();
    if (fileInfo?.currentReleaseName) {
      getVersionDetail();
    }
  }, [getList, getVersionDetail, fileInfo]);

  const releaseRules = useMemo(() => {
    return {
      name: [
        {
          required: true,
          message: '请输入版本号'
        },
        {
          max: 50,
          message: '不可超过50个字符'
        },
        {
          validator: (rule: any, value: string) => {
            if (versionSource[value]) {
              return Promise.reject(new Error('版本号已存在'));
            }
            return Promise.resolve();
          }
        }
      ],
      comment: [
        {
          max: 50,
          message: '不可超过50个字符'
        }
      ]
    };
  }, [versionSource]);

  const onClickOK = async () => {
    const {name, comment} = formDataInstante.getFieldsValue();
    try {
      setLoading(true);
      const res = await releaseConfigFile({
        instanceId,
        group: fileInfo?.group,
        file: fileInfo?.name,
        namespace: fileInfo?.namespace,
        name,
        comment
      });
      if (res?.success) {
        toast.success({
          message: '配置文件发布成功',
          duration: 5
        });
        refresh();
        handleCancel();
        return;
      }
      toast.error({
        message: '配置文件发布失败',
        duration: 5
      });
    } catch (err) {
      toast.error({
        message: '配置文件发布失败',
        duration: 5
      });
    } finally {
      setLoading(false);
    }
  };

  const onNext = async () => {
    await formDataInstante?.validateFields();
    setStep(curStep + 1);
  };
  return (
    <>
      <Button type="actiontext" onClick={showModal}>
        发布
      </Button>
      <Modal
        title="发布配置文件"
        maskClosable={false}
        onCancel={handleCancel}
        visible={isModalVisible}
        width={1000}
        className={styles['mse-release-config-file-modal']}
        footer={null}
      >
        <div className={styles['step-wrap']}>
          <Steps current={curStep}>
            <Step title="填写发布信息" />
            <Step title="版本对比" />
          </Steps>
        </div>
        <Form
          labelAlign="left"
          form={formDataInstante}
          labelWidth="60px"
          className={cx({[styles['release-visible']]: curStep === 1})}
        >
          <Form.Item label="命名空间" required={true}>
            <span>{fileInfo?.namespace}</span>
          </Form.Item>
          <Form.Item label="配置分组" required={true}>
            <span>{fileInfo?.group}</span>
          </Form.Item>
          <Form.Item label="配置文件" required={true}>
            <span>{fileInfo?.name}</span>
          </Form.Item>
          <Form.Item
            rules={releaseRules.name}
            label="版本号"
            name="name"
            tooltip="配置文件发布后，版本号将作为历史发布版本的唯一标识。"
          >
            <Input placeholder="请输入版本号" />
          </Form.Item>
          <Form.Item rules={releaseRules.comment} label="备注" name="comment">
            <Input placeholder="请输入版本备注" />
          </Form.Item>
        </Form>
        {curStep === 1 && (
          <div className={styles['mse-release-diff-editor-wrap']}>
            <div className={styles['diff-top-wrap']}>
              <div className={styles['diff-top-item']}>
                {fileInfo?.currentReleaseName ? '使用中版本' : '无使用中版本'}
                {fileInfo?.currentReleaseName
                  ? `（版本号：${fileInfo?.currentReleaseName}）`
                  : ''}
              </div>
              <div className={styles['diff-top-item']}>
                当前版本（版本号：{formDataInstante.getFieldsValue().name}）
              </div>
            </div>
            <MonacoEditorDiffComponent
              language={FormatMapping[fileInfo?.format] || 'plaintext'}
              width="100%"
              height="280px"
              oriValue={curVersionDetail?.content}
              value={fileInfo?.content}
              options={{
                scrollBeyondLastLine: false,
                readOnly: true,
                wordWrap: 'on',
                domReadOnly: true,
                lineNumbers: 'on',
                contextmenu: false,
                minimap: {
                  enabled: false
                }
              }}
            />
          </div>
        )}
        <div className={styles['import-dialog-footer']}>
          {curStep === 1 && (
            <Button
              onClick={() => {
                setStep(curStep - 1);
              }}
            >
              上一步
            </Button>
          )}
          <Button onClick={handleCancel}>取消</Button>
          {curStep === 1 && (
            <Button type="primary" onClick={onClickOK} loading={loading}>
              确定
            </Button>
          )}
          {curStep === 0 && (
            <Button type="primary" onClick={onNext}>
              下一步
            </Button>
          )}
        </div>
      </Modal>
    </>
  );
};
