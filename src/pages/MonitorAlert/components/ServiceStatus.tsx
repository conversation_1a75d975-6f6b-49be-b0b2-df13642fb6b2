import React, { useState } from 'react';
import { Tooltip } from 'acud';
import { useRequest } from 'ahooks';
import { getCPromInstanceDetail } from '@/apis/monitor';
import styles from './ServiceStatus.module.less';

interface ServiceStatusProps {
  cpromInstanceId?: string;
  region: string;
  onRetentionPeriodChange?: (retentionPeriod: string) => void; // 新增：存储时长变化回调
}

interface CPromInstanceInfo {
  instanceId: string;
  instanceStatus: string;
  retentionPeriod: string;
}

// 状态映射：英文状态 -> 中文状态
const STATUS_MAP: Record<string, string> = {
  'Running': '运行中',
  'Creating': '创建中',
  'Stopped': '已停止',
  'Deleting': '删除中',
  'Error': '异常',
  'Pending': '等待中',
  'Failed': '失败',
  'Unknown': '未知'
};

// 存储时长格式化：将"15d"转换为"15天"
const formatRetentionPeriod = (period: string): string => {
  if (!period || period === 'Unknown') {
    return '未知';
  }

  // 匹配数字+单位的格式
  const match = period.match(/^(\d+)([a-zA-Z]+)$/);
  if (!match) {
    return period; // 如果格式不匹配，返回原值
  }

  const [, number, unit] = match;
  const unitMap: Record<string, string> = {
    'd': '天',
    'h': '小时',
    'm': '分钟',
    's': '秒',
    'w': '周',
    'M': '月',
    'y': '年'
  };

  const chineseUnit = unitMap[unit] || unit;
  return `${number}${chineseUnit}`;
};

// 生成CProm实例详情页面链接
const getCPromInstanceDetailUrl = (instanceId: string): string => {
  return `https://console.bce.baidu.com/cprom/#/instance/detail?id=${instanceId}`;
};

const ServiceStatus: React.FC<ServiceStatusProps> = ({ cpromInstanceId, region, onRetentionPeriodChange }) => {
  const [instanceInfo, setInstanceInfo] = useState<CPromInstanceInfo | null>(null);

  // 获取CProm实例详情
  const { loading: detailLoading, run: fetchInstanceDetail } = useRequest(
    () => getCPromInstanceDetail(cpromInstanceId!, region),
    {
      manual: true,
      onSuccess: (res) => {
        console.log('获取CProm实例详情成功:', res);
        if (res?.success && res.result) {
          const detail = res.result;
          const rawStatus = detail.status?.phase || 'Unknown';
          const rawRetentionPeriod = detail.spec?.vmClusterConfig?.retentionPeriod || 'Unknown';

          setInstanceInfo({
            instanceId: detail.spec?.instanceID || cpromInstanceId!,
            instanceStatus: STATUS_MAP[rawStatus] || rawStatus, // 应用状态映射
            retentionPeriod: formatRetentionPeriod(rawRetentionPeriod) // 格式化存储时长
          });

          // 通知父组件存储时长变化
          if (onRetentionPeriodChange) {
            onRetentionPeriodChange(rawRetentionPeriod);
          }
        }
      },
      onError: (error) => {
        console.error('获取CProm实例详情失败:', error);
        // 设置默认信息
        setInstanceInfo({
          instanceId: cpromInstanceId!,
          instanceStatus: STATUS_MAP['Unknown'], // 应用状态映射
          retentionPeriod: formatRetentionPeriod('Unknown') // 格式化存储时长
        });
      }
    }
  );

  // 鼠标移入时获取实例详情
  const handleMouseEnter = () => {
    if (cpromInstanceId && !instanceInfo && !detailLoading) {
      fetchInstanceDetail();
    }
  };

  // 渲染工具提示内容
  const renderTooltipContent = () => {
    if (detailLoading) {
      return <div>加载中...</div>;
    }

    if (!instanceInfo) {
      return <div>暂无实例信息</div>;
    }

    return (
      <div className={styles.tooltipContent}>
        <div className={styles.tooltipItem}>
          <span className={styles.tooltipLabel}>实例ID:</span>
          <a
            href={getCPromInstanceDetailUrl(instanceInfo.instanceId)}
            target="_blank"
            rel="noopener noreferrer"
            onClick={(e) => e.stopPropagation()} // 防止事件冒泡
          >
            {instanceInfo.instanceId}
          </a>
        </div>
        <div className={styles.tooltipItem}>
          <span className={styles.tooltipLabel}>实例状态:</span>
          <span className={styles.tooltipValue}>{instanceInfo.instanceStatus}</span>
        </div>
        <div className={styles.tooltipItem}>
          <span className={styles.tooltipLabel}>存储时长:</span>
          <span className={styles.tooltipValue}>{instanceInfo.retentionPeriod}</span>
        </div>
      </div>
    );
  };

  // 如果没有CProm实例ID，不显示服务状态
  if (!cpromInstanceId) {
    return null;
  }

  return (
    <Tooltip
      title={renderTooltipContent()}
      placement="bottom"
      trigger="hover"
      overlayClassName={styles.serviceStatusTooltip}
    >
      <span 
        className={styles.serviceStatus}
        onMouseEnter={handleMouseEnter}
      >
        服务状态
      </span>
    </Tooltip>
  );
};

export default ServiceStatus;
