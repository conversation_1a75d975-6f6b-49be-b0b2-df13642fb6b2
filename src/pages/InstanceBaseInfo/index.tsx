import React, {useEffect, useState} from 'react';
import {useFrameworkContext, useRegion} from '@baidu/bce-react-toolkit';
import {Col, Row, Table, Tag, Popover, Input, Button, Space, Modal, Switch, Form, toast, Checkbox, Tooltip} from 'acud';
import {OutlinedEditingSquare} from 'acud-icon';

import CopyDataWhenHover from '@/components/CopyDataWhenHover';
import {RegistractionStatusDict} from '@/utils/enums';
import {formatTime} from '@/utils/utils';
import {updateInstance} from '@/apis/instance';

import styles from './index.module.less';
import ChangeEsg from './registrationChangeEsg';

//统一设置这个页面中的Edit图标大小
OutlinedEditingSquare.defaultProps = {
  width: 16,
  style: {
    marginLeft: 8,
    color: '#2468F2',
  },
};


// 将状态字符串映射为数字状态码
const mapStatusToCode = (status) => {
  const statusMap = {
    'running': 2, // 运行中
    'creating': 1, // 创建中
    'initializing': 0, // 初始化
    'adjusting': 3, // 调整中
    'releasing': 4, // 释放中
    'error': 5, // 运行异常
    'failed': 6  // 创建失败
  };
  return statusMap[status] || 0;
};

// 网关规格映射函数
const mapGatewayType = (type) => {
  const typeMap = {
    small: '小型',
  };
  return typeMap[type] || type || '-';
};

const BaseInfo = (props) => {
  const {detail, requestDetail} = props;
  const {frameworkData} = useFrameworkContext();
  const {region} = useRegion();

  // 编辑相关状态
  const [nameEditing, setNameEditing] = useState(false);
  const [descEditing, setDescEditing] = useState(false);
  const [showPublicAccessModal, setShowPublicAccessModal] = useState(false);
  const [showDeleteProtectionModal, setShowDeleteProtectionModal] = useState(false);

  // 表单状态
  const [nameInput, setNameInput] = useState('');
  const [nameError, setNameError] = useState('');
  const [descInput, setDescInput] = useState('');
  const [descError, setDescError] = useState('');
  const [publicAccessible, setPublicAccessible] = useState(false);
  const [deleteProtection, setDeleteProtection] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  // 初始化表单值
  useEffect(() => {
    if (detail?.data) {
      setNameInput(detail.data.name || '');
      setDescInput(detail.data.description || '');
      setPublicAccessible(detail.data.publicAccessible || false);
      setDeleteProtection(detail.data.deleteProtection || false);
    }
  }, [detail]);

  // 验证实例名称
  const validateName = (value) => {
    if (!value.trim()) {
      setNameError('实例名称不能为空');
      return false;
    }

    if (value.length < 1 || value.length > 64) {
      setNameError('实例名称长度为1-64个字符');
      return false;
    }

    // 检查是否以字母或中文开头
    if (!/^[a-zA-Z\u4e00-\u9fa5]/.test(value)) {
      setNameError('实例名称必须以字母或中文开头');
      return false;
    }

    // 检查是否只包含合法字符(支持大小写字母、数字、中文以及-_/.特殊字符)
    if (!/^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\-_\/\.]*$/.test(value)) {
      setNameError('实例名称只能包含字母、数字、中文以及-_/.特殊字符');
      return false;
    }

    setNameError('');
    return true;
  };

  // 验证描述
  const validateDesc = (value) => {
    if (value && value.length > 64) {
      setDescError('描述长度不能超过64个字符');
      return false;
    }
    setDescError('');
    return true;
  };

  // 更新实例信息
  const updateInstanceInfo = async (field, value) => {
    console.log(`更新实例${field}:`, value);
    setSubmitLoading(true);

    try {
      const instanceId = detail?.data?.instanceId;
      if (!instanceId) {
        throw new Error('实例ID不存在');
      }

      // 创建更新数据对象，保留原有值，只更新指定的字段
      // 这样可以避免在只更新一个字段时导致其他字段被重置
      const updateData = {
        name: field === 'name' ? value : detail?.data?.name,
        description: field === 'description' ? value : detail?.data?.description,
        deleteProtection: field === 'deleteProtection' ? value : detail?.data?.deleteProtection,
        publicAccessible: field === 'publicAccessible' ? value : detail?.data?.publicAccessible
      };

      // 移除undefined的字段
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      console.log('完整更新数据:', updateData);
      
      const response = await updateInstance(instanceId, updateData, region);
      console.log('更新实例返回结果:', response);

      if (response?.success) {
        toast.success({
          message: '更新成功',
          duration: 3
        });
        
        // 重新获取实例详情
        requestDetail();
      } else {
        toast.error({
          message: (response as any)?.message || '更新失败，请重试',
          duration: 3
        });
      }
    } catch (error) {
      console.error('更新实例出错:', error);
      toast.error({
        message: '系统错误，请重试',
        duration: 3
      });
    } finally {
      setSubmitLoading(false);
      // 关闭所有编辑状态
      setNameEditing(false);
      setDescEditing(false);
      setShowPublicAccessModal(false);
      setShowDeleteProtectionModal(false);
    }
  };

  // 实例名称编辑内容
  const nameEditContent = (
    <div className={styles['edit-popover']}>
      <Input
        placeholder="请输入实例名称"
        value={nameInput}
        onChange={(e) => {
          setNameInput(e.target.value);
          validateName(e.target.value);
        }}
        style={{ width: '240px' }}
      />
      {nameError && <div className={styles['error-text']}>{nameError}</div>}
      <div className={styles['tip-text']}>
        长度为1-64个字符，以字母或中文开头，可包含数字、中文以及-_/.特殊字符
      </div>
      <div className={styles['action-buttons']}>
        <Button
          onClick={() => {
            setNameEditing(false);
            setNameInput(detail?.data?.name || '');
            setNameError('');
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          loading={submitLoading}
          disabled={!!nameError || nameInput === detail?.data?.name}
          onClick={() => {
            if (validateName(nameInput)) {
              updateInstanceInfo('name', nameInput);
            }
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );

  // 描述编辑内容
  const descEditContent = (
    <div className={styles['edit-popover']}>
      <Input.TextArea
        placeholder="请输入描述"
        value={descInput}
        onChange={(e) => {
          setDescInput(e.target.value);
          validateDesc(e.target.value);
        }}
        style={{ width: '240px' }}
        autoSize={{ minRows: 2, maxRows: 6 }}
      />
      {descError && <div className={styles['error-text']}>{descError}</div>}
      <div className={styles['tip-text']}>
        长度为0-64个字符
      </div>
      <div className={styles['action-buttons']}>
        <Button
          onClick={() => {
            setDescEditing(false);
            setDescInput(detail?.data?.description || '');
            setDescError('');
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          loading={submitLoading}
          disabled={!!descError || descInput === detail?.data?.description}
          onClick={() => {
            if (validateDesc(descInput)) {
              updateInstanceInfo('description', descInput);
            }
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );

  // 公网访问编辑模态框
  const publicAccessModal = (
    <Modal width={640}  className={styles['instance-detail-edit-modal']}
      title="修改公网访问"
      visible={showPublicAccessModal}
      onCancel={() => setShowPublicAccessModal(false)}
      onOk={() => {
        updateInstanceInfo('publicAccessible', publicAccessible);
      }}
      confirmLoading={submitLoading}
    >
      <Form layout="horizontal" labelCol={{span: 4}} labelAlign="left">
        <Form.Item label="网关名称/ID"  style={{marginBottom:'16px'}}>
          <div>{detail?.data?.name || '-'} ({detail?.data?.instanceId || '-'})</div>
        </Form.Item>
        <Form.Item label="公网访问" className={styles['last-form-item']}
        extra={<div style={{whiteSpace: 'nowrap',marginTop:'-4px'}}>开启公网访问，会自动为网关创建公网接入地址，若不开启则仅支持私有网络访问</div>}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={publicAccessible}
              onChange={(e) => setPublicAccessible(e.target.checked)}
            />
            <span style={{ marginLeft: 8 }}>开启公网</span>
          </div>
         
        </Form.Item>
      </Form>
    </Modal>
  );

  // 删除保护编辑模态框
  const deleteProtectionModal = (
    <Modal className={styles['instance-detail-edit-modal']}
      title="修改删除保护"
      visible={showDeleteProtectionModal}
      onCancel={() => setShowDeleteProtectionModal(false)}
      onOk={() => {
        updateInstanceInfo('deleteProtection', deleteProtection);
      }}
      confirmLoading={submitLoading}
      width={520}
    >
      <Form layout="horizontal" labelCol={{span: 4}} labelAlign="left">
        <Form.Item label="网关名称/ID" className={styles['form-item']}>
          <div>{detail?.data?.name || '-'} ({detail?.data?.instanceId || '-'})</div>
        </Form.Item>
        <Form.Item label="删除保护"  className={styles['last-form-item']}
          extra={<div style={{whiteSpace: 'nowrap',marginTop:'-4px'}}>开启后，可防止通过控制台或 API 误删除网关</div>}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={deleteProtection}
              onChange={(e) => setDeleteProtection(e.target.checked)}
            />
            <span style={{ marginLeft: 8 }}>开启保护</span>
            <Tag type="enhance" style={{marginLeft: 8, color: '#fff', backgroundColor: '#F33E3E',padding:'0 4px'}}>推荐开启</Tag>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );

  const getServerPort = (data: any) => {
    // 将字符串转换为数组
    const ports = data?.serverPort?.split(',');
    const protocols = data?.serverProtocol?.split(',');
    if (!ports || !protocols) {
      return '-';
    }
    
    // 检查数据格式
    // console.log('服务端口数据:', {ports, protocols});
    
    // 生成所需的格式
    return ports
      .map((port: string, index: number) => {
        const protocol = protocols[index] || '';
        return `${port} (${protocol})`;
      })
      .join(' 、 ');
  };

  const getColumns = (instanceId: string) => {
    return [
      // {
      //   title: '私有网络',
      //   key: 'vpcId',
      //   dataIndex: 'vpcId',
      //   render(value: string, record: any) {
      //     return (
      //       <a
      //         href="https://console.bce.baidu.com/network/#/vpc/instance/list"
      //         target="_blank"
      //         rel="noreferrer"
      //       >{`${record.vpcName || '-'}(${value})`}</a>
      //     );
      //   }
      // },
      // {
      //   title: '子网',
      //   key: 'subnetId',
      //   dataIndex: 'subnetId',
      //   render(value: string, record: any) {
      //     return (
      //       <a
      //         href={`https://console.bce.baidu.com/network/#/vpc/subnet/list`}
      //         target="_blank"
      //         rel="noreferrer"
      //       >{`${record.subnetName || '-'}(${value})`}</a>
      //     );
      //   }
      // },
      {
        title: 'BLB名称/ID',
        key: 'BLBName',
        dataIndex: 'BLBName'
      },
      {
        title: '公网地址',
        key: 'externalIP',
        dataIndex: 'externalIP'
      },
      {
        title: '内网地址',
        key: 'internalIP',
        dataIndex: 'internalIP'
      },
      {
        title: '监听协议/端口',
        key: 'protocol',
        dataIndex: 'protocol'
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status'
      },
      {
        title: '创建时间',
        key: 'createTime',
        dataIndex: 'createTime'
      },
      // {
      //   title: '访问控制',
      //   render(_: any, record: any) {
      //     const {enterpriseSecurityGroupName, enterpriseSecurityGroupId, id} =
      //       record;
      //     return (
      //       <>
      //         {enterpriseSecurityGroupId && (
      //           <a
      //             href={`https://console.bce.baidu.com/network/#/vpc/enterpriseSecurity/detail?id=${enterpriseSecurityGroupId}`}
      //             target="_blank"
      //             rel="noreferrer"
      //           >{`${enterpriseSecurityGroupName || '-'}(${enterpriseSecurityGroupId || '-'})`}</a>
      //         )}
      //         <ChangeEsg
      //           esgId={enterpriseSecurityGroupId}
      //           id={id}
      //           instanceId={instanceId}
      //           requestDetail={requestDetail}
      //         />
      //       </>
      //     );
      //   }
      // }
    ];
  };

  return (
    <div>
      {publicAccessModal}
      {deleteProtectionModal}
      <div className={styles['detail-base-card']}>
        <div className={styles['detail-base-card-title']}>基本信息</div>
        <div className={styles['detail-base-card-content']}>
          <Row gutter={[48, 16]}>
          {/* ACUD使用的是 24 栅格系统，将页面宽度等分为 24 份 */}
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>
                  实例名称：
                </div>
                <div className={styles['detail-base-card-value']}>
                  <Tooltip title={detail?.data?.name || '-'}>
                    <span
                      style={{
                        display: 'inline-block',
                        maxWidth: 240, // 视实际宽度调整
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        verticalAlign: 'middle',
                      }}
                    >
                      {detail?.data?.name || '-'}
                    </span>
                  </Tooltip>
                  <Popover
                    content={nameEditContent}
                    // title="编辑实例名称"
                    trigger="click"
                    visible={nameEditing}
                    onVisibleChange={setNameEditing}
                    placement="rightTop"
                  >
                    <OutlinedEditingSquare
                    
                      onClick={() => setNameEditing(true)}
                    />
                  </Popover>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>实例ID：</div>
                <div className={styles['detail-base-card-value']}>
                  <CopyDataWhenHover copyValue={detail?.data?.instanceId || ''} />
               
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>地域：</div>
                <div className={styles['detail-base-card-value']}>
                {frameworkData?.constants?.REGION[detail?.data?.region] || '-'}</div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>状态：</div>
                <div className={styles['detail-base-card-value']}>
                  <Tag
                    color="transparent"
                    className="table-status"
                    style={{paddingLeft: 0}}
                    icon={
                      <span
                        className={
                          RegistractionStatusDict.get(mapStatusToCode(detail?.data?.ingressStatus))?.iconClass
                        }
                      />
                    }
                  >
                    {RegistractionStatusDict.get(mapStatusToCode(detail?.data?.ingressStatus))?.text || '-'}
                  </Tag>
                </div>
              </div>
            </Col>

            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>
                  公网访问：
                </div>
                <div className={styles['detail-base-card-value']}>
                  {detail?.data?.publicAccessible ? '已开启' : '未开启'}
                  <OutlinedEditingSquare
                    onClick={() => {
                      setPublicAccessible(detail?.data?.publicAccessible || false);
                      setShowPublicAccessModal(true);
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>描述：</div>
                <div className={styles['detail-base-card-value']}>
                  <Tooltip title={detail?.data?.description || '-'}>
                    <span
                      style={{
                        display: 'inline-block',
                        maxWidth: 240, // 视实际宽度调整
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        verticalAlign: 'middle',
                      }}
                    >
                      {detail?.data?.description || '-'}
                    </span>
                  </Tooltip>
                  <Popover
                    content={descEditContent}
                    // title="编辑描述"
                    trigger="click"
                    visible={descEditing}
                    onVisibleChange={setDescEditing}
                    placement="rightTop"
                  >
                    <OutlinedEditingSquare
                      onClick={() => setDescEditing(true)}
                    />
                  </Popover>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>创建时间：</div>
                <div className={styles['detail-base-card-value']}>
                  {detail?.data?.createTime || '-'}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>私有网络：</div>
                <div className={styles['detail-base-card-value']}>
                {detail?.data?.vpcId ? (
                        <a
                          href={`https://console.bce.baidu.com/network/#/vpc/instance/detail?vpcId=${detail.data.vpcId}`}
                          target="_blank"
                          rel="noreferrer"
                          style={{ color: '#2468f2', cursor: 'pointer' }}
                        >
                          {detail.data.vpcId}
                        </a>
                      ) : (
                        '-'
                      )}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>子网：</div>
                <div className={styles['detail-base-card-value']}>
                {detail?.data?.subnetId ? (
                        <a
                          href={`https://console.bce.baidu.com/network/#/vpc/subnet/detail?subnetId=${detail.data.subnetId}`}
                          target="_blank"
                          rel="noreferrer"
                          style={{ color: '#2468f2', cursor: 'pointer' }}
                        >
                          {detail.data.subnetId}
                        </a>
                      ) : (
                        '-'
                      )}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>网关规格：</div>
                <div className={styles['detail-base-card-value']}>
                  {mapGatewayType(detail?.data?.gatewayType)}
                </div>
              </div>
            </Col>
            {/* <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>节点数量：</div>
                <div className={styles['detail-base-card-value']}>
                  {detail?.data?.replicas || '-'}
                </div>
              </div>
            </Col> */}
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>删除保护：</div>
                <div className={styles['detail-base-card-value']}>
                  {detail?.data?.deleteProtection ? '已开启' : '未开启'}
                  <OutlinedEditingSquare
                    onClick={() => {
                      setDeleteProtection(detail?.data?.deleteProtection || false);
                      setShowDeleteProtectionModal(true);
                    }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
      {/* <div className={styles['detail-base-card-line-wrap']}>
        <div className={styles['detail-base-card-line']}></div>
      </div> */}
      <div className={styles['detail-base-card']}>
        <div className={styles['detail-base-card-title']}>网关入口</div>
        <div className={styles['detail-base-card-content']}>
          <Row gutter={[48, 16]}>
            {/* 内网地址，一行是24 */}
            <Col span={8}>  
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>
                  内网地址：
                </div>
                <div className={styles['detail-base-card-value']}>
                  {detail?.data?.loadBalanceInfo?.privateAddress || detail?.data?.internalIP || '-'}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles['detail-base-card-item']}>
                <div className={styles['detail-base-card-label']}>
                  公网地址：
                </div>
                <div className={styles['detail-base-card-value']}>
                  {detail?.data?.loadBalanceInfo?.publicAddress || detail?.data?.externalIP || '-'}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    
      {/* <div className={styles['detail-base-card']}>
        <div className={styles['detail-base-card-title']}>网关入口</div>
        <div className={styles['detail-base-card-content']}>
          <Table
            dataSource={detail.loadBalanceList || []}
            columns={getColumns(detail.id)}
            pagination={false}
            size="small"
            style={{ width: '100%'}}
          />
        </div>
      </div> */}
    </div>
  );
};

export default BaseInfo;
