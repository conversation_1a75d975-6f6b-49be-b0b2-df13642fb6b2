# 消费者列表&创建消费者

1. 现状描述：目前在实例详情页面中左侧有 3 个导航栏（实例详情、推理服务、路由配置），点击每个导航栏右侧会切换成当前导航栏下对应的页面。

2. 需求描述：现在，我期望在这 3 个导航栏最下方新增一个导航栏【消费者管理】，用于展示当前实例中的所有消费者以及相关的属性，接下来在第 3 点页面交互中我会描述选中消费者管理这个导航栏后右侧页面的样式和交互，你需要在遵循相关规范的前提下生成这个页面，另外，这个页面中所有字段和字段值来源于查询消费者列表这个接口，接口文档请参考查询消费者列表接口.md，你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数生成这个页面并和接口对应上。

3. 页面交互与样式：消费者管理页面标题为消费者管理，你可以参考路由配置这个页面。在这个页面下方有 1. 创建消费者=按钮= 2. 刷新=按钮=，创建消费者按钮靠页面左侧，刷新按钮靠页面右侧，这个布局可以参考路由配置页面。在创建按钮右侧有一行文案：已创建 X 个，共可创建 200 个。文案和创建按钮垂直水平居中，颜色为 #84868C ；X需要根据查询消费者列表接口返回的 totalCount 回显。点击刷新按钮会重新调用查询消费者列表接口，在这两个元素下方有一个=列表=，用于展示当前实例中的所有消费者，列表包含 4 列（消费者名称/ID、描述、创建时间、操作），列表支持=分页=，默认 10 条/页。其中消费者名称/ID 这一列中消费者名称和 ID 需要分成 2 行展示，操作列中包含 2 个操作：编辑、删除。消费者名称/ID、描述、创建时间这 3 个参数来自于查询消费者列表接口中的返回字段，所以，在刚开始进入消费者列表页面的时候就需要调用查询消费者列表这个接口用于回显字段值，同时，当用户点击刷新按钮的时候也需要重新调用查询消费者列表。另外，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。因为现在只有点击实例名称才会进入实例详情页，才会包含消费者列表这个导航栏。

    1. 点击创建消费者按钮后，页面中间出现一个弹窗，弹窗的标题为“创建消费者”，弹窗中为=表单=，包含 4 个字段（消费者名称/描述/认证方式/授权路由范围）。这 4 个字段靠左对齐，且宽度一致。其中，消费者名称右侧为输入框，描述右侧为输入框，认证方式右侧为 button 样式的单选，目前仅包含 Key Auth 一个选项，且默认选中。授权路由范围右侧为一个 Transfer 穿梭选择框，左侧多选组件中展示当前实例中所有路由，这里需要根据实例 ID 请求查询实例中路由列表的接口，具体接口调用我会在第 4 点中说明，穿梭选择框中支持全选。消费者名称为必填，仅支持大小写字母、数字以及-*特殊字符，长度限制 2-64 个字符，且在输入框下方有一行文字："仅支持大小写字母、数字以及-*特殊字符，长度限制 2-64 个字符"。如果消费者名称不合法，需要在输入框下方报错原因，同时在穿梭选择框中路由名称右侧需要有一个标签，用于回显当前路由是否开启认证，你需要调用查询路由详情这个接口，判断当前路由是否开启消费者认证，如果未开启消费者认证，则路由名称右侧展示一个标签："未开启认证"。

4. 页面与接口对应关系：用户点击创建消费者按钮后在页面中间出现一个弹窗，包含 4 个字段（消费者名称/描述/认证方式/授权路由范围），点击创建按钮后首先需要调用【查询路由列表】这个接口，用于在穿梭选择框左侧的多选组件中回显所有的路由名称，另外，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。还需要根据路由详情接口中是否开启消费者认证来判断是否展示"未开启认证"标签，当用户在表单中输入了相关信息后，点击弹窗右下角的确定按钮后，会根据当前弹窗中表单中的相关参数去请求【创建消费者】接口，如果成功就先返回消费者列表页面，并重新请求【查询消费者列表】这个接口，并在右上角提示消费者创建成功。如果失败需要在右上角 toast 提示接口返回的失败原因，不需要关闭创建消费者这个弹窗。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：参见附件

7. 遵守原则：尽量不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你生成消费者列表这个页面，所以你不应该再自动为我完善其他的交互页面。

# 编辑消费者

1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、路由配置、消费者管理），点击每个导航栏后右侧会切换成当前导航栏下对应的页面。当前点击消费者管理导航栏右侧会出现 1 个消费者列表，在消费者列表页面中有一个列表用于展示当前实例中所有消费者信息，消费者列表中操作列包含编辑操作和删除操作。

2. 需求描述：现在我期望当用户点击消费者列表中消费者操作列中的编辑操作后当前页面出现一个弹窗，这个弹窗整体和创建消费者的弹窗的布局、样式均保持一致，唯一不一致的是需要先调用查询消费者详情这个接口，查询后根据接口返回的字段预先填充相关信息，如果某个字段值为空则不需要填充，其中消费者名称、认证方式、认证信息无法编辑，因此仅作为静态回显，其他字段均可编辑。这个弹窗的标题为消费者编辑。我会在第 3 点中详细描述编辑消费者这个弹窗的交互与样式，你需要在遵循相关规范的前提下生成这个页面。另外，【编辑消费者】这个弹窗中涉及的所有接口请求、返回需要和编辑消费者接口文档中涉及的参数完全符合。

    1. 这个页面中可能会涉及到 3 个接口：1. 查询实例详情 2. 编辑消费者 3. 查询消费者详情 （接口文档在接口文档文件夹中）。我会在第 4 点中描述这 3 个接口和页面的对应关系，你需要在遵循相关规范的前提下修改编辑消费者这个弹窗中涉及模块相关的参数和字段。你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。

3. 页面交互与样式：编辑消费者弹窗整体布局、样式、交互逻辑方式和创建消费者弹窗保持一致。你可以直接复用创建消费者弹窗的代码，在这个基础上进行修改。其中编辑消费者也是包含 4 个字段（消费者名称/描述/认证方式/认证信息/授权路由范围，消费者名称右侧为静态回显，描述右侧为输入框，需要根据消费者详情返回的信息进行预填充，认证方式右侧为静态回显，认证信息右侧为静态回显，支持鼠标移入认证信息字段值后出现 1 个复制图标快速复制。授权路由范围的样式、交互和创建消费者抽屉中的授权路由范围保持一致，同样需要先根据查询消费者详情接口返回的字段值进行预填充勾选。

4. 页面与接口对应关系：当用户点击消费者列表中操作列中的编辑操作后，在页面出现编辑消费者弹窗，需要根据实例 ID 和当前消费者 ID 调用查询消费者详情这个接口，然后根据接口返回值对相关的字段值进行预填充，其中消费者名称、认证方式、认证信息不可编辑，因此仅作静态回显，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。点击弹窗右下角的确定按钮后调用编辑消费者这个接口，更新成功则需要关闭弹窗且在页面右上角 toast 提示“消费者 {消费者名称} 更新成功“，返回消费者列表并且刷新消费者列表，更新失败则弹窗不需要关闭，在页面右上角 toast 提示错误原因。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：1. 查询消费者详情 2. 编辑消费者 3. 查询消费者列表 4. 查询实例详情（参见附件）

7. 遵守原则：所有字段以接口文档为准，不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改编辑消费者这个模块中各模块涉及的相关接口、字段，所以你不应该再自动为我完善其他页面的交互和接口。

# 创建消费者

1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、路由配置、消费者列表），点击每个导航栏右侧会切换成当前导航栏下对应的页面，现在点击消费者列表导航栏页面右侧包含一个标签页消费者列表，在这个标签页中有一个创建消费者按钮。

2. 需求描述：现在，我期望点击创建消费者按钮后，在页面中间出现一个=弹窗=，可以执行创建消费者的操作，用户需要输入一些字段后请求后端去执行创建消费者的操作，接下来在第 3 点页面交互中我会描述点击创建消费者这个按钮后的交式和样式，你需要在遵循相关规范的前提下生成相关模块，另外，这个抽屉中字段需要和创建消费者这个接口的请求参数对应上，接口文档请参考接口文档文件夹中创建消费者. md，你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数生成这个抽屉中的字段并和接口对应上。

3. 页面交互与样式：点击创建消费者按钮后，页面中间出现一个抽屉，抽屉的标题为“创建消费者”，抽屉中为=表单=，整体分成 2 个模块（消费者信息/消费者授权），其中消费者信息包含 3 个字段（消费者名称/描述/认证方式），消费者授权模块中包含 1 个字段（授权权路由范围）。其中，消费者名称右侧为输入框，描述右侧为输入框，认证方式右侧为 button 样式的单选，目前仅包含 JWT 一个选项，且默认选中。授权路由范围右侧首先有一行提示文案：“可选择当前网关实例中多条路由，是否生效取决于路由本身是否开启消费者认证“。提示文案下方有一个 Transfer 穿梭选择框，左侧多选组件中展示当前实例中所有路由，这里需要根据实例 ID 请求查询实例中路由列表的接口，具体接口调用我会在第 4 点中说明，穿梭选择框中支持全选。

4. 页面与接口的对应关系：

    1. 用户点击创建消费者按钮后在页面右侧出现一个抽屉，包含 2 个模块（消费者信息/消费者授权），其中消费者授权模块中包含一个字段（授权权路由范围），字段右侧首先为一行提示文案，提示文案下方为一个穿梭选择框，需要调用【查询路由列表】这个接口，用于在穿梭选择框左侧的多选组件中回显所有的路由（路由名称），另外，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。当用户在表单中输入了相关信息后，点击抽屉右下角的确定按钮后，会根据当前抽屉中表单中的相关参数去请求【创建消费者】接口，如果成功就先返回消费者列表标签页面，并重新请求【查询消费者列表】这个接口，并在右上角提示消费者创建成功。如果失败需要在右上角 toast 提示接口返回的失败原因，不需要关闭创建消费者这个抽屉。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：参见附件

7. 遵守原则：尽量不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你生成创建消费者这个抽屉里的交互页面，所以你不应该再自动为我完善编辑消费者的交互页面。

# 查询消费者详情

1. 现状描述：目前在实例详情页面中左侧有 4 个导航栏（实例详情、推理服务、路由配置、消费者管理），点击每个导航栏后右侧会切换成当前导航栏下对应的页面。消费者管理页面中有一个列表用于展示当前实例中所有消费者信息。

2. 需求描述：现在我期望当用户点击消费者列表中消费者名称后在当前页面右侧出现一个抽屉，这个抽屉的标题为消费者详情。我会在第 3 点中详细描述消费者详情这个抽屉的交互与样式，你需要在遵循相关规范的前提下生成这个页面。另外，【消费者详情】这个抽屉中涉及的所有接口请求、返回需要和查询消费者详情接口文档中涉及的参数完全符合。

    1. 这个页面中会涉及到 1 个接口：1. 查询消费者详情（接口文档在接口文档文件夹中）。我会在第 4 点中描述这个接口和页面的对应关系，你需要在遵循相关规范的前提下修改消费者详情这个抽屉中涉及模块相关的参数和字段。你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数进行修改并和接口对应上，尽可能进行详细检查，以减少重试次数。

3. 页面交互与样式：其中消费者详情包含 5 个字段（消费者名称/描述/认证方式/认证信息/已授权路由），已授权路由中为 1 个=列表=，总共包含 3 列（路由名称/创建时间/消费者认证），用于回显当前消费者已被授权的路由列表信息，路由名称、创建时间、消费者认证均来自于查询消费者详情这个接口中返回的字段值。

4. 页面与接口对应关系：当用户点击消费者列表中消费者的名称后，在页面右侧出现消费者详情抽屉，需要根据实例 ID 和当前消费者 ID 调用查询消费者详情这个接口，然后根据接口返回值对相关的字段值进行回显，这个接口可能需要实例 ID，在当前路由路径中你应该可以查到。

5. 可维护性：尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可，另外，在你认为比较重要的操作节点时使用 console. log 输出关键信息以方便后期前后端联调。

6. 接口文档：1. 查询消费者详情 （参见附件）

7. 遵守原则：所有字段以接口文档为准，不要自动生成我没有提到的交互逻辑，比如在本次 prompt 中我只希望你修改消费者详情这个模块中各模块涉及的相关接口、字段，所以你不应该再自动为我完善其他页面的交互和接口。
8. 另外，点击操作列中的删除按钮会出现一个 Popover 气泡二次确认是否需要删除，点击确定后调用删除消费者接口，成功执行则刷新列表。
